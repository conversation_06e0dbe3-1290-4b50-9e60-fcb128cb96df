import 'package:flutter/material.dart';

class PrivacyPolicyScreen extends StatelessWidget {
  const PrivacyPolicyScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Privacy Policy'),
        backgroundColor: Theme.of(context).primaryColor,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Privacy Policy',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Last Updated: ${DateTime.now().toString().substring(0, 10)}',
              style: const TextStyle(
                color: Colors.grey,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 24),
            _buildSection(
              title: '1. Introduction',
              content:
                  'This Privacy Policy explains how the Cattle Manager App ("we", "our", or "us") collects, uses, and discloses your information when you use our mobile application (the "App").',
            ),
            _buildSection(
              title: '2. Information We Collect',
              content:
                  'We collect information that you provide directly to us, such as when you create an account, update your profile, use the features in the App, or communicate with us. This information may include your name, email address, phone number, farm details, and cattle information.\n\nThe App also collects data about your cattle, including breeding records, health records, milk production, and financial transactions.',
            ),
            _buildSection(
              title: '3. How We Use Your Information',
              content:
                  'We use the information we collect to:\n• Provide, maintain, and improve the App\n• Process and complete transactions\n• Send you technical notices and support messages\n• Respond to your comments, questions, and requests\n• Monitor and analyze trends, usage, and activities in connection with the App\n• Personalize and improve the App and provide content or features that match user profiles or interests',
            ),
            _buildSection(
              title: '4. Data Storage',
              content:
                  'Currently, all data is stored locally on your device. In future versions, we plan to implement cloud storage with Firebase authentication for a multi-user system with different roles (Admin, Manager, Staff, Veterinarian, Consultant).\n\nWhen cloud storage is implemented, we will update this Privacy Policy with additional details about data transmission, storage, and security measures.',
            ),
            _buildSection(
              title: '5. Data Security',
              content:
                  'We take reasonable measures to help protect your personal information from loss, theft, misuse, unauthorized access, disclosure, alteration, and destruction. However, no security system is impenetrable, and we cannot guarantee the security of our systems.',
            ),
            _buildSection(
              title: '6. Your Rights',
              content:
                  'You have the right to access, update, or delete your personal information at any time. You can do this directly through the App settings or by contacting us.',
            ),
            _buildSection(
              title: '7. Changes to This Privacy Policy',
              content:
                  'We may update this Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page and updating the "Last Updated" date.',
            ),
            _buildSection(
              title: '8. Children\'s Privacy',
              content:
                  'The App is not intended for children under the age of 13. We do not knowingly collect personal information from children under 13.',
            ),
            _buildSection(
              title: '9. Third-Party Services',
              content:
                  'The App may contain links to third-party websites or services. We are not responsible for the practices of such third parties. We encourage you to read the privacy policies of any third-party websites or services that you visit.',
            ),
            _buildSection(
              title: '10. Contact Us',
              content:
                  'If you have any questions about this Privacy Policy, please contact us at:',
            ),
            const Padding(
              padding: EdgeInsets.only(left: 16.0, bottom: 24.0),
              child: Text(
                '<EMAIL>',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.blue,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection({required String title, required String content}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: const TextStyle(
              fontSize: 16,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }
}
