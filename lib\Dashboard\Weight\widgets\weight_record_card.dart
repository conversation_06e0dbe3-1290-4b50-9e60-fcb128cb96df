import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/weight_record_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../../constants/app_colors.dart';

class WeightRecordCard extends StatelessWidget {
  final WeightRecordIsar record;
  final CattleIsar? cattle;
  final bool isSelected;
  final bool isSelectionMode;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final ValueChanged<bool?>? onSelectionChanged;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final bool compact;

  const WeightRecordCard({
    Key? key,
    required this.record,
    this.cattle,
    this.isSelected = false,
    this.isSelectionMode = false,
    this.onTap,
    this.onLongPress,
    this.onSelectionChanged,
    this.onEdit,
    this.onDelete,
    this.compact = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final weightStatusColor = _getWeightStatusColor();
    final methodColor = _getMethodColor();
    final dateColor = _getDateColor();
    final cattleColor = _getCattleColor();
    final bcsColor = _getBcsColor();
    final notesColor = _getNotesColor();
    final methodIcon = _getMethodIcon();
    final cattleName = cattle?.name ?? 'Unknown Cattle';
    final tagId = cattle?.tagId ?? '';
    final displayName = (tagId.isNotEmpty) ? '$cattleName ($tagId)' : cattleName;
    final formattedWeight = '${record.weight.toStringAsFixed(1)} kg';
    final formattedDate = record.measurementDate != null
        ? DateFormat('MMM dd, yyyy').format(record.measurementDate!)
        : 'No date';

    return Card(
      margin: compact
          ? const EdgeInsets.symmetric(horizontal: 8, vertical: 4)
          : const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 2,
      shadowColor: Colors.black26,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: const BorderSide(
          color: Color(0xFFEEEEEE),
          width: 1,
        ),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: isSelectionMode
            ? () => onSelectionChanged?.call(!isSelected)
            : onTap,
        onLongPress: onLongPress,
        child: Container(
          padding: compact
              ? const EdgeInsets.all(12)
              : const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Row 1: Date + Weight + Menu/Checkbox
              IntrinsicHeight(
                child: Row(
                  children: [
                    if (isSelectionMode)
                      SizedBox(
                        width: 56,
                        child: Checkbox(
                          value: isSelected,
                          onChanged: onSelectionChanged,
                          activeColor: AppColors.primary,
                        ),
                      ),
                    // Weight status indicator - 4px vertical stripe
                    Container(
                      width: 4,
                      height: 40,
                      decoration: BoxDecoration(
                        color: weightStatusColor,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    const SizedBox(width: 12),
                    // Date with icon - flexible width
                    Expanded(
                      child: Row(
                        children: [
                          Icon(
                            Icons.calendar_today_outlined,
                            size: 20,
                            color: dateColor,
                          ),
                          const SizedBox(width: 8),
                          Flexible(
                            child: Text(
                              formattedDate,
                              style: TextStyle(
                                color: dateColor,
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                              maxLines: 1,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 16),
                    // Weight - right aligned with minimum width
                    Container(
                      constraints: const BoxConstraints(minWidth: 80),
                      child: Text(
                        formattedWeight,
                        style: TextStyle(
                          color: weightStatusColor,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                        textAlign: TextAlign.right,
                        maxLines: 1,
                      ),
                    ),
                    if (!isSelectionMode && (onEdit != null || onDelete != null))
                      SizedBox(
                        width: 24,
                        child: PopupMenuButton<String>(
                          icon: const Icon(
                            Icons.more_vert,
                            color: Color(0xFF303F9F), // Different indigo shade
                          ),
                          onSelected: (String choice) {
                            if (choice == 'Edit' && onEdit != null) {
                              onEdit!();
                            } else if (choice == 'Delete' && onDelete != null) {
                              onDelete!();
                            }
                          },
                          itemBuilder: (BuildContext context) => [
                            if (onEdit != null)
                              const PopupMenuItem(
                                value: 'Edit',
                                child: Row(
                                  children: [
                                    Icon(Icons.edit_outlined),
                                    SizedBox(width: 8),
                                    Text('Edit'),
                                  ],
                                ),
                              ),
                            if (onDelete != null)
                              const PopupMenuItem(
                                value: 'Delete',
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.delete_outline,
                                      color: Colors.red,
                                    ),
                                    SizedBox(width: 8),
                                    Text(
                                      'Delete',
                                      style: TextStyle(color: Colors.red),
                                    ),
                                  ],
                                ),
                              ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
              const SizedBox(height: 8),
              // Row 2: Cattle + Method + Quality
              Row(
                children: [
                  if (isSelectionMode)
                    const SizedBox(width: 56),
                  const SizedBox(width: 16),
                  // Cattle with icon - flexible width
                  Expanded(
                    child: Row(
                      children: [
                        Icon(
                          Icons.pets,
                          color: cattleColor,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Flexible(
                          child: Text(
                            displayName,
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                              color: cattleColor,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),
                  // Method - right aligned with minimum width
                  Container(
                    constraints: const BoxConstraints(minWidth: 100),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          methodIcon,
                          size: 20,
                          color: methodColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _getMethodDisplayName(),
                          style: TextStyle(
                            color: methodColor,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                        ),
                      ],
                    ),
                  ),
                  if (!isSelectionMode && (onEdit != null || onDelete != null))
                    const SizedBox(width: 24),
                ],
              ),

              // Row 3: Body Condition Score (if available)
              if (record.bodyConditionScore != null) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    if (isSelectionMode)
                      const SizedBox(width: 56),
                    const SizedBox(width: 16),
                    // Body Condition Score
                    Expanded(
                      child: Row(
                        children: [
                          Icon(
                            Icons.fitness_center,
                            color: bcsColor,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'BCS: ${record.bodyConditionScore!.toStringAsFixed(1)}',
                            style: TextStyle(
                              color: bcsColor,
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (!isSelectionMode && (onEdit != null || onDelete != null))
                      const SizedBox(width: 24),
                  ],
                ),
              ],

              // Notes (if available)
              if (record.notes != null && record.notes!.isNotEmpty) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    if (isSelectionMode)
                      const SizedBox(width: 56),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        record.notes!,
                        style: TextStyle(
                          fontSize: 14,
                          color: notesColor,
                          fontStyle: FontStyle.italic,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (!isSelectionMode && (onEdit != null || onDelete != null))
                      const SizedBox(width: 24),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Color _getWeightStatusColor() {
    // Single color for all weight-related elements
    return const Color(0xFF1976D2); // Blue
  }

  Color _getMethodColor() {
    // Single color for all measurement methods
    return const Color(0xFF2E7D32); // Green
  }

  IconData _getMethodIcon() {
    switch (record.measurementMethod?.toLowerCase()) {
      case 'scale':
        return Icons.scale;
      case 'tape':
        return Icons.straighten;
      case 'visual_estimate':
        return Icons.visibility;
      default:
        return Icons.straighten;
    }
  }

  String _getMethodDisplayName() {
    switch (record.measurementMethod?.toLowerCase()) {
      case 'scale':
        return 'Scale';
      case 'tape':
        return 'Tape';
      case 'visual_estimate':
        return 'Visual';
      default:
        return record.measurementMethod ?? 'Unknown';
    }
  }

  Color _getDateColor() {
    // Single color for all date-related elements
    return const Color(0xFF303F9F); // Indigo
  }

  Color _getCattleColor() {
    // Single color for all cattle-related elements
    return const Color(0xFF7B1FA2); // Purple
  }

  Color _getBcsColor() {
    // Single color for body condition score
    return const Color(0xFFD32F2F); // Red
  }

  Color _getNotesColor() {
    // Single color for notes
    return const Color(0xFF16A085); // Teal (moved from cattle, now used for notes)
  }


}
