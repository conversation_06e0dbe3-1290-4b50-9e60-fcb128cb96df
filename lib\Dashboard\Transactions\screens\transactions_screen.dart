import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../controllers/transaction_controller.dart';
import '../tabs/transaction_analytics_tab.dart';
import '../tabs/transaction_records_tab.dart';
import '../tabs/transaction_insights_tab.dart';
import '../dialogs/transaction_form_dialog.dart';
import '../../../routes/app_routes.dart';
import '../../widgets/index.dart';
import '../../widgets/mixins/screen_state_mapper.dart'; // Import Screen State Mapper
import '../../../constants/app_layout.dart'; // Import Universal Layout
import '../../../constants/app_tabs.dart'; // Import Universal Tabs
import '../../../constants/app_colors.dart'; // Import Universal Colors

/// Transactions screen with Provider-managed controller lifecycle
/// Following the CattleScreen pattern: StatelessWidget with ChangeNotifierProvider
class TransactionsScreen extends StatelessWidget {
  const TransactionsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => TransactionController(),
      child: const _TransactionsScreenContent(),
    );
  }
}

/// Internal content widget that accesses the controller via Provider
class _TransactionsScreenContent extends StatefulWidget {
  const _TransactionsScreenContent();

  @override
  State<_TransactionsScreenContent> createState() => _TransactionsScreenContentState();
}

class _TransactionsScreenContentState extends State<_TransactionsScreenContent>
    with TickerProviderStateMixin, UniversalScreenState, ScreenStateMapper {
  late TabController _tabController;
  UniversalTabManager? _tabManager;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return UniversalLayout.tabScreen(
      title: 'Transaction Management',
      body: Consumer<TransactionController>(
        builder: (context, transactionController, child) {
          // Initialize tab manager here where Provider context is available
          _tabManager ??= UniversalTabManager.threeTabs(
            controller: _tabController,
            tabViews: [
              // Use Builder widgets for lazy initialization
              Builder(
                builder: (context) => TransactionAnalyticsTab(controller: transactionController),
              ),
              Builder(
                builder: (context) => TransactionRecordsTab(controller: transactionController),
              ),
              Builder(
                builder: (context) => TransactionInsightsTab(controller: transactionController),
              ),
            ],
            labels: const ['Analytics', 'Records', 'Insights'],
            icons: const [Icons.analytics, Icons.list, Icons.insights],
            colors: [
              UniversalEmptyStateTheme.transactions, // Blue for analytics
              const Color(0xFF388E3C), // Green for records
              Colors.purple, // Purple for insights
            ],
            showFABs: const [false, true, false], // FAB only on Records tab
            indicatorColor: UniversalEmptyStateTheme.transactions,
          );

          return UniversalStateBuilder(
            state: getScreenStateFromController(transactionController),
            errorMessage: transactionController.errorMessage,
            onRetry: () {}, // No manual retry needed - reactive streams auto-recover
            moduleColor: UniversalEmptyStateTheme.transactions,
            loadingWidget: UniversalLoadingIndicator.transactions(),
            errorWidget: UniversalErrorIndicator.transactions(
              message: transactionController.errorMessage ?? 'Failed to load transaction data',
              onRetry: () {}, // No manual retry needed - reactive streams auto-recover
            ),
            child: _tabManager!, // Tab manager is guaranteed to be initialized above
          );
        },
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.bar_chart),
          onPressed: () => Navigator.pushNamed(
            context,
            AppRoutes.transactionsReport,
          ),
          tooltip: 'View Transaction Reports',
        ),
        // Removed manual refresh - reactive streams handle all updates automatically
      ],
      floatingActionButton: AnimatedBuilder(
        animation: _tabController,
        builder: (context, child) {
          // Only rebuild FAB when tab changes, not the entire screen
          return _tabManager?.getCurrentFAB(
            onPressed: _addTransaction,
            tooltip: 'Add Transaction',
            backgroundColor: UniversalEmptyStateTheme.transactions,
          ) ?? const SizedBox.shrink(); // Handle null case
        },
      ), // Optimized FAB management with AnimatedBuilder
      // No onRefresh needed - reactive streams handle all data updates automatically
    );
  }

  void _addTransaction() {
    final transactionController = context.read<TransactionController>();

    showDialog(
      context: context,
      builder: (context) => TransactionFormDialog(
        categories: transactionController.categories,
        // No onTransactionAdded callback needed - reactive streams handle updates!
      ),
    );
  }

  // State mapping is now handled by ScreenStateMapper mixin
}
