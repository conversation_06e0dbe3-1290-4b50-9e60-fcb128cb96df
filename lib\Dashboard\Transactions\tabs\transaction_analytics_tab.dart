import 'package:flutter/material.dart';
import '../../../constants/app_tabs.dart';
import '../controllers/transaction_controller.dart';
import '../../widgets/index.dart';

class TransactionAnalyticsTab extends StatefulWidget {
  final TransactionController controller;

  const TransactionAnalyticsTab({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  State<TransactionAnalyticsTab> createState() => _TransactionAnalyticsTabState();
}

class _TransactionAnalyticsTabState extends State<TransactionAnalyticsTab>
    with AutomaticKeepAliveClientMixin {

  late final FilterController _filterController;

  @override
  void initState() {
    super.initState();
    _filterController = FilterController();
  }

  @override
  void dispose() {
    _filterController.dispose();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    return ListenableBuilder(
      listenable: widget.controller,
      builder: (context, child) {
        if (widget.controller.isLoading) {
          return UniversalLoadingIndicator.transactions();
        }

        if (widget.controller.hasError) {
          return UniversalErrorIndicator.transactions(
            message: widget.controller.errorMessage ?? 'Failed to load analytics data',
            onRetry: () => widget.controller.refresh(),
          );
        }

        final analyticsSummary = widget.controller.analyticsSummary;

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              UniversalDateFilterWidget(
                controller: _filterController,
                theme: FilterTheme.transaction,
                config: WidgetConfig.compactConfig,
              ),
              const SizedBox(height: 16),
              _buildSummaryCards(analyticsSummary),
              const SizedBox(height: 16),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.analytics, color: UniversalEmptyStateTheme.transactions),
                          const SizedBox(width: 8),
                          const Text('Financial Overview', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Text('Total Transactions: ${analyticsSummary.totalTransactions}'),
                      Text('Income Count: ${analyticsSummary.incomeTransactions}'),
                      Text('Expense Count: ${analyticsSummary.expenseTransactions}'),
                      Text('Average Income: \$${analyticsSummary.averageIncome.toStringAsFixed(2)}'),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Row(
                        children: [
                          Icon(Icons.pie_chart, color: Colors.purple),
                          SizedBox(width: 8),
                          Text('Top Categories', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                        ],
                      ),
                      const SizedBox(height: 16),
                      ...analyticsSummary.categoryBreakdown.entries.take(5).map((e) =>
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 2),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(e.key),
                              Text('\$${e.value.toStringAsFixed(2)}', style: const TextStyle(fontWeight: FontWeight.bold)),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSummaryCards(TransactionAnalyticsResult analyticsSummary) {
    return Row(
      children: [
        Expanded(child: Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                const Icon(Icons.arrow_upward, color: Colors.green, size: 32),
                const SizedBox(height: 8),
                const Text('Total Income', style: TextStyle(fontSize: 12, color: Colors.grey)),
                Text('\$${analyticsSummary.totalIncome.toStringAsFixed(2)}',
                     style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Colors.green)),
              ],
            ),
          ),
        )),
        const SizedBox(width: 12),
        Expanded(child: Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                const Icon(Icons.arrow_downward, color: Colors.red, size: 32),
                const SizedBox(height: 8),
                const Text('Total Expenses', style: TextStyle(fontSize: 12, color: Colors.grey)),
                Text('\$${analyticsSummary.totalExpenses.toStringAsFixed(2)}',
                     style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Colors.red)),
              ],
            ),
          ),
        )),
        const SizedBox(width: 12),
        Expanded(child: Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Icon(Icons.account_balance_wallet, color: analyticsSummary.netBalance >= 0 ? Colors.green : Colors.red, size: 32),
                const SizedBox(height: 8),
                const Text('Net Balance', style: TextStyle(fontSize: 12, color: Colors.grey)),
                Text('\$${analyticsSummary.netBalance.toStringAsFixed(2)}',
                     style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold,
                                    color: analyticsSummary.netBalance >= 0 ? Colors.green : Colors.red)),
              ],
            ),
          ),
        )),
      ],
    );
  }








}
