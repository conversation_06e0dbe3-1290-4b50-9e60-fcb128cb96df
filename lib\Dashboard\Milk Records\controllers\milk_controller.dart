import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:isar/isar.dart';
import 'package:async/async.dart';
import '../models/milk_record_isar.dart';
import '../models/milk_sale_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../services/milk_repository.dart';
import '../services/milk_analytics_service.dart';
import '../../Cattle/services/cattle_repository.dart';
import '../../Cattle/controllers/cattle_controller.dart'; // Import for ControllerState enum

/// Filter state object for decoupled filter management
/// Following the established FilterController pattern for consistency
class MilkFilterState {
  final String? searchQuery;
  final DateTime? startDate;
  final DateTime? endDate;
  final String? cattleId;
  final String? session;

  const MilkFilterState({
    this.searchQuery,
    this.startDate,
    this.endDate,
    this.cattleId,
    this.session,
  });

  /// Check if any filters are active
  bool get hasActiveFilters =>
      (searchQuery?.isNotEmpty ?? false) ||
      startDate != null ||
      endDate != null ||
      (cattleId?.isNotEmpty ?? false) ||
      (session?.isNotEmpty ?? false);

  /// Create a copy with updated values
  MilkFilterState copyWith({
    String? searchQuery,
    DateTime? startDate,
    DateTime? endDate,
    String? cattleId,
    String? session,
  }) {
    return MilkFilterState(
      searchQuery: searchQuery ?? this.searchQuery,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      cattleId: cattleId ?? this.cattleId,
      session: session ?? this.session,
    );
  }

  /// Clear all filters
  static const MilkFilterState empty = MilkFilterState();
}

/// Reactive controller for the main milk records screen using Dual-Stream Pattern
/// Following the cattle module template: separate unfiltered and filtered streams
/// Unfiltered stream feeds analytics, filtered stream feeds UI
class MilkController extends ChangeNotifier {
  // Repositories
  final MilkRepository _milkRepository = GetIt.instance<MilkRepository>();
  final CattleRepository _cattleRepository = GetIt.instance<CattleRepository>();
  final Isar _isar = GetIt.instance<Isar>();

  // State management
  ControllerState _state = ControllerState.loading;
  String? _errorMessage;

  // Stream subscriptions for real-time updates - Separated for filtered/unfiltered data
  StreamSubscription? _unfilteredStreamSubscription;
  StreamSubscription? _filteredStreamSubscription;

  // Data - Critical separation: filtered data for UI, unfiltered for analytics
  List<MilkRecordIsar> _unfilteredMilkRecords = []; // Complete dataset for analytics calculations
  List<MilkSaleIsar> _unfilteredMilkSales = []; // Complete milk sales dataset
  List<CattleIsar> _unfilteredCattle = []; // Complete cattle dataset for analytics

  List<MilkRecordIsar> _filteredMilkRecords = []; // Filtered dataset for UI display
  bool _hasActiveFilters = false; // Track if filters are currently applied

  // Analytics result from the dedicated service - ALWAYS calculated on unfiltered data
  MilkAnalyticsResult _analyticsResult = MilkAnalyticsResult.empty;

  // Filter state management - decoupled from UI
  MilkFilterState _currentFilters = MilkFilterState.empty;

  // Getters
  ControllerState get state => _state;
  String? get errorMessage => _errorMessage;

  /// Returns the filtered milk records for UI display
  /// This is what the MilkRecordsTab should show
  List<MilkRecordIsar> get milkRecords => List.unmodifiable(_filteredMilkRecords);

  /// Returns the complete unfiltered milk records for analytics
  /// This ensures analytics are always calculated on the full dataset
  List<MilkRecordIsar> get unfilteredMilkRecords => List.unmodifiable(_unfilteredMilkRecords);

  /// Returns the complete unfiltered milk sales for analytics
  List<MilkSaleIsar> get unfilteredMilkSales => List.unmodifiable(_unfilteredMilkSales);

  /// Returns the complete unfiltered cattle list for analytics
  List<CattleIsar> get unfilteredCattle => List.unmodifiable(_unfilteredCattle);

  // Main analytics object - single source of truth
  MilkAnalyticsResult get analytics => _analyticsResult;

  // Convenience getters for backward compatibility with UI
  int get totalMilkRecords => _analyticsResult.totalMilkRecords;
  int get totalMilkSales => _analyticsResult.totalMilkSales;
  double get totalMilkProduced => _analyticsResult.totalMilkProduced;
  double get totalMilkSold => _analyticsResult.totalMilkSold;
  double get averageDailyProduction => _analyticsResult.averageDailyProduction;
  double get averageProductionPerCattle => _analyticsResult.averageProductionPerCattle;
  double get totalRevenue => _analyticsResult.totalRevenue;
  double get averagePricePerLiter => _analyticsResult.averagePricePerLiter;
  Map<String, int> get sessionDistribution => _analyticsResult.sessionDistribution;
  Map<String, double> get cattleProductionDistribution => _analyticsResult.cattleProductionDistribution;
  Map<String, int> get productionFrequencyDistribution => _analyticsResult.productionFrequencyDistribution;
  Map<String, double> get buyerDistribution => _analyticsResult.buyerDistribution;
  Map<String, int> get paymentMethodDistribution => _analyticsResult.paymentMethodDistribution;
  double get productionConsistency => _analyticsResult.productionConsistency;
  double get salesEfficiency => _analyticsResult.salesEfficiency;
  String get topProducingCattle => _analyticsResult.topProducingCattle;
  double get topProducingCattleAmount => _analyticsResult.topProducingCattleAmount;
  String get bestBuyer => _analyticsResult.bestBuyer;
  double get bestBuyerVolume => _analyticsResult.bestBuyerVolume;
  double get productionTrend => _analyticsResult.productionTrend;
  double get priceTrend => _analyticsResult.priceTrend;
  int get daysWithProduction => _analyticsResult.daysWithProduction;
  int get daysWithSales => _analyticsResult.daysWithSales;
  bool get hasData => _unfilteredMilkRecords.isNotEmpty || _unfilteredMilkSales.isNotEmpty;

  // Filter state access
  MilkFilterState get currentFilters => _currentFilters;

  // Constructor
  MilkController() {
    _initializeStreamListeners();
  }

  /// Initialize stream listeners for real-time updates using Dual-Stream Pattern
  /// Critical: Separate streams for unfiltered (analytics) and filtered (UI) data
  void _initializeStreamListeners() {
    // Primary stream: Unfiltered data for analytics calculations
    // This stream NEVER changes and always provides the complete dataset
    final combinedStream = StreamZip([
      _milkRepository.watchAllMilkRecords(),
      _milkRepository.watchAllMilkSales(),
      _cattleRepository.watchAllCattle(),
    ]);

    _unfilteredStreamSubscription = combinedStream.listen((data) {
      _handleUnfilteredDataUpdate(
        data[0] as List<MilkRecordIsar>,
        data[1] as List<MilkSaleIsar>,
        data[2] as List<CattleIsar>,
      );
    });

    // Initially, filtered data equals unfiltered data (no filters applied)
    _filteredMilkRecords = _unfilteredMilkRecords;
    _hasActiveFilters = false;
  }

  /// Handle unfiltered data updates - Used for analytics calculations
  /// Critical: This method ONLY updates analytics and unfiltered data
  void _handleUnfilteredDataUpdate(
    List<MilkRecordIsar> unfilteredMilkRecords,
    List<MilkSaleIsar> unfilteredMilkSales,
    List<CattleIsar> unfilteredCattle,
  ) async {
    try {
      // Update the complete unfiltered datasets
      _unfilteredMilkRecords = unfilteredMilkRecords;
      _unfilteredMilkSales = unfilteredMilkSales;
      _unfilteredCattle = unfilteredCattle;

      // ALWAYS calculate analytics on the complete unfiltered dataset
      // This ensures analytics remain accurate regardless of applied filters
      if (_unfilteredMilkRecords.isEmpty && _unfilteredMilkSales.isEmpty) {
        _analyticsResult = MilkAnalyticsResult.empty;
      } else {
        _calculateAnalytics();
      }

      // If no filters are active, update filtered data to match unfiltered
      if (!_hasActiveFilters) {
        _filteredMilkRecords = List.from(_unfilteredMilkRecords);
      }

      _setState(ControllerState.loaded);
      notifyListeners();
    } catch (e) {
      debugPrint('Error handling unfiltered data update: $e');
      _setState(ControllerState.error);
      _errorMessage = 'Failed to update unfiltered data: $e';
      notifyListeners();
    }
  }

  /// Calculate analytics using the dedicated service
  /// Critical: ALWAYS uses unfiltered data to ensure accurate analytics
  void _calculateAnalytics() {
    _analyticsResult = MilkAnalyticsService.calculate(
      _unfilteredMilkRecords, // Use unfiltered data for accurate analytics
      _unfilteredMilkSales,
      _unfilteredCattle,
    );
  }

  /// Apply filters using the FilterController pattern for decoupled filter management
  /// Critical Fix: This method now creates a separate filtered stream without affecting analytics
  void applyFilters(MilkFilterState filterState) async {
    // Update current filter state
    _currentFilters = filterState;

    // Cancel existing filtered subscription (but keep unfiltered stream for analytics)
    _filteredStreamSubscription?.cancel();

    // Check if filters are active
    _hasActiveFilters = filterState.hasActiveFilters;

    if (_hasActiveFilters) {
      // Build filtered query for UI display
      final filteredQuery = _buildFilteredQuery(filterState);

      // Create separate stream for filtered data
      _filteredStreamSubscription = filteredQuery.watch(fireImmediately: true)
          .listen((filteredList) {
        _handleFilteredDataUpdate(filteredList);
      });
    } else {
      // No filters: filtered data equals unfiltered data
      _filteredMilkRecords = List.from(_unfilteredMilkRecords);
      notifyListeners();
    }
  }

  /// Convenience method for backward compatibility and simple filter updates
  void updateFilters({
    String? searchQuery,
    DateTime? startDate,
    DateTime? endDate,
    String? cattleId,
    String? session,
  }) {
    final newFilters = _currentFilters.copyWith(
      searchQuery: searchQuery,
      startDate: startDate,
      endDate: endDate,
      cattleId: cattleId,
      session: session,
    );
    applyFilters(newFilters);
  }

  /// Clear all filters
  void clearFilters() {
    applyFilters(MilkFilterState.empty);
  }

  /// Handle filtered data updates - Used for UI display only
  /// Critical: This method NEVER affects analytics calculations
  void _handleFilteredDataUpdate(List<MilkRecordIsar> filteredList) async {
    try {
      // Update only the filtered dataset for UI display
      _filteredMilkRecords = filteredList;

      // Do NOT recalculate analytics here - they're handled by unfiltered stream
      // This ensures analytics always reflect the complete dataset

      notifyListeners();
    } catch (e) {
      debugPrint('Error handling filtered data update: $e');
      // Don't change state to error for filtered data issues
      notifyListeners();
    }
  }

  /// Build filtered Isar query based on FilterState object
  /// This method dynamically constructs database queries for optimal performance
  dynamic _buildFilteredQuery(MilkFilterState filterState) {
    // Start with base query and build the chain using dynamic typing
    dynamic currentQuery = _isar.milkRecordIsars.where();

    // Apply search filter at database level
    if (filterState.searchQuery?.isNotEmpty ?? false) {
      final searchTerm = filterState.searchQuery!.toLowerCase();
      currentQuery = currentQuery.filter().group((q) => q
          .notesContains(searchTerm, caseSensitive: false));
    }

    // Apply date range filters at database level
    if (filterState.startDate != null) {
      currentQuery = currentQuery.filter().dateGreaterThan(filterState.startDate!);
    }
    if (filterState.endDate != null) {
      // Add one day to make end date inclusive
      final inclusiveEndDate = filterState.endDate!.add(const Duration(days: 1));
      currentQuery = currentQuery.filter().dateLessThan(inclusiveEndDate);
    }

    // Apply cattle filter
    if (filterState.cattleId?.isNotEmpty ?? false) {
      currentQuery = currentQuery.filter().cattleBusinessIdEqualTo(filterState.cattleId);
    }

    // Apply session filter
    if (filterState.session?.isNotEmpty ?? false) {
      currentQuery = currentQuery.filter().sessionEqualTo(filterState.session);
    }

    // Apply sorting at database level for optimal performance
    currentQuery = currentQuery.sortByDateDesc(); // Default sort by date (newest first)

    return currentQuery;
  }

  // CRUD Methods - Single Source of Truth pattern
  // These methods only update the database, stream handles UI updates
  // Exceptions bubble up naturally to be handled by higher-level error handlers

  /// Add new milk record - only updates database, stream handles UI update
  Future<void> addMilkRecord(MilkRecordIsar record) async {
    await _milkRepository.saveMilkRecord(record);
    // Stream will handle the UI update automatically
  }

  /// Add new milk sale - only updates database, stream handles UI update
  Future<void> addMilkSale(MilkSaleIsar sale) async {
    await _milkRepository.saveMilkSale(sale);
    // Stream will handle the UI update automatically
  }

  /// Update milk record - only updates database, stream handles UI update
  Future<void> updateMilkRecord(MilkRecordIsar updatedRecord) async {
    await _milkRepository.saveMilkRecord(updatedRecord);
    // Stream will handle the UI update automatically
  }

  /// Update milk sale - only updates database, stream handles UI update
  Future<void> updateMilkSale(MilkSaleIsar updatedSale) async {
    await _milkRepository.saveMilkSale(updatedSale);
    // Stream will handle the UI update automatically
  }

  /// Delete milk record - only updates database, stream handles UI update
  Future<void> deleteMilkRecord(int recordId) async {
    await _milkRepository.deleteMilkRecord(recordId);
    // Stream will handle the UI update automatically
  }

  /// Delete milk sale - only updates database, stream handles UI update
  Future<void> deleteMilkSale(int saleId) async {
    await _milkRepository.deleteMilkSale(saleId);
    // Stream will handle the UI update automatically
  }

  // Helper methods
  CattleIsar? getCattle(String? cattleBusinessId) {
    if (cattleBusinessId == null) return null;
    try {
      return _unfilteredCattle.firstWhere(
        (cattle) => cattle.businessId == cattleBusinessId,
      );
    } catch (e) {
      return null;
    }
  }

  String getCattleName(String? cattleBusinessId) {
    final cattle = getCattle(cattleBusinessId);
    return cattle?.name ?? 'Unknown Cattle';
  }

  List<CattleIsar> get femaleCattleList {
    return _unfilteredCattle.where((c) => c.gender == CattleGender.female).toList();
  }

  // State management helpers
  void _setState(ControllerState newState) {
    _state = newState;
    _errorMessage = null;
    notifyListeners();
  }

  void _setError(String error) {
    _state = ControllerState.error;
    _errorMessage = error;
    notifyListeners();
  }

  @override
  void dispose() {
    _unfilteredStreamSubscription?.cancel();
    _filteredStreamSubscription?.cancel();
    super.dispose();
  }
}
