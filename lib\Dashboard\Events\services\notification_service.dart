import 'package:flutter/material.dart';
import '../models/event_isar.dart';
import 'package:shared_preferences/shared_preferences.dart';

class NotificationService {
  static Future<List<EventIsar>> getUpcomingEvents(List<EventIsar> events) async {
    final prefs = await SharedPreferences.getInstance();
    final upcomingReminderDays = prefs.getInt('upcoming_reminder_days') ?? 7;
    final now = DateTime.now();
    final threshold = now.add(Duration(days: upcomingReminderDays));

    return events.where((event) {
      if (event.isCompleted) return false;
      if (event.eventDate == null) return false;
      final eventDateTime = _combineDateTime(
        event.eventDate!, 
        event.time?.toTimeOfDay() ?? const TimeOfDay(hour: 0, minute: 0)
      );
      return eventDateTime.isAfter(now) && eventDateTime.isBefore(threshold);
    }).toList()
      ..sort((a, b) => _combineDateTime(
          a.eventDate ?? DateTime.now(), 
          a.time?.toTimeOfDay() ?? const TimeOfDay(hour: 0, minute: 0)
        ).compareTo(_combineDateTime(
          b.eventDate ?? DateTime.now(), 
          b.time?.toTimeOfDay() ?? const TimeOfDay(hour: 0, minute: 0)
        ))
      );
  }

  static Future<List<EventIsar>> getMissedEvents(List<EventIsar> events) async {
    final prefs = await SharedPreferences.getInstance();
    final missedAlertHours = prefs.getInt('missed_alert_hours') ?? 24;
    final now = DateTime.now();
    final threshold = now.subtract(Duration(hours: missedAlertHours));

    return events.where((event) {
      if (event.isCompleted) return false;
      if (event.eventDate == null) return false;
      final eventDateTime = _combineDateTime(
        event.eventDate!, 
        event.time?.toTimeOfDay() ?? const TimeOfDay(hour: 0, minute: 0)
      );
      return eventDateTime.isBefore(now) && eventDateTime.isAfter(threshold);
    }).toList()
      ..sort((a, b) => _combineDateTime(
          b.eventDate ?? DateTime.now(), 
          b.time?.toTimeOfDay() ?? const TimeOfDay(hour: 0, minute: 0)
        ).compareTo(_combineDateTime(
          a.eventDate ?? DateTime.now(), 
          a.time?.toTimeOfDay() ?? const TimeOfDay(hour: 0, minute: 0)
        ))
      );
  }

  static Future<List<EventIsar>> getImportantEvents(List<EventIsar> events) async {
    final now = DateTime.now();
    final criticalThreshold = now.add(const Duration(hours: 12));

    return events.where((event) {
      if (event.isCompleted) return false;
      if (event.eventDate == null) return false;
      final eventDateTime = _combineDateTime(
        event.eventDate!, 
        event.time?.toTimeOfDay() ?? const TimeOfDay(hour: 0, minute: 0)
      );
      return (event.priority == EventPriority.high ||
          _isImportantEventType(event.type?.toEventType() ?? EventType.miscellaneous)) &&
          eventDateTime.isBefore(criticalThreshold);
    }).toList()
      ..sort((a, b) {
        // Sort by priority first, then by date
        final priorityCompare = b.priority.index.compareTo(a.priority.index);
        if (priorityCompare != 0) return priorityCompare;
        return _combineDateTime(
          a.eventDate ?? DateTime.now(), 
          a.time?.toTimeOfDay() ?? const TimeOfDay(hour: 0, minute: 0)
        ).compareTo(_combineDateTime(
          b.eventDate ?? DateTime.now(), 
          b.time?.toTimeOfDay() ?? const TimeOfDay(hour: 0, minute: 0)
        ));
      });
  }

  static bool isMissedEvent(EventIsar event) {
    if (event.isCompleted) return false;
    if (event.eventDate == null) return false;
    final now = DateTime.now();
    final eventDateTime = _combineDateTime(
      event.eventDate!, 
      event.time?.toTimeOfDay() ?? const TimeOfDay(hour: 0, minute: 0)
    );
    return eventDateTime.isBefore(now);
  }

  static bool isUpcomingEvent(EventIsar event) {
    if (event.isCompleted) return false;
    if (event.eventDate == null) return false;
    final now = DateTime.now();
    final eventDateTime = _combineDateTime(
      event.eventDate!, 
      event.time?.toTimeOfDay() ?? const TimeOfDay(hour: 0, minute: 0)
    );
    return eventDateTime.isAfter(now);
  }

  static bool isImportantEvent(EventIsar event) {
    if (event.isCompleted) return false;
    return event.priority == EventPriority.high || 
      _isImportantEventType(event.type?.toEventType() ?? EventType.miscellaneous);
  }

  static bool _isImportantEventType(EventType type) {
    return [
      EventType.pregnancyCheck,
      EventType.vaccination,
      EventType.calving,
      EventType.healthCheckup,
      EventType.breeding,
      EventType.dryOff,
    ].contains(type);
  }

  static DateTime _combineDateTime(DateTime date, TimeOfDay time) {
    return DateTime(
      date.year,
      date.month,
      date.day,
      time.hour,
      time.minute,
    );
  }

  static Color getPriorityColor(EventPriority priority) {
    switch (priority) {
      case EventPriority.high:
        return Colors.red.shade700;
      case EventPriority.medium:
        return Colors.orange.shade700;
      case EventPriority.low:
        return Colors.green.shade600;
    }
  }

  static IconData getEventTypeIcon(EventType type) {
    switch (type) {
      case EventType.breeding:
        return Icons.favorite;
      case EventType.pregnancyCheck:
        return Icons.pregnant_woman;
      case EventType.calving:
        return Icons.child_care;
      case EventType.vaccination:
        return Icons.medical_services;
      case EventType.healthCheckup:
        return Icons.local_hospital;
      case EventType.weightMeasurement:
        return Icons.monitor_weight;
      case EventType.deworming:
        return Icons.bug_report;
      case EventType.purchased:
        return Icons.shopping_cart;
      case EventType.sold:
        return Icons.attach_money;
      case EventType.miscellaneous:
        return Icons.more_horiz;
      case EventType.custom:
        return Icons.category;
      case EventType.dryOff:
        return Icons.opacity;
    }
  }

  static String getEventPriorityLabel(EventPriority priority) {
    switch (priority) {
      case EventPriority.high:
        return 'Urgent';
      case EventPriority.medium:
        return 'Important';
      case EventPriority.low:
        return 'Normal';
    }
  }
}