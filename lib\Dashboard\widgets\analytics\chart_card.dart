import 'package:flutter/material.dart';

/// Chart Card Widget
/// 
/// A reusable card widget for displaying charts and graphs.
/// Provides consistent styling and layout for chart components.
class ChartCard extends StatelessWidget {
  final String title;
  final Widget chart;
  final String? subtitle;
  final Color? color;
  final VoidCallback? onTap;
  final List<Widget>? actions;

  const ChartCard({
    Key? key,
    required this.title,
    required this.chart,
    this.subtitle,
    this.color,
    this.onTap,
    this.actions,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final primaryColor = color ?? theme.primaryColor;

    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: theme.textTheme.titleMedium?.copyWith(
                            color: primaryColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (subtitle != null) ...[
                          const SizedBox(height: 4),
                          Text(
                            subtitle!,
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  if (actions != null) ...actions!,
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Chart content
              chart,
            ],
          ),
        ),
      ),
    );
  }
}
