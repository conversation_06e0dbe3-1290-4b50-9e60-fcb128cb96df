import 'package:flutter/material.dart';

/// Recommendation Card Widget
/// 
/// A specialized card widget for displaying actionable recommendations.
/// Provides consistent styling for recommendation components.
class RecommendationCard extends StatelessWidget {
  final String title;
  final String description;
  final String? actionText;
  final VoidCallback? onAction;
  final IconData? icon;
  final Color? color;
  final RecommendationPriority priority;

  const RecommendationCard({
    Key? key,
    required this.title,
    required this.description,
    this.actionText,
    this.onAction,
    this.icon,
    this.color,
    this.priority = RecommendationPriority.medium,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final recommendationColor = _getRecommendationColor(context);
    final recommendationIcon = _getRecommendationIcon();

    return Card(
      elevation: 2,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: recommendationColor.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with priority indicator
              Row(
                children: [
                  Icon(
                    icon ?? recommendationIcon,
                    color: recommendationColor,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      title,
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: recommendationColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  _buildPriorityChip(context),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Description
              Text(
                description,
                style: theme.textTheme.bodyMedium,
              ),
              
              // Action button
              if (actionText != null && onAction != null) ...[
                const SizedBox(height: 16),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: onAction,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: recommendationColor,
                      foregroundColor: Colors.white,
                    ),
                    child: Text(actionText!),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPriorityChip(BuildContext context) {
    final priorityColor = _getPriorityColor();
    final priorityText = _getPriorityText();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: priorityColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: priorityColor.withValues(alpha: 0.3)),
      ),
      child: Text(
        priorityText,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: priorityColor,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Color _getRecommendationColor(BuildContext context) {
    if (color != null) return color!;
    return _getPriorityColor();
  }

  Color _getPriorityColor() {
    switch (priority) {
      case RecommendationPriority.high:
        return Colors.red;
      case RecommendationPriority.medium:
        return Colors.orange;
      case RecommendationPriority.low:
        return Colors.blue;
    }
  }

  String _getPriorityText() {
    switch (priority) {
      case RecommendationPriority.high:
        return 'High';
      case RecommendationPriority.medium:
        return 'Medium';
      case RecommendationPriority.low:
        return 'Low';
    }
  }

  IconData _getRecommendationIcon() {
    switch (priority) {
      case RecommendationPriority.high:
        return Icons.priority_high;
      case RecommendationPriority.medium:
        return Icons.lightbulb_outline;
      case RecommendationPriority.low:
        return Icons.info_outline;
    }
  }
}

/// Priority levels for recommendations
enum RecommendationPriority {
  high,
  medium,
  low,
}
