import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'info_row.dart';

class PregnancyHistoryCard extends StatelessWidget {
  final List<Map<String, dynamic>> records;
  final String title;
  final String emptyMessage;
  final Function(Map<String, dynamic>)? onEdit;
  final Function(Map<String, dynamic>)? onDelete;
  final Function(Map<String, dynamic>)? onStatusTap;

  const PregnancyHistoryCard({
    super.key,
    required this.records,
    this.title = 'Pregnancy History',
    this.emptyMessage = 'No pregnancy records found',
    this.onEdit,
    this.onDelete,
    this.onStatusTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      color: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with purple color theme
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.purple.withAlpha(26), // 0.1 * 255 = 26
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundColor:
                      Colors.purple.withAlpha(51), // 0.2 * 255 = 51
                  child: const Icon(
                    Icons.pregnant_woman,
                    color: Colors.purple,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.purple,
                  ),
                ),
              ],
            ),
          ),

          // Content based on whether records are available
          if (records.isEmpty)
            Padding(
              padding: const EdgeInsets.all(24.0),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.purple.withAlpha(26), // 0.1 * 255 = 26
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.pregnant_woman_outlined,
                        size: 48,
                        color: Colors.purple,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      emptyMessage,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Add a pregnancy record to start tracking',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.black54,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            )
          else
            Padding(
              padding: const EdgeInsets.fromLTRB(10, 16, 10, 16),
              child: ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: records.length,
                itemBuilder: (context, index) {
                  // Add divider between items
                  if (index > 0) {
                    return Column(
                      children: [
                        const Divider(height: 32),
                        _buildPregnancyRecord(context, records[index]),
                      ],
                    );
                  }
                  return _buildPregnancyRecord(context, records[index]);
                },
              ),
            ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return const Color(0xFF1976D2); // Blue
      case 'confirmed':
        return const Color(0xFF2E7D32); // Green
      case 'abortion':
        return const Color(0xFFD32F2F); // Red
      case 'completed':
        return const Color(0xFF9C27B0); // Purple
      default:
        return Colors.grey;
    }
  }

  // Helper method to calculate pregnancy stage
  String _calculatePregnancyStage(DateTime startDate) {
    final now = DateTime.now();
    final daysSinceStart = now.difference(startDate).inDays;

    if (daysSinceStart < 90) {
      return 'Early Stage';
    } else if (daysSinceStart < 180) {
      return 'Mid Stage';
    } else {
      return 'Late Stage';
    }
  }

  // Helper method to calculate days remaining until due date
  String _calculateDaysRemaining(DateTime startDate, DateTime? dueDate) {
    dueDate ??= startDate.add(const Duration(days: 280));

    final now = DateTime.now();
    final daysRemaining = dueDate.difference(now).inDays;

    if (daysRemaining < 0) {
      return 'Past due by ${-daysRemaining} days';
    } else if (daysRemaining == 0) {
      return 'Due today';
    } else {
      return '$daysRemaining days remaining';
    }
  }

  Widget _buildPregnancyRecord(
      BuildContext context, Map<String, dynamic> record) {
    // Add null checks for all date fields
    final startDate = record['startDate'] != null
        ? DateTime.tryParse(record['startDate'].toString()) ?? DateTime.now()
        : DateTime.now();
    final expectedCalvingDate = record['expectedCalvingDate'] != null
        ? DateTime.tryParse(record['expectedCalvingDate'].toString())
        : null;
    final status = record['status'] ?? 'Unknown';
    final notes = record['notes'] ?? '';
    final cattleId = record['cattleId']?.toString() ?? '';
    final cattleName = record['cattleName']?.toString() ?? '';

    final statusColor = _getStatusColor(status);

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Record Header with status-based color
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: statusColor.withAlpha(26), // 0.1 * 255 = 26
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Row(
                  children: [
                    CircleAvatar(
                      radius: 20,
                      backgroundColor:
                          statusColor.withAlpha(51), // 0.2 * 255 = 51
                      child: Icon(
                        Icons.pregnant_woman,
                        color: statusColor,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            DateFormat('MMMM dd, yyyy').format(startDate),
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: statusColor,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                          if (cattleName.isNotEmpty || cattleId.isNotEmpty) ...[
                            const SizedBox(height: 4),
                            Text(
                              '${cattleName.isNotEmpty ? cattleName : 'Unknown'} (${cattleId.isNotEmpty ? cattleId : 'Unknown'})',
                              style: TextStyle(
                                fontSize: 15,
                                fontWeight: FontWeight.w600,
                                color: statusColor,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              // More options menu (edit/delete)
              if (onEdit != null || onDelete != null)
                PopupMenuButton<String>(
                  icon: const Icon(Icons.more_vert),
                  onSelected: (value) {
                    if (value == 'edit' && onEdit != null) {
                      onEdit!(record);
                    } else if (value == 'delete' && onDelete != null) {
                      onDelete!(record);
                    }
                  },
                  itemBuilder: (context) => [
                    if (onEdit != null)
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit),
                            SizedBox(width: 8),
                            Text('Edit'),
                          ],
                        ),
                      ),
                    if (onDelete != null)
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, color: Colors.red),
                            SizedBox(width: 8),
                            Text('Delete', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                  ],
                ),
            ],
          ),
        ),

        // Record Details
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Pregnancy Stage
              InfoRow(
                icon: Icons.pregnant_woman,
                label: 'Stage',
                value: status.toString().toLowerCase() == 'completed'
                    ? 'Delivery Completed'
                    : _calculatePregnancyStage(startDate),
                color: Colors.purple,
              ),
              const SizedBox(height: 10),

              // Status
              InfoRow(
                icon: Icons.check_circle_outline,
                label: 'Status',
                value: status,
                color: statusColor,
                isStatus: true,
                onTap: onStatusTap != null ? () => onStatusTap!(record) : null,
              ),
              const SizedBox(height: 10),

              // Expected Delivery Date
              InfoRow(
                icon: Icons.today,
                label: status.toLowerCase() == 'completed'
                    ? 'Delivery Date'
                    : 'Expected Delivery Date',
                value: status.toLowerCase() == 'completed' &&
                        record['completionDate'] != null
                    ? DateFormat('MMMM dd, yyyy').format(
                        DateTime.parse(record['completionDate'].toString()))
                    : expectedCalvingDate != null
                        ? DateFormat('MMMM dd, yyyy')
                            .format(expectedCalvingDate)
                        : 'Not set',
                color: Colors.teal,
              ),
              const SizedBox(height: 10),

              // Remaining Days
              InfoRow(
                icon: Icons.hourglass_bottom,
                label: status.toLowerCase() == 'completed'
                    ? 'Days Taken'
                    : 'Remaining',
                value: status.toLowerCase() == 'completed'
                    ? record['completionDate'] != null &&
                            record['startDate'] != null
                        ? '${DateTime.parse(record['completionDate'].toString()).difference(DateTime.parse(record['startDate'])).inDays} days'
                        : expectedCalvingDate != null
                            ? '${expectedCalvingDate.difference(startDate).inDays} days'
                            : 'Unknown'
                    : _calculateDaysRemaining(startDate, expectedCalvingDate),
                color: Colors.indigo,
              ),

              // Notes section
              if (notes.isNotEmpty) ...[
                const SizedBox(height: 12),
                const Divider(),
                const SizedBox(height: 4),
                InfoRow(
                  icon: Icons.notes,
                  label: 'Notes',
                  value: notes,
                  color: Colors.deepPurple,
                  isMultiline: true,
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }
}
