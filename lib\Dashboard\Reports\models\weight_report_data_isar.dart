import 'package:flutter/material.dart';
import 'package:isar/isar.dart';
import 'chart_data_isar.dart';
import 'report_data_isar.dart';

part 'weight_report_data_isar.g.dart';

@collection
class WeightReportDataIsar with ReportDataIsarMixin {
  Id id = Isar.autoIncrement;

  // Weight records filtered by the current report criteria
  @ignore
  List<WeightRecordIsar> get filteredRecords {
    return createMockWeightRecords();
  }

  // Weight trends data for charts
  @ignore
  List<WeightTrendPoint> get weightTrends {
    final result = <WeightTrendPoint>[];
    if (weightDates != null && weightValues != null) {
      for (int i = 0; i < weightDates!.length && i < weightValues!.length; i++) {
        result.add(WeightTrendPoint(
          date: weightDates![i],
          weight: weightValues![i],
        ));
      }
    }
    return result;
  }

  // Mock method to create sample weight records for the report
  List<WeightRecordIsar> createMockWeightRecords() {
    // This is a placeholder implementation
    // In a real app, this data would be retrieved from a database
    final records = <WeightRecordIsar>[];
    
    // Return empty list for now
    return records;
  }

  int? totalWeighings;
  double? averageWeight;
  double? minimumWeight;
  double? maximumWeight;
  double? totalWeightGain;
  double? averageWeightGain;
  double? averageDailyGain;

  // Monthly weight chart data
  List<DateTime>? weightDates;
  List<double>? weightValues;

  // Weight range distribution
  List<String>? weightRanges;
  List<int>? weightRangeCounts;
  List<int>? weightRangeColors;

  WeightReportDataIsar();

  /// Constructor with parameters
  WeightReportDataIsar.withParams({
    this.id = Isar.autoIncrement,
    String? reportType,
    String? businessId,
    this.totalWeighings,
    this.averageWeight,
    this.minimumWeight,
    this.maximumWeight,
    this.totalWeightGain,
    this.averageWeightGain,
    this.averageDailyGain,
    this.weightDates,
    this.weightValues,
    this.weightRanges,
    this.weightRangeCounts,
    this.weightRangeColors,
  }) {
    this.reportType = reportType;
    this.businessId = businessId;
  }

  /// Factory constructor to create a new weight report
  factory WeightReportDataIsar.create({
    required String title,
    DateTime? startDate,
    DateTime? endDate,
    String? filterCriteria,
    int? totalWeighings,
    double? averageWeight,
    double? minimumWeight,
    double? maximumWeight,
    double? totalWeightGain,
    double? averageWeightGain,
    double? averageDailyGain,
    List<DateTime>? weightDates,
    List<double>? weightValues,
    List<String>? weightRanges,
    List<int>? weightRangeCounts,
    List<int>? weightRangeColors,
  }) {
    final report = WeightReportDataIsar();

    // Initialize the base report properties
    report.initializeReport(
      reportType: 'weight',
      title: title,
      startDate: startDate,
      endDate: endDate,
      filterCriteria: filterCriteria,
    );

    // Set the weight-specific properties
    report.totalWeighings = totalWeighings;
    report.averageWeight = averageWeight;
    report.minimumWeight = minimumWeight;
    report.maximumWeight = maximumWeight;
    report.totalWeightGain = totalWeightGain;
    report.averageWeightGain = averageWeightGain;
    report.averageDailyGain = averageDailyGain;
    report.weightDates = weightDates;
    report.weightValues = weightValues;
    report.weightRanges = weightRanges;
    report.weightRangeCounts = weightRangeCounts;
    report.weightRangeColors = weightRangeColors;

    return report;
  }

  @override
  List<DataColumn> getTableColumns() {
    return [
      const DataColumn(label: Text('Metric')),
      const DataColumn(label: Text('Value'), numeric: true),
    ];
  }

  @override
  List<DataRow> getTableRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('Total Weighings')),
        DataCell(Text('${totalWeighings ?? 0}')),
      ]),
      DataRow(cells: [
        const DataCell(Text('Average Weight (kg)')),
        DataCell(Text(averageWeight?.toStringAsFixed(1) ?? '0.0')),
      ]),
      DataRow(cells: [
        const DataCell(Text('Minimum Weight (kg)')),
        DataCell(Text(minimumWeight?.toStringAsFixed(1) ?? '0.0')),
      ]),
      DataRow(cells: [
        const DataCell(Text('Maximum Weight (kg)')),
        DataCell(Text(maximumWeight?.toStringAsFixed(1) ?? '0.0')),
      ]),
      DataRow(cells: [
        const DataCell(Text('Total Weight Gain (kg)')),
        DataCell(Text(totalWeightGain?.toStringAsFixed(1) ?? '0.0')),
      ]),
      DataRow(cells: [
        const DataCell(Text('Average Weight Gain (kg)')),
        DataCell(Text(averageWeightGain?.toStringAsFixed(1) ?? '0.0')),
      ]),
      DataRow(cells: [
        const DataCell(Text('Average Daily Gain (kg/day)')),
        DataCell(Text(averageDailyGain?.toStringAsFixed(2) ?? '0.00')),
      ]),
    ];
  }

  @override
  Map<String, dynamic> getSummaryData() {
    return {
      'Total Weighings': totalWeighings ?? 0,
      'Average Weight (kg)': averageWeight?.toStringAsFixed(1) ?? '0.0',
      'Total Weight Gain (kg)': totalWeightGain?.toStringAsFixed(1) ?? '0.0',
      'Average Daily Gain (kg/day)':
          averageDailyGain?.toStringAsFixed(2) ?? '0.00',
    };
  }

  @override
  List<ChartDataIsar> getChartData() {
    final result = <ChartDataIsar>[];

    // Add weight range distribution chart data
    if (weightRanges != null &&
        weightRangeCounts != null &&
        weightRangeColors != null) {
      for (int i = 0;
          i < weightRanges!.length &&
              i < weightRangeCounts!.length &&
              i < weightRangeColors!.length;
          i++) {
        result.add(ChartDataIsar()
          ..label = weightRanges![i]
          ..value = weightRangeCounts![i].toDouble()
          ..colorValue = weightRangeColors![i]);
      }
    }

    return result;
  }

  // Helper method to get time series weight data
  List<ChartDataIsar> getTimeSeriesChartData() {
    final result = <ChartDataIsar>[];

    // Create time series chart data from weightDates and weightValues
    if (weightDates != null && weightValues != null) {
      for (int i = 0;
          i < weightDates!.length && i < weightValues!.length;
          i++) {
        result.add(ChartDataIsar()
              ..date = weightDates![i]
              ..value = weightValues![i]
              ..colorValue = Colors.blue.r.toInt() // Use .r instead of .red
            );
      }
    }

    return result;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    map.addAll({
      'totalWeighings': totalWeighings,
      'averageWeight': averageWeight,
      'minimumWeight': minimumWeight,
      'maximumWeight': maximumWeight,
      'totalWeightGain': totalWeightGain,
      'averageWeightGain': averageWeightGain,
      'averageDailyGain': averageDailyGain,
      'weightDates':
          weightDates?.map((date) => date.toIso8601String()).toList(),
      'weightValues': weightValues,
      'weightRanges': weightRanges,
      'weightRangeCounts': weightRangeCounts,
      'weightRangeColors': weightRangeColors,
    });
    return map;
  }

  factory WeightReportDataIsar.fromMap(Map<String, dynamic> map) {
    final filteredRecords = (map['records'] as List<dynamic>)
        .map((record) => WeightRecordIsar.fromMap(record))
        .toList();

    // Initialize variables
    int totalRecords = filteredRecords.length;
    double totalWeight = 0;
    double? maxWeight;
    double? minWeight;
    
    // Calculate statistics
    for (final record in filteredRecords) {
      final double weight = record.weight; // Record.weight is required, so no null check needed
      totalWeight += weight;
      maxWeight = maxWeight == null ? weight : (weight > maxWeight ? weight : maxWeight);
      minWeight = minWeight == null ? weight : (weight < minWeight ? weight : minWeight);
    }

    final report = WeightReportDataIsar.withParams(
      id: map['id'] as int? ?? Isar.autoIncrement,
      reportType: map['reportType'] as String?,
      businessId: map['businessId'] as String?,
      totalWeighings: totalRecords,
      averageWeight: totalRecords > 0 ? totalWeight / totalRecords : 0,
      minimumWeight: minWeight,
      maximumWeight: maxWeight,
      weightDates: filteredRecords.map((r) => r.date).toList(),
      weightValues: filteredRecords.map((r) => r.weight).toList(),
      weightRanges: map['weightRanges'] as List<String>?,
      weightRangeCounts: map['weightRangeCounts'] as List<int>?,
      weightRangeColors: map['weightRangeColors'] as List<int>?,
    );
    
    return report;
  }

  /// Empty constructor for use when no data is available
  WeightReportDataIsar.empty() {
    // Initialize with default values
    initializeReport(
      reportType: 'weight',
      title: 'Weight Report',
      startDate: DateTime.now().subtract(const Duration(days: 90)),
      endDate: DateTime.now(),
      filterCriteria: '',
    );
    totalWeighings = 0;
    averageWeight = 0;
    minimumWeight = 0;
    maximumWeight = 0;
    // Initialize other fields to default values if needed
  }
}

/// Class to represent a weight record in the weight report
class WeightRecordIsar {
  final String cattleId;
  final DateTime date;
  final double weight;
  final double? previousWeight;
  final double? weightGain;
  final int? daysSinceLastWeighing;
  final double? dailyGain;

  WeightRecordIsar({
    required this.cattleId,
    required this.date,
    required this.weight,
    this.previousWeight,
    this.weightGain,
    this.daysSinceLastWeighing,
    this.dailyGain,
  });

  factory WeightRecordIsar.fromMap(Map<String, dynamic> map) {
    return WeightRecordIsar(
      cattleId: map['cattleId'] as String,
      date: DateTime.parse(map['date'] as String),
      weight: map['weight'] as double,
      previousWeight: map['previousWeight'] as double?,
      weightGain: map['weightGain'] as double?,
      daysSinceLastWeighing: map['daysSinceLastWeighing'] as int?,
      dailyGain: map['dailyGain'] as double?,
    );
  }
}

/// Class to represent a point in the weight trend chart
class WeightTrendPoint {
  final DateTime date;
  final double weight;

  WeightTrendPoint({
    required this.date,
    required this.weight,
  });
}
