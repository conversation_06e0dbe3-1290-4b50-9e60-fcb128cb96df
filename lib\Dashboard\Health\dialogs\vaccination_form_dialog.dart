import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import 'package:isar/isar.dart';
import '../models/vaccination_record_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../../utils/message_utils.dart';

class VaccinationFormDialog extends StatefulWidget {
  final String cattleId;
  final List<CattleIsar>? cattle;
  final VaccinationIsar? vaccination;
  final Future<bool> Function(VaccinationIsar)? onSave;

  const VaccinationFormDialog({
    Key? key,
    required this.cattleId,
    this.cattle,
    this.vaccination,
    this.onSave,
  }) : super(key: key);

  @override
  State<VaccinationFormDialog> createState() => _VaccinationFormDialogState();
}

class _VaccinationFormDialogState extends State<VaccinationFormDialog> {

  final _formKey = GlobalKey<FormState>();
  final _scrollController = ScrollController();
  final _logger = Logger('VaccinationFormDialog');

  // Input styling
  static const EdgeInsets _inputContentPadding = EdgeInsets.symmetric(horizontal: 16, vertical: 12);

  // Vaccination data
  late String _cattleId;
  String _vaccineName = '';
  String _batchNumber = '';
  String _manufacturer = '';
  DateTime _administeredDate = DateTime.now();
  String _notes = '';
  double _cost = 0.0;

  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _cattleId = widget.cattleId;

    if (widget.vaccination != null) {
      _loadVaccinationData();
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _loadVaccinationData() {
    final vaccination = widget.vaccination!;
    _vaccineName = vaccination.vaccineName ?? '';
    _batchNumber = vaccination.batchNumber ?? '';
    _manufacturer = vaccination.manufacturer ?? '';
    _administeredDate = vaccination.date ?? DateTime.now();
    _notes = vaccination.notes ?? '';
    _cost = vaccination.cost ?? 0.0;
  }

  // Helper method to get selected cattle
  CattleIsar? _getSelectedCattle() {
    if (widget.cattle == null || widget.cattle!.isEmpty) {
      return null;
    }

    return widget.cattle!.firstWhere(
      (cattle) => cattle.businessId == _cattleId,
      orElse: () => widget.cattle!.first,
    );
  }

  // Format cattle for display
  String _getCattleDisplayName() {
    final selectedCattle = _getSelectedCattle();
    if (selectedCattle == null) {
      return 'Cattle ID: $_cattleId';
    }

    return '${selectedCattle.name ?? 'Unnamed'} (${selectedCattle.tagId ?? 'No Tag'})';
  }

  Future<void> _saveVaccination() async {
    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();

      setState(() {
        _isSaving = true;
      });

      try {
        // Preserve the existing ID if editing
        final existingId = widget.vaccination?.id;
        final existingRecordId = widget.vaccination?.recordId;
        final createdAt = widget.vaccination?.createdAt ?? DateTime.now();

        // Create VaccinationIsar object
        final vaccinationRecord = VaccinationIsar()
          ..id = existingId ?? Isar.autoIncrement // Preserve Isar ID for updates
          ..recordId = existingRecordId // Preserve recordId if it exists
          ..cattleId = _cattleId
          ..vaccineName = _vaccineName
          ..batchNumber = _batchNumber
          ..manufacturer = _manufacturer
          ..date = _administeredDate
          ..notes = _notes
          ..cost = _cost
          ..createdAt = createdAt
          ..updatedAt = DateTime.now();

        if (mounted) {
          if (widget.onSave != null) {
            // Use the onSave callback if provided
            await widget.onSave!(vaccinationRecord);
            if (mounted) {
              setState(() {
                _isSaving = false;
              });
              Navigator.pop(context);
            }
          } else {
            // Return the vaccination object directly
            setState(() {
              _isSaving = false;
            });
            Navigator.pop(context, vaccinationRecord);
          }
        }
      } catch (e) {
        _logger.severe('Error saving vaccination: $e');
        if (mounted) {
          setState(() {
            _isSaving = false;
          });
          if (context.mounted) {
            HealthMessageUtils.showError(context, _getReadableErrorMessage(e));
          }
        }
      }
    }
  }

  // Convert error to user-friendly message
  String _getReadableErrorMessage(dynamic error) {
    final message = error.toString();

    if (message.contains('Vaccination date is required')) {
      return 'Please select a vaccination date';
    }
    else if (message.contains('Vaccine name is required')) {
      return 'Please enter a vaccine name';
    }

    return 'Error saving vaccination. Please check your inputs and try again.';
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.vaccination == null ? 'Add Vaccination' : 'Edit Vaccination'),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.9,
        child: _buildFormContent(),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isSaving ? null : _saveVaccination,
          child: _isSaving
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Save'),
        ),
      ],
    );
  }

  Widget _buildFormContent() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Expanded(
            child: Scrollbar(
              controller: _scrollController,
                    child: SingleChildScrollView(
                      controller: _scrollController,
                      padding: const EdgeInsets.all(16.0),
                      physics: const ClampingScrollPhysics(),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          // Cattle selector/display
                          if (widget.cattle != null && widget.cattle!.length > 1)
                            DropdownButtonFormField<String>(
                              decoration: const InputDecoration(
                                labelText: 'Cattle',
                                border: OutlineInputBorder(),
                                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                prefixIcon: Icon(
                                  Icons.pets,
                                  color: Colors.brown,
                                ),
                              ),
                              value: _cattleId,
                              items: widget.cattle!.map((cattle) {
                                final displayName = '${cattle.name ?? 'Unnamed'} (${cattle.tagId ?? 'No Tag'})';
                                return DropdownMenuItem<String>(
                                  value: cattle.businessId,
                                  child: Text(displayName),
                                );
                              }).toList(),
                              onChanged: _isSaving
                                  ? null
                                  : (value) {
                                      if (value != null) {
                                        setState(() {
                                          _cattleId = value;
                                        });
                                      }
                                    },
                            )
                          else
                            Card(
                              color: Theme.of(context).colorScheme.surface,
                              child: Padding(
                                padding: const EdgeInsets.all(12.0),
                                child: Row(
                                  children: [
                                    const Icon(Icons.pets, color: Colors.brown),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: Text(
                                        _getCattleDisplayName(),
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 16,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          const SizedBox(height: 16),

                          // Vaccine Name
                          TextFormField(
                            initialValue: _vaccineName,
                            decoration: const InputDecoration(
                              labelText: 'Vaccine Name',
                              border: OutlineInputBorder(),
                              hintText: 'Enter vaccine name',
                              contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                              constraints: BoxConstraints(maxHeight: 56),
                              prefixIcon: Icon(
                                Icons.medication_liquid,
                                color: Colors.blue,
                              ),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter vaccine name';
                              }
                              return null;
                            },
                            onChanged: (value) => _vaccineName = value,
                          ),
                          const SizedBox(height: 16),

                          // Date Picker
                          InkWell(
                            onTap: _isSaving
                                ? null
                                : () async {
                                    final context = this.context;
                                    final pickedDate = await showDatePicker(
                                      context: context,
                                      initialDate: _administeredDate,
                                      firstDate: DateTime(2000),
                                      lastDate: DateTime.now(),
                                    );
                                    if (pickedDate != null &&
                                        mounted &&
                                        context.mounted) {
                                      setState(() {
                                        _administeredDate = pickedDate;
                                      });
                                    }
                                  },
                            child: InputDecorator(
                              decoration: const InputDecoration(
                                labelText: 'Administered Date',
                                border: OutlineInputBorder(),
                                prefixIcon: Icon(
                                  Icons.calendar_today,
                                  color: Colors.blue,
                                ),
                                constraints: BoxConstraints(maxHeight: 56),
                                contentPadding: _inputContentPadding,
                              ),
                              child: Text(
                                _administeredDate
                                    .toLocal()
                                    .toString()
                                    .split(' ')[0],
                                style: const TextStyle(fontSize: 16),
                              ),
                            ),
                          ),
                          const SizedBox(height: 16),

                          // Batch Number Field
                          TextFormField(
                            decoration: const InputDecoration(
                              labelText: 'Batch Number',
                              border: OutlineInputBorder(),
                              hintText: 'Enter batch number',
                              contentPadding: _inputContentPadding,
                              constraints: BoxConstraints(maxHeight: 56),
                              prefixIcon: Icon(
                                Icons.numbers,
                                color: Colors.indigo,
                              ),
                            ),
                            initialValue: _batchNumber,
                            textInputAction: TextInputAction.next,
                            enabled: !_isSaving,
                            onSaved: (value) {
                              _batchNumber = value ?? '';
                            },
                          ),
                          const SizedBox(height: 16),

                          // Manufacturer Field
                          TextFormField(
                            decoration: const InputDecoration(
                              labelText: 'Manufacturer',
                              border: OutlineInputBorder(),
                              hintText: 'Enter manufacturer name',
                              contentPadding: _inputContentPadding,
                              constraints: BoxConstraints(maxHeight: 56),
                              prefixIcon: Icon(
                                Icons.factory,
                                color: Colors.brown,
                              ),
                            ),
                            initialValue: _manufacturer,
                            textInputAction: TextInputAction.next,
                            enabled: !_isSaving,
                            onSaved: (value) {
                              _manufacturer = value ?? '';
                            },
                          ),
                          const SizedBox(height: 16),

                          // Cost Field
                          TextFormField(
                            decoration: const InputDecoration(
                              labelText: 'Cost',
                              border: OutlineInputBorder(),
                              prefixText: '\$',
                              hintText: '0.00',
                              contentPadding: _inputContentPadding,
                              constraints: BoxConstraints(maxHeight: 56),
                              prefixIcon: Icon(
                                Icons.attach_money,
                                color: Colors.amber,
                              ),
                            ),
                            initialValue: _cost.toString(),
                            keyboardType: const TextInputType.numberWithOptions(decimal: true),
                            textInputAction: TextInputAction.next,
                            enabled: !_isSaving,
                            onSaved: (value) {
                              _cost = double.tryParse(value ?? '0') ?? 0;
                            },
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return null;
                              }
                              if (double.tryParse(value) == null) {
                                return 'Please enter a valid number';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 16),

                          // Notes Field
                          TextFormField(
                            decoration: const InputDecoration(
                              labelText: 'Notes',
                              border: OutlineInputBorder(),
                              alignLabelWithHint: true,
                              hintText: 'Enter additional notes',
                              contentPadding: EdgeInsets.all(16),
                              prefixIcon: Icon(
                                Icons.note,
                                color: Colors.grey,
                              ),
                            ),
                            initialValue: _notes,
                            maxLines: 3,
                            textInputAction: TextInputAction.done,
                            enabled: !_isSaving,
                            onSaved: (value) {
                              _notes = value ?? '';
                            },
                          ),
                        ],
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }
}
