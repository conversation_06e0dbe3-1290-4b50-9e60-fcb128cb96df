import 'package:flutter/material.dart';
import 'package:isar/isar.dart';
import 'package:uuid/uuid.dart';

part 'event_isar.g.dart';

/// Enum for event types
enum EventType {
  vaccination,
  healthCheckup,
  breeding,
  pregnancyCheck,
  weightMeasurement,
  deworming,
  dryOff,
  calving,
  purchased,
  sold,
  miscellaneous,
  custom,
}

/// Enum for event priorities
enum EventPriority {
  low,
  medium,
  high,
}

/// Embedded class for TimeOfDay since Isar doesn't directly support it
@embedded
class TimeOfDayIsar {
  /// Hour of the day (0-23)
  int? hour;
  
  /// Minute (0-59)
  int? minute;
  
  /// Convert to Flutter's TimeOfDay
  TimeOfDay toTimeOfDay() {
    return TimeOfDay(hour: hour ?? 0, minute: minute ?? 0);
  }
  
  /// Create from Flutter's TimeOfDay
  static TimeOfDayIsar fromTimeOfDay(TimeOfDay time) {
    return TimeOfDayIsar()
      ..hour = time.hour
      ..minute = time.minute;
  }
}

/// Embedded class to support compatibility with CattleEventIsar from Cattle module
@embedded
class EventTypeEmbedded {
  // Stored as a string: 'vaccination', 'healthCheckup', etc.
  String? value;

  /// Convert to EventType enum
  EventType toEventType() {
    switch (value) {
      case 'EventType.vaccination':
        return EventType.vaccination;
      case 'EventType.healthCheckup':
        return EventType.healthCheckup;
      case 'EventType.breeding':
        return EventType.breeding;
      case 'EventType.pregnancyCheck':
        return EventType.pregnancyCheck;
      case 'EventType.weightMeasurement':
      case 'EventType.weighing':
        return EventType.weightMeasurement;
      case 'EventType.alert':
        return EventType.miscellaneous;
      case 'EventType.deworming':
        return EventType.deworming;
      default:
        return EventType.miscellaneous;
    }
  }

  /// Set value from EventType enum
  void fromEventType(EventType type) {
    switch (type) {
      case EventType.vaccination:
        value = 'EventType.vaccination';
        break;
      case EventType.healthCheckup:
        value = 'EventType.healthCheckup';
        break;
      case EventType.breeding:
        value = 'EventType.breeding';
        break;
      case EventType.pregnancyCheck:
        value = 'EventType.pregnancyCheck';
        break;
      case EventType.weightMeasurement:
        value = 'EventType.weighing';
        break;
      case EventType.deworming:
        value = 'EventType.deworming';
        break;
      default:
        value = 'EventType.other';
        break;
    }
  }

  /// Get display name for the type
  String getDisplayName() {
    final eventType = toEventType();
    switch (eventType) {
      case EventType.vaccination:
        return 'Vaccination';
      case EventType.healthCheckup:
        return 'Health Checkup';
      case EventType.breeding:
        return 'Breeding';
      case EventType.pregnancyCheck:
        return 'Pregnancy Check';
      case EventType.weightMeasurement:
        return 'Weighing';
      case EventType.deworming:
        return 'Deworming';
      case EventType.dryOff:
        return 'Dry Off';
      case EventType.calving:
        return 'Calving';
      case EventType.purchased:
        return 'Purchased';
      case EventType.sold:
        return 'Sold';
      case EventType.miscellaneous:
        return 'Miscellaneous';
      case EventType.custom:
        return 'Custom';
    }
  }

  /// Get icon data
  IconData getIcon() {
    final eventType = toEventType();
    switch (eventType) {
      case EventType.vaccination:
        return Icons.medical_services;
      case EventType.healthCheckup:
        return Icons.health_and_safety;
      case EventType.breeding:
        return Icons.favorite;
      case EventType.pregnancyCheck:
        return Icons.pregnant_woman;
      case EventType.weightMeasurement:
        return Icons.monitor_weight;
      case EventType.deworming:
        return Icons.pest_control;
      case EventType.dryOff:
        return Icons.water_drop;
      case EventType.calving:
        return Icons.child_care;
      case EventType.purchased:
        return Icons.shopping_cart;
      case EventType.sold:
        return Icons.monetization_on;
      case EventType.miscellaneous:
      case EventType.custom:
        return Icons.event_note;
    }
  }

  /// Get color for the event type
  Color getColor() {
    final eventType = toEventType();
    switch (eventType) {
      case EventType.vaccination:
        return Colors.blue;
      case EventType.healthCheckup:
        return Colors.green;
      case EventType.breeding:
        return Colors.pink;
      case EventType.pregnancyCheck:
        return Colors.purple;
      case EventType.weightMeasurement:
        return Colors.orange;
      case EventType.deworming:
        return Colors.teal;
      case EventType.dryOff:
        return Colors.lightBlue;
      case EventType.calving:
        return Colors.amber;
      case EventType.purchased:
        return Colors.indigo;
      case EventType.sold:
        return Colors.red;
      case EventType.miscellaneous:
      case EventType.custom:
        return Colors.grey;
    }
  }
}

/// Event model for Isar database
@collection
class EventIsar {
  /// Unique identifier for the event
  Id id = Isar.autoIncrement;

  /// Business ID for the event (UUID)
  @Index(unique: true, replace: true)
  String? businessId;

  /// ID of the cattle this event belongs to
  @Index()
  String? cattleId;

  /// Title of the event
  String? title;

  /// Type of the event
  EventTypeEmbedded? type;

  /// Priority of the event
  @enumerated
  EventPriority priority = EventPriority.medium;

  /// Date when the event occurs
  DateTime? eventDate;

  /// Due date for the event (if applicable)
  DateTime? dueDate;

  /// Time of the event
  TimeOfDayIsar? time;

  /// Notes about the event
  String? notes;

  /// Whether the event is completed
  bool isCompleted = false;

  /// Whether the event is missed
  bool isMissed = false;

  /// When the event was created
  DateTime? createdAt;

  /// When the event was last updated
  DateTime? updatedAt;

  /// Constructor
  EventIsar() {
    businessId = 'event_${const Uuid().v4()}';
    createdAt = DateTime.now();
    updatedAt = DateTime.now();
    isCompleted = false;
    isMissed = false;
  }
}