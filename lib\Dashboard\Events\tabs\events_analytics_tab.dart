import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../controllers/events_controller.dart';
import '../../widgets/universal_state_builder.dart';
import '../../widgets/analytics/kpi_card.dart';
import '../../widgets/analytics/chart_card.dart';
import '../../widgets/analytics/distribution_chart.dart';

/// Events Analytics Tab - Shows comprehensive analytics for events
/// Follows the universal analytics tab pattern with KPI cards and charts
class EventsAnalyticsTab extends StatelessWidget {
  const EventsAnalyticsTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<EventsController>(
      builder: (context, controller, child) {
        return UniversalStateBuilder(
          state: controller.state,
          errorMessage: controller.errorMessage,
          onRetry: () {
            // Stream handles refresh automatically
          },
          child: _buildAnalyticsContent(context, controller),
        );
      },
    );
  }

  Widget _buildAnalyticsContent(BuildContext context, EventsController controller) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // KPI Cards Row 1
          Row(
            children: [
              Expanded(
                child: KpiCard(
                  title: 'Total Events',
                  value: controller.totalEvents.toString(),
                  icon: Icons.event,
                  color: Colors.blue,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: KpiCard(
                  title: 'Upcoming',
                  value: controller.upcomingEvents.toString(),
                  icon: Icons.schedule,
                  color: Colors.orange,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // KPI Cards Row 2
          Row(
            children: [
              Expanded(
                child: KpiCard(
                  title: 'Completed',
                  value: controller.completedEvents.toString(),
                  icon: Icons.check_circle,
                  color: Colors.green,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: KpiCard(
                  title: 'Overdue',
                  value: controller.overdueEvents.toString(),
                  icon: Icons.warning,
                  color: Colors.red,
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Completion Rate Card
          ChartCard(
            title: 'Completion Rate',
            child: Center(
              child: Column(
                children: [
                  Text(
                    '${(controller.completionRate * 100).toStringAsFixed(1)}%',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      color: Colors.green,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Events completed on time',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Event Type Distribution
          ChartCard(
            title: 'Event Type Distribution',
            child: DistributionChart(
              data: controller.eventTypeDistribution,
              colors: const [
                Colors.blue,
                Colors.green,
                Colors.orange,
                Colors.purple,
                Colors.red,
              ],
            ),
          ),
          const SizedBox(height: 16),

          // Event Status Distribution
          ChartCard(
            title: 'Event Status Distribution',
            child: DistributionChart(
              data: controller.eventStatusDistribution,
              colors: const [
                Colors.green,
                Colors.orange,
                Colors.red,
                Colors.grey,
              ],
            ),
          ),
          const SizedBox(height: 16),

          // Additional Analytics Cards
          Row(
            children: [
              Expanded(
                child: KpiCard(
                  title: 'This Week',
                  value: controller.eventsThisWeek.toString(),
                  icon: Icons.calendar_today,
                  color: Colors.indigo,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: KpiCard(
                  title: 'This Month',
                  value: controller.eventsThisMonth.toString(),
                  icon: Icons.calendar_month,
                  color: Colors.teal,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Performance Metrics
          Row(
            children: [
              Expanded(
                child: KpiCard(
                  title: 'Avg Duration',
                  value: '${controller.averageEventDuration.toStringAsFixed(1)} days',
                  icon: Icons.timer,
                  color: Colors.cyan,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: KpiCard(
                  title: 'Efficiency Score',
                  value: '${(controller.eventEfficiencyScore * 100).toStringAsFixed(0)}%',
                  icon: Icons.trending_up,
                  color: Colors.purple,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
