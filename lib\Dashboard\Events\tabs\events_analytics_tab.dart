import 'package:flutter/material.dart';
import '../controllers/events_controller.dart';
import '../../widgets/index.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_layout.dart';

/// Events Analytics Tab - Shows comprehensive analytics for events
/// Follows the cattle analytics tab pattern exactly
class EventsAnalyticsTab extends StatefulWidget {
  final EventsController controller;

  const EventsAnalyticsTab({
    super.key,
    required this.controller,
  });

  @override
  State<EventsAnalyticsTab> createState() => _EventsAnalyticsTabState();
}

class _EventsAnalyticsTabState extends State<EventsAnalyticsTab> {
  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: widget.controller,
      builder: (context, child) {
        // Check if we have data to display
        if (widget.controller.totalEvents == 0) {
          return const Center(
            child: Text('No Events Yet - Add your first event to see analytics.'),
          );
        }

        return _buildAnalyticsContent(context);
      },
    );
  }

  Widget _buildAnalyticsContent(BuildContext context) {
    // Create card data following cattle pattern
    final cardData = [
      {
        'title': 'Total Events',
        'value': widget.controller.totalEvents.toString(),
        'subtitle': 'All time',
        'icon': Icons.event,
        'color': AppColors.eventsColor,
      },
      {
        'title': 'This Month',
        'value': widget.controller.eventsThisMonth.toString(),
        'subtitle': 'Current month',
        'icon': Icons.calendar_month,
        'color': AppColors.eventsColor,
      },
      {
        'title': 'Upcoming',
        'value': widget.controller.upcomingEvents.toString(),
        'subtitle': 'Scheduled',
        'icon': Icons.schedule,
        'color': AppColors.eventsColor,
      },
      {
        'title': 'Completed',
        'value': widget.controller.completedEvents.toString(),
        'subtitle': 'Finished',
        'icon': Icons.check_circle,
        'color': AppColors.eventsColor,
      },
    ];

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // KPI Cards using ResponsiveGrid like cattle module
          ResponsiveGrid.cards(
            children: cardData.map((data) {
              return UniversalInfoCard(
                title: data['title'] as String,
                value: data['value'] as String,
                subtitle: data['subtitle'] as String?,
                icon: data['icon'] as IconData,
                color: data['color'] as Color,
              );
            }).toList(),
          ),

          const SizedBox(height: 24),

          // Analytics Charts Section
          Text(
            'Event Analytics',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.eventsColor,
            ),
          ),
          const SizedBox(height: 16),

          // Simple analytics summary
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Event Summary',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text('Total Events: ${widget.controller.totalEvents}'),
                  Text('Upcoming: ${widget.controller.upcomingEvents}'),
                  Text('Completed: ${widget.controller.completedEvents}'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
