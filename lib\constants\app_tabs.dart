import 'package:flutter/material.dart';
import 'app_colors.dart';

// FAB constants for backward compatibility
const double kFabElevation = 6.0;
const double kFabBorderRadius = 16.0;

// ============================================================================
// COMPATIBILITY LAYER - Legacy classes for backward compatibility
// ============================================================================

/// Legacy empty state theme class for backward compatibility
/// @deprecated Use UniversalTabEmptyState.forTab() instead
class UniversalEmptyStateTheme {
  static Color primary = AppColors.primary;
  static Color secondary = AppColors.secondary;
  static Color accent = AppColors.accent;
  static Color success = AppColors.success;
  static Color warning = AppColors.warning;
  static Color error = AppColors.error;
  static Color info = AppColors.info;
  static Color surface = AppColors.surface;
  static Color background = AppColors.background;
  static Color onPrimary = AppColors.onPrimary;
  static Color onSecondary = AppColors.onSecondary;
  static Color onSurface = AppColors.onSurface;
  static Color onBackground = AppColors.onBackground;

  // Module-specific color getters for backward compatibility
  static Color get cattle => AppColors.cattleHeader;
  static Color get health => AppColors.healthHeader;
  static Color get milk => AppColors.milkHeader;
  static Color get breeding => AppColors.breedingHeader;
  static Color get transactions => AppColors.transactionHeader;
  static Color get weight => AppColors.weightHeader;
  static Color get events => AppColors.eventsHeader;
  static Color get errorRed => AppColors.error;
}

/// Legacy empty state class for backward compatibility
/// @deprecated Use UniversalTabEmptyState.forTab() instead
class UniversalEmptyState extends StatelessWidget {
  final String title;
  final String message;
  final Widget? icon;
  final Widget? action;
  final Color? backgroundColor;
  final Color? textColor;

  const UniversalEmptyState({
    Key? key,
    required this.title,
    required this.message,
    this.icon,
    this.action,
    this.backgroundColor,
    this.textColor,
  }) : super(key: key);

  const UniversalEmptyState.noData({
    Key? key,
    String? title,
    String? message,
    Widget? action,
    Color? backgroundColor,
    Color? textColor,
  }) : this(
          key: key,
          title: title ?? 'No Data Available',
          message: message ?? 'There are no records to display.',
          icon: const Icon(Icons.inbox_outlined, size: 64),
          action: action,
          backgroundColor: backgroundColor,
          textColor: textColor,
        );

  const UniversalEmptyState.noResults({
    Key? key,
    String? title,
    String? message,
    Widget? action,
    Color? backgroundColor,
    Color? textColor,
  }) : this(
          key: key,
          title: title ?? 'No Results Found',
          message: message ?? 'Try adjusting your search or filters.',
          icon: const Icon(Icons.search_off_outlined, size: 64),
          action: action,
          backgroundColor: backgroundColor,
          textColor: textColor,
        );

  const UniversalEmptyState.error({
    Key? key,
    String? title,
    String? message,
    Widget? action,
    Color? backgroundColor,
    Color? textColor,
  }) : this(
          key: key,
          title: title ?? 'Something went wrong',
          message: message ?? 'Please try again later.',
          icon: const Icon(Icons.error_outline, size: 64),
          action: action,
          backgroundColor: backgroundColor,
          textColor: textColor,
        );

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (icon != null) ...[
              IconTheme(
                data: IconThemeData(
                  color: textColor ?? Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                  size: 64,
                ),
                child: icon!,
              ),
              const SizedBox(height: 24),
            ],
            Text(
              title,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: textColor ?? Theme.of(context).colorScheme.onSurface,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              message,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: textColor ?? Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            if (action != null) ...[
              const SizedBox(height: 32),
              action!,
            ],
          ],
        ),
      ),
    );
  }

  // Module-specific factory methods for backward compatibility
  static Widget cattle({
    String? title,
    String? message,
    Widget? action,
  }) {
    return UniversalEmptyState(
      title: title ?? 'No Cattle Records',
      message: message ?? 'Start by adding your first cattle record.',
      icon: Icon(Icons.pets, size: 64, color: UniversalEmptyStateTheme.cattle),
      action: action,
    );
  }

  static Widget health({
    String? title,
    String? message,
    Widget? action,
  }) {
    return UniversalEmptyState(
      title: title ?? 'No Health Records',
      message: message ?? 'Start by adding your first health record.',
      icon: Icon(Icons.medical_services, size: 64, color: UniversalEmptyStateTheme.health),
      action: action,
    );
  }

  static Widget milk({
    String? title,
    String? message,
    Widget? action,
  }) {
    return UniversalEmptyState(
      title: title ?? 'No Milk Records',
      message: message ?? 'Start by adding your first milk record.',
      icon: Icon(Icons.local_drink, size: 64, color: UniversalEmptyStateTheme.milk),
      action: action,
    );
  }

  static Widget weight({
    String? title,
    String? message,
    Widget? action,
  }) {
    return UniversalEmptyState(
      title: title ?? 'No Weight Records',
      message: message ?? 'Start by adding your first weight record.',
      icon: Icon(Icons.monitor_weight, size: 64, color: UniversalEmptyStateTheme.weight),
      action: action,
    );
  }

  static Widget transactions({
    String? title,
    String? message,
    Widget? action,
  }) {
    return UniversalEmptyState(
      title: title ?? 'No Transaction Records',
      message: message ?? 'Start by adding your first transaction record.',
      icon: Icon(Icons.receipt_long, size: 64, color: UniversalEmptyStateTheme.transactions),
      action: action,
    );
  }

  static Widget breeding({
    String? title,
    String? message,
    Widget? action,
  }) {
    return UniversalEmptyState(
      title: title ?? 'No Breeding Records',
      message: message ?? 'Start by adding your first breeding record.',
      icon: Icon(Icons.favorite, size: 64, color: UniversalEmptyStateTheme.breeding),
      action: action,
    );
  }

  static Widget events({
    String? title,
    String? message,
    Widget? action,
  }) {
    return UniversalEmptyState(
      title: title ?? 'No Event Records',
      message: message ?? 'Start by adding your first event record.',
      icon: Icon(Icons.event, size: 64, color: UniversalEmptyStateTheme.events),
      action: action,
    );
  }
}

/// Legacy empty state actions class for backward compatibility
/// @deprecated Use TabEmptyStateActions instead
class EmptyStateActions {
  static Widget addButton({
    required VoidCallback onPressed,
    required String label,
    Color? backgroundColor,
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: const Icon(Icons.add),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor ?? AppColors.primary,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      ),
    );
  }

  static Widget refreshButton({
    required VoidCallback onPressed,
    String? label,
    Color? backgroundColor,
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: const Icon(Icons.refresh),
      label: Text(label ?? 'Refresh'),
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor ?? AppColors.secondary,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      ),
    );
  }

  static Widget addFirstRecord({
    required VoidCallback onPressed,
    String? label,
    Color? backgroundColor,
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: const Icon(Icons.add),
      label: Text(label ?? 'Add First Record'),
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor ?? AppColors.primary,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      ),
    );
  }

  static Widget clearFilters({
    required VoidCallback onPressed,
    String? label,
    Color? backgroundColor,
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: const Icon(Icons.clear),
      label: Text(label ?? 'Clear Filters'),
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor ?? AppColors.secondary,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      ),
    );
  }
}

/// Legacy empty state type enum for backward compatibility
/// @deprecated Use UniversalTabEmptyState.forTab() instead
enum EmptyStateType {
  noData,
  noResults,
  error,
}

/// Legacy empty state widget for backward compatibility
/// @deprecated Use UniversalTabEmptyState.forTab() instead
class EmptyState extends StatelessWidget {
  final EmptyStateType type;
  final String? title;
  final String? message;
  final Widget? action;

  const EmptyState({
    Key? key,
    required this.type,
    this.title,
    this.message,
    this.action,
  }) : super(key: key);

  /// Custom empty state factory constructor for backward compatibility
  factory EmptyState.custom({
    Key? key,
    required String title,
    required String message,
    IconData? icon,
    Widget? action,
  }) {
    return EmptyState(
      key: key,
      type: EmptyStateType.noData,
      title: title,
      message: message,
      action: action,
    );
  }

  @override
  Widget build(BuildContext context) {
    switch (type) {
      case EmptyStateType.noData:
        return UniversalEmptyState.noData(
          title: title,
          message: message,
          action: action,
        );
      case EmptyStateType.noResults:
        return UniversalEmptyState.noResults(
          title: title,
          message: message,
          action: action,
        );
      case EmptyStateType.error:
        return UniversalEmptyState.error(
          title: title,
          message: message,
          action: action,
        );
    }
  }
}

/// Legacy tab item class for backward compatibility
/// @deprecated Use modern TabBar with Tab widgets instead
class TabItem {
  final String label;
  final IconData icon;
  final Widget screen;
  final Color? color;

  const TabItem({
    required this.label,
    required this.icon,
    required this.screen,
    this.color,
  });
}

/// Legacy tab configurations class for backward compatibility
/// @deprecated Use modern TabBar configuration instead
class TabConfigurations {
  static List<TabItem> health() {
    return [
      TabItem(
        label: 'Records',
        icon: Icons.medical_services,
        screen: Container(),
        color: AppColors.primary,
      ),
      TabItem(
        label: 'Analytics',
        icon: Icons.analytics,
        screen: Container(),
        color: AppColors.secondary,
      ),
    ];
  }

  static List<TabItem> transactions() {
    return [
      TabItem(
        label: 'Records',
        icon: Icons.receipt_long,
        screen: Container(),
        color: AppColors.primary,
      ),
      TabItem(
        label: 'Analytics',
        icon: Icons.analytics,
        screen: Container(),
        color: AppColors.secondary,
      ),
    ];
  }

  static List<TabItem> weight() {
    return [
      TabItem(
        label: 'Records',
        icon: Icons.monitor_weight,
        screen: Container(),
        color: AppColors.primary,
      ),
      TabItem(
        label: 'Analytics',
        icon: Icons.analytics,
        screen: Container(),
        color: AppColors.secondary,
      ),
    ];
  }

  static List<TabItem> milk() {
    return [
      TabItem(
        label: 'Records',
        icon: Icons.local_drink,
        screen: Container(),
        color: AppColors.primary,
      ),
      TabItem(
        label: 'Analytics',
        icon: Icons.analytics,
        screen: Container(),
        color: AppColors.secondary,
      ),
    ];
  }

  /// Legacy method for three-tab module configuration
  /// @deprecated Use modern TabBar configuration instead
  static List<TabItem> threeTabModule({
    String? tab1Label,
    IconData? tab1Icon,
    Color? tab1Color,
    String? tab2Label,
    IconData? tab2Icon,
    Color? tab2Color,
    String? tab3Label,
    IconData? tab3Icon,
    Color? tab3Color,
  }) {
    return [
      TabItem(
        label: tab1Label ?? 'Analytics',
        icon: tab1Icon ?? Icons.analytics,
        screen: Container(),
        color: tab1Color ?? AppColors.primary,
      ),
      TabItem(
        label: tab2Label ?? 'Records',
        icon: tab2Icon ?? Icons.list,
        screen: Container(),
        color: tab2Color ?? AppColors.secondary,
      ),
      TabItem(
        label: tab3Label ?? 'Insights',
        icon: tab3Icon ?? Icons.insights,
        screen: Container(),
        color: tab3Color ?? AppColors.info,
      ),
    ];
  }

  /// Creates a tab configuration with custom parameters
  /// @deprecated Use modern TabBar configuration instead
  static Widget createTabConfiguration({
    String? tab1Label,
    IconData? tab1Icon,
    Color? tab1Color,
    String? tab2Label,
    IconData? tab2Icon,
    Color? tab2Color,
    String? tab3Label,
    IconData? tab3Icon,
    Color? tab3Color,
    bool useMulticolor = false,
    required List<Widget> children,
  }) {
    return DefaultTabController(
      length: children.length,
      child: Column(
        children: [
          TabBar(
            tabs: [
              if (tab1Label != null) Tab(text: tab1Label, icon: Icon(tab1Icon)),
              if (tab2Label != null) Tab(text: tab2Label, icon: Icon(tab2Icon)),
              if (tab3Label != null) Tab(text: tab3Label, icon: Icon(tab3Icon)),
            ],
          ),
          Expanded(
            child: TabBarView(children: children),
          ),
        ],
      ),
    );
  }

  /// Legacy method for two-tab detail configuration
  /// @deprecated Use modern TabBar configuration instead
  static List<TabItem> twoTabDetail({
    String? tab1Label,
    IconData? tab1Icon,
    Color? tab1Color,
    String? tab2Label,
    IconData? tab2Icon,
    Color? tab2Color,
  }) {
    return [
      TabItem(
        label: tab1Label ?? 'Records',
        icon: tab1Icon ?? Icons.list,
        screen: Container(),
        color: tab1Color ?? AppColors.primary,
      ),
      TabItem(
        label: tab2Label ?? 'Analytics',
        icon: tab2Icon ?? Icons.analytics,
        screen: Container(),
        color: tab2Color ?? AppColors.secondary,
      ),
    ];
  }
}

/// Legacy reusable tab bar class for backward compatibility
/// @deprecated Use modern TabBar widget instead
class ReusableTabBar extends StatelessWidget {
  final List<TabItem> tabs;
  final TabController controller;
  final Color? backgroundColor;
  final Color? indicatorColor;
  final Color? labelColor;
  final Color? unselectedLabelColor;

  const ReusableTabBar({
    Key? key,
    required this.tabs,
    required this.controller,
    this.backgroundColor,
    this.indicatorColor,
    this.labelColor,
    this.unselectedLabelColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: backgroundColor ?? AppColors.surface,
      child: TabBar(
        controller: controller,
        tabs: tabs.map((tab) => Tab(
          icon: Icon(tab.icon),
          text: tab.label,
        )).toList(),
        indicatorColor: indicatorColor ?? AppColors.primary,
        labelColor: labelColor ?? AppColors.primary,
        unselectedLabelColor: unselectedLabelColor ?? AppColors.onSurface.withValues(alpha: 0.6),
        indicatorWeight: 3,
        labelStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// Legacy method for backward compatibility
  /// @deprecated Use modern TabBar widget instead
  static Widget controlled({
    required List<TabItem> tabs,
    required TabController controller,
    Color? backgroundColor,
    Color? indicatorColor,
    Color? labelColor,
    Color? unselectedLabelColor,
    String? tab1Label,
    IconData? tab1Icon,
    Color? tab1Color,
    String? tab2Label,
    IconData? tab2Icon,
    Color? tab2Color,
    String? tab3Label,
    IconData? tab3Icon,
    Color? tab3Color,
    bool useMulticolor = false,
  }) {
    return ReusableTabBar(
      tabs: tabs,
      controller: controller,
      backgroundColor: backgroundColor,
      indicatorColor: indicatorColor,
      labelColor: labelColor,
      unselectedLabelColor: unselectedLabelColor,
    );
  }
}

/// Legacy universal list builder class for backward compatibility
/// @deprecated Use ListView.builder or other list widgets directly
class UniversalListBuilder<T> extends StatelessWidget {
  final List<T> items;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final Widget? emptyState;
  final Widget? emptyStateWidget;
  final bool shrinkWrap;
  final ScrollPhysics? physics;
  final EdgeInsetsGeometry? padding;
  final Future<void> Function()? onRefresh;

  const UniversalListBuilder({
    Key? key,
    required this.items,
    required this.itemBuilder,
    this.emptyState,
    this.emptyStateWidget,
    this.shrinkWrap = false,
    this.physics,
    this.padding,
    this.onRefresh,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (items.isEmpty) {
      return emptyStateWidget ?? emptyState ?? const UniversalEmptyState.noData();
    }

    Widget listView = ListView.builder(
      shrinkWrap: shrinkWrap,
      physics: physics,
      padding: padding,
      itemCount: items.length,
      itemBuilder: (context, index) => itemBuilder(context, items[index], index),
    );

    if (onRefresh != null) {
      return RefreshIndicator(
        onRefresh: onRefresh!,
        child: listView,
      );
    }

    return listView;
  }

  // Module-specific factory methods for backward compatibility
  static Widget weight<T>({
    required List<T> items,
    required Widget Function(BuildContext context, T item, int index) itemBuilder,
    Widget? emptyState,
    Widget? emptyStateWidget,
    bool shrinkWrap = false,
    ScrollPhysics? physics,
    EdgeInsetsGeometry? padding,
    Future<void> Function()? onRefresh,
  }) {
    return UniversalListBuilder<T>(
      items: items,
      itemBuilder: itemBuilder,
      emptyState: emptyState,
      emptyStateWidget: emptyStateWidget,
      shrinkWrap: shrinkWrap,
      physics: physics,
      padding: padding,
      onRefresh: onRefresh,
    );
  }

  static Widget transactions<T>({
    required List<T> items,
    required Widget Function(BuildContext context, T item, int index) itemBuilder,
    Widget? emptyState,
    Widget? emptyStateWidget,
    bool shrinkWrap = false,
    ScrollPhysics? physics,
    EdgeInsetsGeometry? padding,
    Future<void> Function()? onRefresh,
  }) {
    return UniversalListBuilder<T>(
      items: items,
      itemBuilder: itemBuilder,
      emptyState: emptyState,
      emptyStateWidget: emptyStateWidget,
      shrinkWrap: shrinkWrap,
      physics: physics,
      padding: padding,
      onRefresh: onRefresh,
    );
  }

  static Widget milk<T>({
    required List<T> items,
    required Widget Function(BuildContext context, T item, int index) itemBuilder,
    Widget? emptyState,
    Widget? emptyStateWidget,
    bool shrinkWrap = false,
    ScrollPhysics? physics,
    EdgeInsetsGeometry? padding,
    Future<void> Function()? onRefresh,
  }) {
    return UniversalListBuilder<T>(
      items: items,
      itemBuilder: itemBuilder,
      emptyState: emptyState,
      emptyStateWidget: emptyStateWidget,
      shrinkWrap: shrinkWrap,
      physics: physics,
      padding: padding,
      onRefresh: onRefresh,
    );
  }
}

/// Universal Tab and FAB System for the entire app
///
/// Provides consistent tab bar and floating action button patterns with:
/// - Number-based tab configurations (2, 3, 4, or 5 tabs)
/// - Universal FAB behavior and positioning
/// - Flexible theming for any module
/// - Consistent interaction patterns

/// Tab configuration for any screen
class UniversalTabConfig {
  final String label;
  final IconData icon;
  final Color color;
  final bool showFAB;

  const UniversalTabConfig({
    required this.label,
    required this.icon,
    required this.color,
    this.showFAB = false,
  });
}

/// Universal tab configurations based on number of tabs
class UniversalTabConfigs {

  // ============================================================================
  // 2-TAB CONFIGURATIONS
  // ============================================================================

  /// Creates a 2-tab configuration
  /// Common pattern: Analytics + Records
  static List<UniversalTabConfig> twoTabs({
    required List<String> labels,
    required List<IconData> icons,
    required List<Color> colors,
    List<bool>? showFABs,
  }) {
    assert(labels.length == 2, 'Must provide exactly 2 labels');
    assert(icons.length == 2, 'Must provide exactly 2 icons');
    assert(colors.length == 2, 'Must provide exactly 2 colors');

    final fabStates = showFABs ?? [false, true]; // Default: FAB on second tab

    return [
      UniversalTabConfig(
        label: labels[0],
        icon: icons[0],
        color: colors[0],
        showFAB: fabStates[0],
      ),
      UniversalTabConfig(
        label: labels[1],
        icon: icons[1],
        color: colors[1],
        showFAB: fabStates[1],
      ),
    ];
  }

  // ============================================================================
  // 3-TAB CONFIGURATIONS
  // ============================================================================

  /// Creates a 3-tab configuration
  /// Common pattern: Analytics + Records + Insights
  static List<UniversalTabConfig> threeTabs({
    required List<String> labels,
    required List<IconData> icons,
    required List<Color> colors,
    List<bool>? showFABs,
  }) {
    assert(labels.length == 3, 'Must provide exactly 3 labels');
    assert(icons.length == 3, 'Must provide exactly 3 icons');
    assert(colors.length == 3, 'Must provide exactly 3 colors');

    final fabStates = showFABs ?? [false, true, false]; // Default: FAB on second tab

    return [
      UniversalTabConfig(
        label: labels[0],
        icon: icons[0],
        color: colors[0],
        showFAB: fabStates[0],
      ),
      UniversalTabConfig(
        label: labels[1],
        icon: icons[1],
        color: colors[1],
        showFAB: fabStates[1],
      ),
      UniversalTabConfig(
        label: labels[2],
        icon: icons[2],
        color: colors[2],
        showFAB: fabStates[2],
      ),
    ];
  }

  // ============================================================================
  // 4-TAB CONFIGURATIONS
  // ============================================================================

  /// Creates a 4-tab configuration
  /// Common pattern: Analytics + Records + Custom + Insights
  static List<UniversalTabConfig> fourTabs({
    required List<String> labels,
    required List<IconData> icons,
    required List<Color> colors,
    List<bool>? showFABs,
  }) {
    assert(labels.length == 4, 'Must provide exactly 4 labels');
    assert(icons.length == 4, 'Must provide exactly 4 icons');
    assert(colors.length == 4, 'Must provide exactly 4 colors');

    final fabStates = showFABs ?? [false, true, false, false]; // Default: FAB on second tab

    return [
      UniversalTabConfig(
        label: labels[0],
        icon: icons[0],
        color: colors[0],
        showFAB: fabStates[0],
      ),
      UniversalTabConfig(
        label: labels[1],
        icon: icons[1],
        color: colors[1],
        showFAB: fabStates[1],
      ),
      UniversalTabConfig(
        label: labels[2],
        icon: icons[2],
        color: colors[2],
        showFAB: fabStates[2],
      ),
      UniversalTabConfig(
        label: labels[3],
        icon: icons[3],
        color: colors[3],
        showFAB: fabStates[3],
      ),
    ];
  }

  // ============================================================================
  // 5-TAB CONFIGURATIONS
  // ============================================================================

  /// Creates a 5-tab configuration (maximum supported)
  /// Common pattern: Analytics + Records + Custom1 + Custom2 + Insights
  static List<UniversalTabConfig> fiveTabs({
    required List<String> labels,
    required List<IconData> icons,
    required List<Color> colors,
    List<bool>? showFABs,
  }) {
    assert(labels.length == 5, 'Must provide exactly 5 labels');
    assert(icons.length == 5, 'Must provide exactly 5 icons');
    assert(colors.length == 5, 'Must provide exactly 5 colors');

    final fabStates = showFABs ?? [false, true, false, false, false]; // Default: FAB on second tab

    return [
      UniversalTabConfig(
        label: labels[0],
        icon: icons[0],
        color: colors[0],
        showFAB: fabStates[0],
      ),
      UniversalTabConfig(
        label: labels[1],
        icon: icons[1],
        color: colors[1],
        showFAB: fabStates[1],
      ),
      UniversalTabConfig(
        label: labels[2],
        icon: icons[2],
        color: colors[2],
        showFAB: fabStates[2],
      ),
      UniversalTabConfig(
        label: labels[3],
        icon: icons[3],
        color: colors[3],
        showFAB: fabStates[3],
      ),
      UniversalTabConfig(
        label: labels[4],
        icon: icons[4],
        color: colors[4],
        showFAB: fabStates[4],
      ),
    ];
  }
}

/// Universal Tab Bar Widget
class UniversalTabBar extends StatelessWidget {
  final List<UniversalTabConfig> tabConfigs;
  final TabController controller;
  final Color? indicatorColor;
  final bool isScrollable;

  const UniversalTabBar({
    Key? key,
    required this.tabConfigs,
    required this.controller,
    this.indicatorColor,
    this.isScrollable = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, child) {
        final currentIndex = controller.index;
        return TabBar(
          controller: controller,
          isScrollable: isScrollable,
          indicatorColor: indicatorColor ?? AppColors.primary,
          labelColor: null, // We'll handle colors per tab
          unselectedLabelColor: null, // We'll handle colors per tab
          tabs: tabConfigs.asMap().entries.map((entry) {
            final index = entry.key;
            final config = entry.value;
            final isActive = currentIndex == index;
            final activeColor = config.color;
            final inactiveColor = Color.lerp(config.color, Colors.grey[400]!, 0.6) ?? 
                                 config.color.withValues(alpha: 0.4);

            return Tab(
              icon: Icon(
                config.icon,
                color: isActive ? activeColor : inactiveColor,
              ),
              child: Text(
                config.label,
                style: TextStyle(
                  color: isActive ? activeColor : inactiveColor,
                  fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            );
          }).toList(),
        );
      },
    );
  }

  // Factory constructors based on number of tabs
  factory UniversalTabBar.twoTabs({
    required TabController controller,
    required List<String> labels,
    required List<IconData> icons,
    required List<Color> colors,
    List<bool>? showFABs,
    Color? indicatorColor,
  }) {
    return UniversalTabBar(
      tabConfigs: UniversalTabConfigs.twoTabs(
        labels: labels,
        icons: icons,
        colors: colors,
        showFABs: showFABs,
      ),
      controller: controller,
      indicatorColor: indicatorColor,
      isScrollable: false,
    );
  }

  factory UniversalTabBar.threeTabs({
    required TabController controller,
    required List<String> labels,
    required List<IconData> icons,
    required List<Color> colors,
    List<bool>? showFABs,
    Color? indicatorColor,
  }) {
    return UniversalTabBar(
      tabConfigs: UniversalTabConfigs.threeTabs(
        labels: labels,
        icons: icons,
        colors: colors,
        showFABs: showFABs,
      ),
      controller: controller,
      indicatorColor: indicatorColor,
      isScrollable: false,
    );
  }

  factory UniversalTabBar.fourTabs({
    required TabController controller,
    required List<String> labels,
    required List<IconData> icons,
    required List<Color> colors,
    List<bool>? showFABs,
    Color? indicatorColor,
  }) {
    return UniversalTabBar(
      tabConfigs: UniversalTabConfigs.fourTabs(
        labels: labels,
        icons: icons,
        colors: colors,
        showFABs: showFABs,
      ),
      controller: controller,
      indicatorColor: indicatorColor,
      isScrollable: true, // 4+ tabs should be scrollable
    );
  }

  factory UniversalTabBar.fiveTabs({
    required TabController controller,
    required List<String> labels,
    required List<IconData> icons,
    required List<Color> colors,
    List<bool>? showFABs,
    Color? indicatorColor,
  }) {
    return UniversalTabBar(
      tabConfigs: UniversalTabConfigs.fiveTabs(
        labels: labels,
        icons: icons,
        colors: colors,
        showFABs: showFABs,
      ),
      controller: controller,
      indicatorColor: indicatorColor,
      isScrollable: true, // 5 tabs should be scrollable
    );
  }
}

/// Universal FAB System - Completely module-agnostic
class UniversalFAB {

  /// Creates a standard add FAB with consistent styling
  static FloatingActionButton add({
    required VoidCallback onPressed,
    String tooltip = 'Add',
    Color? backgroundColor,
    bool mini = false,
  }) {
    return FloatingActionButton(
      onPressed: onPressed,
      tooltip: tooltip,
      mini: mini,
      elevation: kFabElevation,
      backgroundColor: backgroundColor ?? AppColors.primary,
      foregroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(kFabBorderRadius),
      ),
      child: const Icon(Icons.add),
    );
  }

  /// Creates a save FAB
  static FloatingActionButton save({
    required VoidCallback onPressed,
    String tooltip = 'Save',
    Color? backgroundColor,
  }) {
    return FloatingActionButton(
      onPressed: onPressed,
      tooltip: tooltip,
      elevation: kFabElevation,
      backgroundColor: backgroundColor ?? AppColors.primary,
      foregroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(kFabBorderRadius),
      ),
      child: const Icon(Icons.save),
    );
  }

  /// Creates an edit FAB
  static FloatingActionButton edit({
    required VoidCallback onPressed,
    String tooltip = 'Edit',
    Color? backgroundColor,
  }) {
    return FloatingActionButton(
      onPressed: onPressed,
      tooltip: tooltip,
      elevation: kFabElevation,
      backgroundColor: backgroundColor ?? AppColors.primary,
      foregroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(kFabBorderRadius),
      ),
      child: const Icon(Icons.edit),
    );
  }

  /// Creates a delete FAB
  static FloatingActionButton delete({
    required VoidCallback onPressed,
    String tooltip = 'Delete',
  }) {
    return FloatingActionButton(
      onPressed: onPressed,
      tooltip: tooltip,
      elevation: kFabElevation,
      backgroundColor: Colors.red,
      foregroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(kFabBorderRadius),
      ),
      child: const Icon(Icons.delete),
    );
  }

  /// Creates an extended FAB
  static FloatingActionButton extended({
    required VoidCallback onPressed,
    required Widget label,
    required Widget icon,
    String? tooltip,
    Color? backgroundColor,
  }) {
    return FloatingActionButton.extended(
      onPressed: onPressed,
      label: label,
      icon: icon,
      tooltip: tooltip,
      elevation: kFabElevation,
      backgroundColor: backgroundColor ?? AppColors.primary,
      foregroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(kFabBorderRadius),
      ),
    );
  }
}

/// Universal Tab Screen System
/// Combines tab bar and FAB management in one widget
class UniversalTabManager extends StatelessWidget {
  final List<UniversalTabConfig> tabConfigs;
  final TabController controller;
  final List<Widget> tabViews;
  final Color? indicatorColor;
  final bool isScrollable;

  const UniversalTabManager({
    Key? key,
    required this.tabConfigs,
    required this.controller,
    required this.tabViews,
    this.indicatorColor,
    this.isScrollable = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Universal Tab Bar
        UniversalTabBar(
          tabConfigs: tabConfigs,
          controller: controller,
          indicatorColor: indicatorColor,
          isScrollable: isScrollable,
        ),
        // Tab Views
        Expanded(
          child: TabBarView(
            controller: controller,
            children: tabViews,
          ),
        ),
      ],
    );
  }

  /// Get the current FAB based on selected tab
  /// Returns a FAB if the current tab should show one
  Widget? getCurrentFAB({
    VoidCallback? onPressed,
    String? tooltip,
    Color? backgroundColor,
  }) {
    if (controller.index < 0 || controller.index >= tabConfigs.length) {
      return null;
    }

    final currentConfig = tabConfigs[controller.index];
    if (!currentConfig.showFAB || onPressed == null) {
      return null;
    }

    return UniversalFAB.add(
      onPressed: onPressed,
      tooltip: tooltip ?? 'Add',
      backgroundColor: backgroundColor ?? currentConfig.color,
    );
  }

  // Factory constructors based on number of tabs
  factory UniversalTabManager.twoTabs({
    required TabController controller,
    required List<Widget> tabViews,
    required List<String> labels,
    required List<IconData> icons,
    required List<Color> colors,
    List<bool>? showFABs,
    Color? indicatorColor,
  }) {
    return UniversalTabManager(
      tabConfigs: UniversalTabConfigs.twoTabs(
        labels: labels,
        icons: icons,
        colors: colors,
        showFABs: showFABs,
      ),
      controller: controller,
      tabViews: tabViews,
      indicatorColor: indicatorColor,
      isScrollable: false,
    );
  }

  factory UniversalTabManager.threeTabs({
    required TabController controller,
    required List<Widget> tabViews,
    required List<String> labels,
    required List<IconData> icons,
    required List<Color> colors,
    List<bool>? showFABs,
    Color? indicatorColor,
  }) {
    return UniversalTabManager(
      tabConfigs: UniversalTabConfigs.threeTabs(
        labels: labels,
        icons: icons,
        colors: colors,
        showFABs: showFABs,
      ),
      controller: controller,
      tabViews: tabViews,
      indicatorColor: indicatorColor,
      isScrollable: false,
    );
  }

  factory UniversalTabManager.fourTabs({
    required TabController controller,
    required List<Widget> tabViews,
    required List<String> labels,
    required List<IconData> icons,
    required List<Color> colors,
    List<bool>? showFABs,
    Color? indicatorColor,
  }) {
    return UniversalTabManager(
      tabConfigs: UniversalTabConfigs.fourTabs(
        labels: labels,
        icons: icons,
        colors: colors,
        showFABs: showFABs,
      ),
      controller: controller,
      tabViews: tabViews,
      indicatorColor: indicatorColor,
      isScrollable: true, // 4+ tabs should be scrollable
    );
  }

  factory UniversalTabManager.fiveTabs({
    required TabController controller,
    required List<Widget> tabViews,
    required List<String> labels,
    required List<IconData> icons,
    required List<Color> colors,
    List<bool>? showFABs,
    Color? indicatorColor,
  }) {
    return UniversalTabManager(
      tabConfigs: UniversalTabConfigs.fiveTabs(
        labels: labels,
        icons: icons,
        colors: colors,
        showFABs: showFABs,
      ),
      controller: controller,
      tabViews: tabViews,
      indicatorColor: indicatorColor,
      isScrollable: true, // 5 tabs should be scrollable
    );
  }
}

/// Universal Empty State System for Tab-Based Screens
///
/// Provides empty states that automatically adapt to the current tab's color
/// and theme, ensuring consistent visual design across all modules.
class UniversalTabEmptyState extends StatelessWidget {
  final String title;
  final String message;
  final IconData? icon;
  final Color tabColor;
  final Widget? action;
  final int tabIndex;
  final String moduleName;

  const UniversalTabEmptyState({
    super.key,
    required this.title,
    required this.message,
    required this.tabColor,
    required this.tabIndex,
    required this.moduleName,
    this.icon,
    this.action,
  });

  /// Factory constructor that automatically determines the appropriate icon
  /// based on the tab index and module
  factory UniversalTabEmptyState.forTab({
    required String title,
    required String message,
    required Color tabColor,
    required int tabIndex,
    required String moduleName,
    Widget? action,
  }) {
    IconData defaultIcon;

    // Determine icon based on tab index (common pattern across modules)
    switch (tabIndex) {
      case 0: // Analytics tab
        defaultIcon = Icons.analytics_outlined;
        break;
      case 1: // Records tab
        defaultIcon = Icons.list_alt_outlined;
        break;
      case 2: // Insights tab
        defaultIcon = Icons.lightbulb_outlined;
        break;
      case 3: // Additional tab
        defaultIcon = Icons.extension_outlined;
        break;
      case 4: // Additional tab
        defaultIcon = Icons.more_horiz_outlined;
        break;
      default:
        defaultIcon = Icons.inbox_outlined;
    }

    return UniversalTabEmptyState(
      title: title,
      message: message,
      tabColor: tabColor,
      tabIndex: tabIndex,
      moduleName: moduleName,
      icon: defaultIcon,
      action: action,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Icon with tab color
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: tabColor.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                size: 64,
                color: tabColor,
              ),
            ),

            const SizedBox(height: 24),

            // Title
            Text(
              title,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: tabColor,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 12),

            // Message
            Text(
              message,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),

            // Action button (if provided)
            if (action != null) ...[
              const SizedBox(height: 32),
              action!,
            ],
          ],
        ),
      ),
    );
  }
}

/// Pre-built action buttons for empty states that match tab colors
class TabEmptyStateActions {
  /// Creates an "Add First Record" button with tab color
  static Widget addFirstRecord({
    required VoidCallback onPressed,
    required Color tabColor,
    String text = 'Add First Record',
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: const Icon(Icons.add),
      label: Text(text),
      style: ElevatedButton.styleFrom(
        backgroundColor: tabColor,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  /// Creates a "Clear Filters" button with tab color
  static Widget clearFilters({
    required VoidCallback onPressed,
    required Color tabColor,
    String text = 'Clear Filters',
  }) {
    return OutlinedButton.icon(
      onPressed: onPressed,
      icon: const Icon(Icons.clear_all),
      label: Text(text),
      style: OutlinedButton.styleFrom(
        foregroundColor: tabColor,
        side: BorderSide(color: tabColor),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  /// Creates a "Retry" button with tab color
  static Widget retry({
    required VoidCallback onPressed,
    required Color tabColor,
    String text = 'Try Again',
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: const Icon(Icons.refresh),
      label: Text(text),
      style: ElevatedButton.styleFrom(
        backgroundColor: tabColor,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  /// Creates an "Add" button with tab color (alias for addFirstRecord)
  static Widget add({
    required VoidCallback onPressed,
    String label = 'Add Record',
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: const Icon(Icons.add),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }
}
