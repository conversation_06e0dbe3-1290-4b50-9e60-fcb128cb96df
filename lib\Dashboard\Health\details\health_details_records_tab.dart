/*
import 'package:flutter/material.dart';
import '../models/health_record_isar.dart';
import '../../Cattle/models/cattle_isar.dart';

import 'health_details_screen.dart';
import 'package:intl/intl.dart';
import '../../../constants/app_tabs.dart';

class HealthDetailsRecordsTab extends StatefulWidget {
  final HealthRecordIsar healthRecord;
  final CattleIsar? cattle;
  final List<HealthRecordIsar> relatedRecords;

  const HealthDetailsRecordsTab({
    Key? key,
    required this.healthRecord,
    this.cattle,
    required this.relatedRecords,
  }) : super(key: key);

  @override
  State<HealthDetailsRecordsTab> createState() => _HealthDetailsRecordsTabState();
}

class _HealthDetailsRecordsTabState extends State<HealthDetailsRecordsTab>
    with AutomaticKeepAliveClientMixin {

  String _searchQuery = '';
  String _selectedType = 'All';
  String _selectedStatus = 'All';

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    final filteredRecords = _getFilteredRecords();

    return Column(
      children: [
        // Search and Filter Section
        _buildSearchAndFilters(),
        
        // Records List
        Expanded(
          child: filteredRecords.isEmpty
              ? UniversalEmptyState.health(
                  title: _searchQuery.isNotEmpty || _selectedType != 'All' || _selectedStatus != 'All'
                      ? 'No Matching Records'
                      : 'No Health Records',
                  message: _searchQuery.isNotEmpty || _selectedType != 'All' || _selectedStatus != 'All'
                      ? 'Try adjusting your search or filter criteria'
                      : 'Health records for ${widget.cattle?.name ?? 'this cattle'} will appear here',
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16.0),
                  itemCount: filteredRecords.length,
                  itemBuilder: (context, index) {
                    final record = filteredRecords[index];
                    final isCurrentRecord = record.businessId == widget.healthRecord.businessId;
                    return _buildRecordCard(record, isCurrentRecord);
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildSearchAndFilters() {
    final recordTypes = ['All', ...widget.relatedRecords
        .map((r) => r.recordType ?? 'Unknown')
        .toSet()
        .toList()..sort()];
    
    final statuses = ['All', ...widget.relatedRecords
        .map((r) => r.status ?? 'Unknown')
        .toSet()
        .toList()..sort()];

    return Card(
      margin: const EdgeInsets.all(16.0),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Search Bar
            TextField(
              decoration: const InputDecoration(
                hintText: 'Search health records...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() => _searchQuery = value);
              },
            ),
            const SizedBox(height: 16),
            
            // Filters
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedType,
                    decoration: const InputDecoration(
                      labelText: 'Type',
                      border: OutlineInputBorder(),
                    ),
                    items: recordTypes.map((type) => DropdownMenuItem(
                      value: type,
                      child: Text(type),
                    )).toList(),
                    onChanged: (value) {
                      setState(() => _selectedType = value ?? 'All');
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedStatus,
                    decoration: const InputDecoration(
                      labelText: 'Status',
                      border: OutlineInputBorder(),
                    ),
                    items: statuses.map((status) => DropdownMenuItem(
                      value: status,
                      child: Text(status),
                    )).toList(),
                    onChanged: (value) {
                      setState(() => _selectedStatus = value ?? 'All');
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecordCard(HealthRecordIsar record, bool isCurrentRecord) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: isCurrentRecord ? 4 : 1,
      color: isCurrentRecord ? UniversalEmptyStateTheme.health.withValues(alpha: 0.1) : null,
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getRecordTypeColor(record.recordType),
          child: Icon(
            _getRecordTypeIcon(record.recordType),
            color: Colors.white,
            size: 20,
          ),
        ),
        title: Row(
          children: [
            Expanded(
              child: Text(
                record.recordType ?? 'Health Record',
                style: TextStyle(
                  fontWeight: isCurrentRecord ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ),
            if (isCurrentRecord)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: UniversalEmptyStateTheme.health,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'Current',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Date: ${_formatDate(record.date)}'),
            if (record.description != null && record.description!.isNotEmpty)
              Text(
                'Description: ${record.description}',
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            if (record.treatment != null && record.treatment!.isNotEmpty)
              Text(
                'Treatment: ${record.treatment}',
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _getStatusIcon(record.status),
              color: _getStatusColor(record.status),
              size: 20,
            ),
            const SizedBox(height: 4),
            Text(
              record.status ?? 'Unknown',
              style: TextStyle(
                fontSize: 10,
                color: _getStatusColor(record.status),
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        onTap: isCurrentRecord ? null : () {
          // Navigate to this record's detail screen
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => HealthDetailScreen(
                healthRecord: record,
                title: record.recordType ?? 'Health Record',
              ),
            ),
          );
        },
      ),
    );
  }

  List<HealthRecordIsar> _getFilteredRecords() {
    List<HealthRecordIsar> filtered = List.from(widget.relatedRecords);

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((record) {
        final searchLower = _searchQuery.toLowerCase();
        return (record.recordType?.toLowerCase().contains(searchLower) ?? false) ||
               (record.description?.toLowerCase().contains(searchLower) ?? false) ||
               (record.treatment?.toLowerCase().contains(searchLower) ?? false) ||
               (record.status?.toLowerCase().contains(searchLower) ?? false);
      }).toList();
    }

    // Apply type filter
    if (_selectedType != 'All') {
      filtered = filtered.where((record) => record.recordType == _selectedType).toList();
    }

    // Apply status filter
    if (_selectedStatus != 'All') {
      filtered = filtered.where((record) => record.status == _selectedStatus).toList();
    }

    // Sort by date (newest first)
    filtered.sort((a, b) => (b.date ?? DateTime.now()).compareTo(a.date ?? DateTime.now()));

    return filtered;
  }

  IconData _getRecordTypeIcon(String? recordType) {
    switch (recordType?.toLowerCase()) {
      case 'vaccination':
        return Icons.vaccines;
      case 'treatment':
        return Icons.healing;
      case 'checkup':
        return Icons.medical_services;
      case 'surgery':
        return Icons.local_hospital;
      default:
        return Icons.health_and_safety;
    }
  }

  Color _getRecordTypeColor(String? recordType) {
    switch (recordType?.toLowerCase()) {
      case 'vaccination':
        return Colors.green;
      case 'treatment':
        return Colors.orange;
      case 'checkup':
        return Colors.blue;
      case 'surgery':
        return Colors.red;
      default:
        return UniversalEmptyStateTheme.health;
    }
  }

  IconData _getStatusIcon(String? status) {
    switch (status?.toLowerCase()) {
      case 'active':
        return Icons.pending;
      case 'completed':
        return Icons.check_circle;
      case 'cancelled':
        return Icons.cancel;
      default:
        return Icons.help_outline;
    }
  }

  Color _getStatusColor(String? status) {
    switch (status?.toLowerCase()) {
      case 'active':
        return Colors.orange;
      case 'completed':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'Unknown';
    return DateFormat('MMM dd, yyyy').format(date);
  }
}
*/
