warning: in the working copy of 'lib/Dashboard/Breeding/dialogs/breeding_form_dialog.dart', LF will be replaced by CR<PERSON> the next time Git touches it
warning: in the working copy of 'lib/Dashboard/Breeding/dialogs/delivery_form_dialog.dart', LF will be replaced by <PERSON><PERSON> the next time Git touches it
warning: in the working copy of 'lib/Dashboard/Breeding/dialogs/pregnancy_form_dialog.dart', LF will be replaced by <PERSON><PERSON> the next time Git touches it
warning: in the working copy of 'lib/Dashboard/Breeding/screens/breeding_records_screen.dart', LF will be replaced by <PERSON><PERSON> the next time Git touches it
warning: in the working copy of 'lib/Dashboard/Breeding/screens/breeding_screen.dart', LF will be replaced by <PERSON><PERSON> the next time Git touches it
warning: in the working copy of 'lib/Dashboard/Breeding/screens/heat_calendar_screen.dart', LF will be replaced by CR<PERSON> the next time Git touches it
warning: in the working copy of 'lib/Dashboard/Breeding/screens/pregnancy_records_screen.dart', LF will be replaced by <PERSON><PERSON> the next time Git touches it
warning: in the working copy of 'lib/Dashboard/Cattle/cattle_tabs/breeding/breeding_view.dart', L<PERSON> will be replaced by <PERSON><PERSON> the next time Git touches it
warning: in the working copy of 'lib/Dashboard/Cattle/cattle_tabs/breeding/delivery_view.dart', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'lib/Dashboard/Cattle/cattle_tabs/breeding/pregnancy_view.dart', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'lib/Dashboard/Cattle/cattle_tabs/health_tab.dart', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'lib/Dashboard/Cattle/models/animal_type.dart', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'lib/Dashboard/Cattle/models/cattle.dart', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'lib/Dashboard/Cattle/models/health_record.dart', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'lib/Dashboard/Cattle/screens/cattle_detail_screen.dart', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'lib/Dashboard/Events/events_tabs/all_events_tab.dart', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'lib/Dashboard/Events/screens/events_screen.dart', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'lib/Dashboard/Farm Setup/screens/animal_types_screen.dart', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'lib/Dashboard/Farm Setup/screens/farm_info_screen.dart', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'lib/Dashboard/Farm Setup/screens/gestation_settings_screen.dart', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'lib/Dashboard/Farm Setup/screens/milk_settings_screen.dart', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'lib/Dashboard/Health/dialogs/treatment_form_dialog.dart', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'lib/Dashboard/Health/screens/health_records_list.dart', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'lib/Dashboard/Health/screens/health_screen.dart', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'lib/Dashboard/Health/screens/treatments_screen.dart', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'lib/Dashboard/Health/screens/vaccinations_screen.dart', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'lib/Dashboard/Milk Records/screens/milk_records_screen.dart', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'lib/Dashboard/Reports/screens/pregnancies_report_screen.dart', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'lib/Dashboard/dashboard_screen.dart', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'lib/Dashboard/widgets/farm_selection_drawer.dart', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'lib/main.dart', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'lib/utils/navigation_utils.dart', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'windows/flutter/generated_plugin_registrant.cc', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'windows/flutter/generated_plugin_registrant.h', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'windows/flutter/generated_plugins.cmake', LF will be replaced by CRLF the next time Git touches it
D	analysis_output.txt
M	lib/Dashboard/Breeding/dialogs/breeding_form_dialog.dart
M	lib/Dashboard/Breeding/dialogs/delivery_form_dialog.dart
M	lib/Dashboard/Breeding/dialogs/pregnancy_form_dialog.dart
M	lib/Dashboard/Breeding/models/breeding_record.dart
M	lib/Dashboard/Breeding/models/pregnancy_record.dart
M	lib/Dashboard/Breeding/screens/breeding_records_screen.dart
M	lib/Dashboard/Breeding/screens/breeding_screen.dart
M	lib/Dashboard/Breeding/screens/heat_calendar_screen.dart
M	lib/Dashboard/Breeding/screens/pregnancy_records_screen.dart
M	lib/Dashboard/Cattle/cattle_tabs/breeding/breeding_view.dart
M	lib/Dashboard/Cattle/cattle_tabs/breeding/delivery_view.dart
M	lib/Dashboard/Cattle/cattle_tabs/breeding/pregnancy_view.dart
M	lib/Dashboard/Cattle/cattle_tabs/health_tab.dart
M	lib/Dashboard/Cattle/cattle_tabs/milk_tab.dart
M	lib/Dashboard/Cattle/cattle_tabs/overview_tab.dart
M	lib/Dashboard/Cattle/models/animal_type.dart
M	lib/Dashboard/Cattle/models/breed_category.dart
M	lib/Dashboard/Cattle/models/cattle.dart
M	lib/Dashboard/Cattle/models/cattle_event.dart
M	lib/Dashboard/Cattle/models/cattle_milk_record.dart
M	lib/Dashboard/Cattle/models/health_record.dart
M	lib/Dashboard/Cattle/models/medication.dart
M	lib/Dashboard/Cattle/models/vaccination.dart
M	lib/Dashboard/Cattle/screens/cattle_detail_screen.dart
M	lib/Dashboard/Cattle/screens/cattle_management_screen.dart
M	lib/Dashboard/Cattle/screens/cattle_records_screen.dart
M	lib/Dashboard/Cattle/screens/qr_code_scanner_screen.dart
M	lib/Dashboard/Cattle/services/cattle_event_service.dart
M	lib/Dashboard/Cattle/services/health_service.dart
M	lib/Dashboard/Cattle/widgets/card_header.dart
M	lib/Dashboard/Cattle/widgets/delivery_history_card.dart
M	lib/Dashboard/Cattle/widgets/eligibility_card.dart
M	lib/Dashboard/Cattle/widgets/history_card.dart
M	lib/Dashboard/Cattle/widgets/info_row.dart
M	lib/Dashboard/Cattle/widgets/pregnancy_history_card.dart
M	lib/Dashboard/Cattle/widgets/stat_item.dart
M	lib/Dashboard/Cattle/widgets/stats_card.dart
M	lib/Dashboard/Events/dialogs/event_form_dialog.dart
M	lib/Dashboard/Events/events_tabs/all_events_tab.dart
M	lib/Dashboard/Events/events_tabs/event_alerts_tab.dart
M	lib/Dashboard/Events/events_tabs/event_history_tab.dart
M	lib/Dashboard/Events/screens/events_screen.dart
M	lib/Dashboard/Farm Setup/models/farm.dart
M	lib/Dashboard/Farm Setup/models/milk_settings.dart
M	lib/Dashboard/Farm Setup/screens/alerts_settings_screen.dart
M	lib/Dashboard/Farm Setup/screens/animal_types_screen.dart
M	lib/Dashboard/Farm Setup/screens/cattle_breeds_screen.dart
M	lib/Dashboard/Farm Setup/screens/data_backup_screen.dart
M	lib/Dashboard/Farm Setup/screens/event_types_screen.dart
M	lib/Dashboard/Farm Setup/screens/expense_categories_screen.dart
M	lib/Dashboard/Farm Setup/screens/farm_info_screen.dart
M	lib/Dashboard/Farm Setup/screens/gestation_settings_screen.dart
M	lib/Dashboard/Farm Setup/screens/income_categories_screen.dart
M	lib/Dashboard/Farm Setup/screens/milk_settings_screen.dart
M	lib/Dashboard/Farm Setup/screens/users_roles_screen.dart
M	lib/Dashboard/Health/dialogs/health_record_form_dialog.dart
M	lib/Dashboard/Health/dialogs/tre