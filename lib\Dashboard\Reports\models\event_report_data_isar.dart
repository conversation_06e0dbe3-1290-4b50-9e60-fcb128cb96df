import 'package:flutter/material.dart';
import 'package:isar/isar.dart';
import 'chart_data_isar.dart';
import 'report_data_isar.dart';

part 'event_report_data_isar.g.dart';

@collection
class EventReportDataIsar with ReportDataIsarMixin {
  Id id = Isar.autoIncrement;

  @override
  @Index()
  String? get reportType => super.reportType;
  @override
  set reportType(String? value) => super.reportType = value;

  @override
  @Index(unique: true)
  String? get businessId => super.businessId;
  @override
  set businessId(String? value) => super.businessId = value;

  // Get all events filtered by the current report criteria
  @ignore
  List<EventIsar> get filteredEvents {
    return createMockEvents();
  }

  // Mock method to create sample events for the report
  List<EventIsar> createMockEvents() {
    // This is a placeholder implementation
    // In a real app, this data would be retrieved from a database
    final events = <EventIsar>[];

    // Return empty list for now
    return events;
  }

  int? totalEvents;
  int? completedEvents;
  int? pendingEvents;
  int? overdueEvents;
  int? cancelledEvents;

  // Event type distribution
  List<String>? eventTypeNames;
  List<int>? eventTypeCounts;
  List<int>? eventTypeColors;

  // Monthly event distribution
  List<DateTime>? eventDates;
  List<int>? eventCounts;

  // Completion rate by type
  List<String>? completionRateLabels;
  List<double>? completionRateValues;
  List<int>? completionRateColors;

  /// Default constructor with name
  EventReportDataIsar.empty();

  /// Factory constructor to create a new event report
  factory EventReportDataIsar.create({
    required String title,
    DateTime? startDate,
    DateTime? endDate,
    String? filterCriteria,
    int? totalEvents,
    int? completedEvents,
    int? pendingEvents,
    int? overdueEvents,
    int? cancelledEvents,
    List<String>? eventTypeNames,
    List<int>? eventTypeCounts,
    List<int>? eventTypeColors,
    List<DateTime>? eventDates,
    List<int>? eventCounts,
    List<String>? completionRateLabels,
    List<double>? completionRateValues,
    List<int>? completionRateColors,
  }) {
    final report = EventReportDataIsar();

    // Initialize the base report properties
    report.initializeReport(
      reportType: 'event',
      title: title,
      startDate: startDate,
      endDate: endDate,
      filterCriteria: filterCriteria,
    );

    // Set the event-specific properties
    report.totalEvents = totalEvents;
    report.completedEvents = completedEvents;
    report.pendingEvents = pendingEvents;
    report.overdueEvents = overdueEvents;
    report.cancelledEvents = cancelledEvents;
    report.eventTypeNames = eventTypeNames;
    report.eventTypeCounts = eventTypeCounts;
    report.eventTypeColors = eventTypeColors;
    report.eventDates = eventDates;
    report.eventCounts = eventCounts;
    report.completionRateLabels = completionRateLabels;
    report.completionRateValues = completionRateValues;
    report.completionRateColors = completionRateColors;

    return report;
  }

  /// Constructor used in events_report_screen.dart
  EventReportDataIsar({
    List<dynamic>? events,
    DateTime? startDate,
    DateTime? endDate,
    String? searchQuery,
    String? selectedType,
    List<String>? selectedCattleIds,
  }) {
    // Initialize the base report properties
    initializeReport(
      reportType: 'event',
      title: 'Events Report',
      startDate: startDate,
      endDate: endDate,
      filterCriteria: searchQuery,
    );

    // Store the events for the getter
    _events = events ?? [];

    // Process events
    if (events != null && events.isNotEmpty) {
      // Calculate summary metrics
      totalEvents = events.length;
      completedEvents = events.where((e) => e.status == 'Completed').length;
      pendingEvents = events.where((e) => e.status == 'Pending').length;
      overdueEvents = events.where((e) => e.status == 'Overdue').length;
      cancelledEvents = events.where((e) => e.status == 'Cancelled').length;

      // Create event type distribution data
      final typeDistribution = <String, int>{};
      for (final event in events) {
        final type = event.type ?? 'Unknown';
        typeDistribution[type] = (typeDistribution[type] ?? 0) + 1;
      }

      eventTypeNames = typeDistribution.keys.toList();
      eventTypeCounts = typeDistribution.values.toList();

      // Generate colors for each type
      final colorList = [
        Colors.blue
            .value, // ignore: deprecated_member_use (value is correct for storing ARGB int)
        Colors.green
            .value, // ignore: deprecated_member_use (value is correct for storing ARGB int)
        Colors.orange
            .value, // ignore: deprecated_member_use (value is correct for storing ARGB int)
        Colors.purple
            .value, // ignore: deprecated_member_use (value is correct for storing ARGB int)
        Colors.red
            .value, // ignore: deprecated_member_use (value is correct for storing ARGB int)
        Colors.teal
            .value, // ignore: deprecated_member_use (value is correct for storing ARGB int)
      ];

      eventTypeColors = List.generate(
          eventTypeNames!.length, (i) => colorList[i % colorList.length]);

      // Create time series data
      if (events.isNotEmpty) {
        final dateMap = <DateTime, int>{};
        for (final event in events) {
          if (event.date != null) {
            final date =
                DateTime(event.date.year, event.date.month, event.date.day);
            dateMap[date] = (dateMap[date] ?? 0) + 1;
          }
        }

        final sortedDates = dateMap.keys.toList()..sort();
        eventDates = sortedDates;
        eventCounts = sortedDates.map((date) => dateMap[date]!).toList();
      }

      // Create completion rate data
      final completionRates = <String, double>{};
      final statusCounts = <String, int>{};

      for (final event in events) {
        final type = event.type ?? 'Unknown';
        statusCounts[type] = (statusCounts[type] ?? 0) + 1;
        if (event.status == 'Completed') {
          completionRates[type] = (completionRates[type] ?? 0) + 1;
        }
      }

      completionRateLabels = statusCounts.keys.toList();
      completionRateValues = completionRateLabels!.map((type) {
        final total = statusCounts[type] ?? 0;
        final completed = completionRates[type] ?? 0;
        return total > 0 ? (completed / total) * 100 : 0.0;
      }).toList();

      completionRateColors = List.generate(
          completionRateLabels!.length, (i) => colorList[i % colorList.length]);
    } else {
      // Set defaults if no events
      totalEvents = 0;
      completedEvents = 0;
      pendingEvents = 0;
      overdueEvents = 0;
      cancelledEvents = 0;
    }
  }

  // Store events for the details view
  List<dynamic> _events = [];

  // Getter for events
  @ignore
  List<dynamic> get events => _events;

  @override
  List<DataColumn> getTableColumns() {
    return [
      const DataColumn(label: Text('Status')),
      const DataColumn(label: Text('Count'), numeric: true),
      const DataColumn(label: Text('Percentage'), numeric: true),
    ];
  }

  @override
  List<DataRow> getTableRows() {
    final total = totalEvents ?? 0;
    final completed = completedEvents ?? 0;
    final pending = pendingEvents ?? 0;
    final overdue = overdueEvents ?? 0;
    final cancelled = cancelledEvents ?? 0;

    return [
      DataRow(cells: [
        const DataCell(Text('Completed')),
        DataCell(Text('$completed')),
        DataCell(Text(
            '${total > 0 ? ((completed / total) * 100).toStringAsFixed(1) : '0.0'}%')),
      ]),
      DataRow(cells: [
        const DataCell(Text('Pending')),
        DataCell(Text('$pending')),
        DataCell(Text(
            '${total > 0 ? ((pending / total) * 100).toStringAsFixed(1) : '0.0'}%')),
      ]),
      DataRow(cells: [
        const DataCell(Text('Overdue')),
        DataCell(Text('$overdue')),
        DataCell(Text(
            '${total > 0 ? ((overdue / total) * 100).toStringAsFixed(1) : '0.0'}%')),
      ]),
      DataRow(cells: [
        const DataCell(Text('Cancelled')),
        DataCell(Text('$cancelled')),
        DataCell(Text(
            '${total > 0 ? ((cancelled / total) * 100).toStringAsFixed(1) : '0.0'}%')),
      ]),
    ];
  }

  @override
  Map<String, dynamic> getSummaryData() {
    return {
      'Total Events': totalEvents ?? 0,
      'Completed': completedEvents ?? 0,
      'Pending': pendingEvents ?? 0,
      'Overdue': overdueEvents ?? 0,
      'Completion Rate':
          '${totalEvents != null && totalEvents! > 0 ? ((completedEvents ?? 0) / totalEvents! * 100).toStringAsFixed(1) : '0.0'}%',
    };
  }

  @override
  List<ChartDataIsar> getChartData() {
    final result = <ChartDataIsar>[];

    // Add event type distribution chart data
    if (eventTypeNames != null &&
        eventTypeCounts != null &&
        eventTypeColors != null) {
      for (int i = 0;
          i < eventTypeNames!.length &&
              i < eventTypeCounts!.length &&
              i < eventTypeColors!.length;
          i++) {
        result.add(ChartDataIsar()
          ..label = eventTypeNames![i]
          ..value = eventTypeCounts![i].toDouble()
          ..colorValue = eventTypeColors![i]);
      }
    }

    return result;
  }

  // Helper method to get completion rate chart data
  List<ChartDataIsar> getCompletionRateChartData() {
    final result = <ChartDataIsar>[];

    // Add completion rate chart data
    if (completionRateLabels != null &&
        completionRateValues != null &&
        completionRateColors != null) {
      for (int i = 0;
          i < completionRateLabels!.length &&
              i < completionRateValues!.length &&
              i < completionRateColors!.length;
          i++) {
        result.add(ChartDataIsar()
          ..label = completionRateLabels![i]
          ..value = completionRateValues![i]
          ..colorValue = completionRateColors![i]);
      }
    }

    return result;
  }

  // Helper method to get time series event data
  List<ChartDataIsar> getTimeSeriesChartData() {
    final result = <ChartDataIsar>[];

    // Create time series chart data from eventDates and eventCounts
    if (eventDates != null && eventCounts != null) {
      for (int i = 0; i < eventDates!.length && i < eventCounts!.length; i++) {
        result.add(ChartDataIsar()
              ..date = eventDates![i]
              ..value = eventCounts![i].toDouble()
              ..colorValue = Colors.orange
                  .value // ignore: deprecated_member_use (value is correct for storing ARGB int)
            );
      }
    }

    return result;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    map.addAll({
      'totalEvents': totalEvents,
      'completedEvents': completedEvents,
      'pendingEvents': pendingEvents,
      'overdueEvents': overdueEvents,
      'cancelledEvents': cancelledEvents,
      'eventTypeNames': eventTypeNames,
      'eventTypeCounts': eventTypeCounts,
      'eventTypeColors': eventTypeColors,
      'eventDates': eventDates?.map((date) => date.toIso8601String()).toList(),
      'eventCounts': eventCounts,
      'completionRateLabels': completionRateLabels,
      'completionRateValues': completionRateValues,
      'completionRateColors': completionRateColors,
    });
    return map;
  }

  factory EventReportDataIsar.fromMap(Map<String, dynamic> map) {
    final report = EventReportDataIsar();

    // Initialize the base properties
    report.initFromMap(map);

    // Set event-specific properties
    report.totalEvents = map['totalEvents'] as int?;
    report.completedEvents = map['completedEvents'] as int?;
    report.pendingEvents = map['pendingEvents'] as int?;
    report.overdueEvents = map['overdueEvents'] as int?;
    report.cancelledEvents = map['cancelledEvents'] as int?;

    // Handle lists
    if (map['eventTypeNames'] != null) {
      report.eventTypeNames = List<String>.from(map['eventTypeNames'] as List);
    }

    if (map['eventTypeCounts'] != null) {
      report.eventTypeCounts = List<int>.from(map['eventTypeCounts'] as List);
    }

    if (map['eventTypeColors'] != null) {
      report.eventTypeColors = List<int>.from(map['eventTypeColors'] as List);
    }

    if (map['eventDates'] != null) {
      report.eventDates = (map['eventDates'] as List)
          .map((dateStr) => DateTime.parse(dateStr as String))
          .toList();
    }

    if (map['eventCounts'] != null) {
      report.eventCounts = List<int>.from(map['eventCounts'] as List);
    }

    if (map['completionRateLabels'] != null) {
      report.completionRateLabels =
          List<String>.from(map['completionRateLabels'] as List);
    }

    if (map['completionRateValues'] != null) {
      report.completionRateValues =
          List<double>.from(map['completionRateValues'] as List);
    }

    if (map['completionRateColors'] != null) {
      report.completionRateColors =
          List<int>.from(map['completionRateColors'] as List);
    }

    return report;
  }
}

/// Class to represent an event in the event report
class EventIsar {
  final String eventId;
  final String title;
  final String type;
  final DateTime date;
  final String status;
  final String description;
  final String? cattleId;

  EventIsar({
    required this.eventId,
    required this.title,
    required this.type,
    required this.date,
    required this.status,
    required this.description,
    this.cattleId,
  });
}
