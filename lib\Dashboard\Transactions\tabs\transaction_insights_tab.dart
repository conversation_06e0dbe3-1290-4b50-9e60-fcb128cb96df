import 'package:flutter/material.dart';
import '../../../constants/app_tabs.dart';
import '../controllers/transaction_controller.dart';
import '../../widgets/index.dart';

class TransactionInsightsTab extends StatefulWidget {
  final TransactionController controller;

  const TransactionInsightsTab({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  State<TransactionInsightsTab> createState() => _TransactionInsightsTabState();
}

class _TransactionInsightsTabState extends State<TransactionInsightsTab>
    with AutomaticKeepAliveClientMixin {

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    return ListenableBuilder(
      listenable: widget.controller,
      builder: (context, child) {
        if (widget.controller.isLoading) {
          return UniversalLoadingIndicator.transactions();
        }

        if (widget.controller.hasError) {
          return UniversalErrorIndicator.transactions(
            message: widget.controller.errorMessage ?? 'Failed to load insights data',
            onRetry: () => widget.controller.refresh(),
          );
        }

        final analyticsSummary = widget.controller.analyticsSummary;
        final insightsData = widget.controller.insightsData;

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildInsightsHeader(analyticsSummary),
              const SizedBox(height: 24),
              _buildTopCategoriesCard(insightsData),
              const SizedBox(height: 16),
              _buildFinancialHealthCard(analyticsSummary, insightsData),
              const SizedBox(height: 16),
              _buildTrendsCard(insightsData),
              const SizedBox(height: 16),
              _buildRecommendationsCard(insightsData),
            ],
          ),
        );
      },
    );
  }

  Widget _buildInsightsHeader(TransactionAnalyticsSummary analyticsSummary) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [UniversalEmptyStateTheme.transactions, const Color(0xFF1565C0)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.insights, color: Colors.white, size: 28),
              SizedBox(width: 12),
              Text(
                'Transaction Insights',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            'Analysis based on ${analyticsSummary.totalTransactions} transactions',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Date range: ${_formatDateRange(analyticsSummary.firstTransactionDate, analyticsSummary.lastTransactionDate)}',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopCategoriesCard(TransactionInsightsData insightsData) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.star, color: Colors.amber),
                SizedBox(width: 8),
                Text(
                  'Top Categories',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildTopCategoryItem(
                    'Top Income',
                    insightsData.topIncomeCategory,
                    Icons.trending_up,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildTopCategoryItem(
                    'Top Expense',
                    insightsData.topExpenseCategory,
                    Icons.trending_down,
                    Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildTopCategoryItem(
              'Most Used Payment',
              insightsData.mostUsedPaymentMethod,
              Icons.payment,
              UniversalEmptyStateTheme.transactions,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopCategoryItem(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildFinancialHealthCard(TransactionAnalyticsSummary analyticsSummary, TransactionInsightsData insightsData) {
    final healthScore = _calculateHealthScore(analyticsSummary);
    final healthColor = _getHealthColor(healthScore);
    
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.health_and_safety, color: Colors.teal),
                SizedBox(width: 8),
                Text(
                  'Financial Health',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildHealthMetric(
                    'Health Score',
                    '${healthScore.toStringAsFixed(0)}/100',
                    Icons.score,
                    healthColor,
                  ),
                ),
                Expanded(
                  child: _buildHealthMetric(
                    'Monthly Avg Income',
                    '\$${insightsData.monthlyAverageIncome.toStringAsFixed(2)}',
                    Icons.trending_up,
                    Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildHealthMetric(
                    'Monthly Avg Expense',
                    '\$${insightsData.monthlyAverageExpense.toStringAsFixed(2)}',
                    Icons.trending_down,
                    Colors.red,
                  ),
                ),
                Expanded(
                  child: _buildHealthMetric(
                    'Expense Ratio',
                    '${_calculateExpenseRatio(analyticsSummary).toStringAsFixed(1)}%',
                    Icons.pie_chart,
                    _getExpenseRatioColor(_calculateExpenseRatio(analyticsSummary)),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHealthMetric(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 6),
          Text(
            title,
            style: const TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.w500,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTrendsCard(TransactionInsightsData insightsData) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.show_chart, color: Colors.purple),
                SizedBox(width: 8),
                Text(
                  'Monthly Trends',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (insightsData.monthlyTrends.isEmpty)
              const Center(
                child: Text(
                  'No trend data available',
                  style: TextStyle(color: Colors.grey),
                ),
              )
            else
              const SizedBox(
                height: 200,
                child: Center(
                  child: Text(
                    'Monthly trends chart will be displayed here',
                    style: TextStyle(color: Colors.grey),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecommendationsCard(TransactionInsightsData insightsData) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.lightbulb, color: Colors.orange),
                SizedBox(width: 8),
                Text(
                  'Recommendations',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (insightsData.recommendations.isEmpty)
              const Text(
                'Great job! Your financial management looks healthy.',
                style: TextStyle(color: Colors.green, fontWeight: FontWeight.w500),
              )
            else
              ...insightsData.recommendations.map(
                (recommendation) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Icon(Icons.arrow_right, color: Colors.orange, size: 20),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          recommendation,
                          style: const TextStyle(fontSize: 14),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  // Helper methods
  String _formatDateRange(DateTime? start, DateTime? end) {
    if (start == null && end == null) return 'All time';
    if (start == null) return 'Until ${_formatDate(end!)}';
    if (end == null) return 'From ${_formatDate(start)}';
    return '${_formatDate(start)} - ${_formatDate(end)}';
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  double _calculateHealthScore(TransactionAnalyticsSummary summary) {
    if (summary.totalTransactions == 0) return 0;
    
    double score = 50; // Base score
    
    // Positive balance adds points
    if (summary.netBalance > 0) {
      score += 30;
    } else {
      score -= 20;
    }
    
    // Income vs expense ratio
    if (summary.totalIncome > 0) {
      final ratio = summary.totalExpenses / summary.totalIncome;
      if (ratio < 0.7) {
        score += 20;
      } else if (ratio < 0.9) {
        score += 10;
      } else if (ratio > 1.2) {
        score -= 20;
      }
    }
    
    return score.clamp(0, 100);
  }

  Color _getHealthColor(double score) {
    if (score >= 80) return Colors.green;
    if (score >= 60) return Colors.orange;
    return Colors.red;
  }

  double _calculateExpenseRatio(TransactionAnalyticsSummary summary) {
    if (summary.totalIncome == 0) return 0;
    return (summary.totalExpenses / summary.totalIncome) * 100;
  }

  Color _getExpenseRatioColor(double ratio) {
    if (ratio <= 70) return Colors.green;
    if (ratio <= 90) return Colors.orange;
    return Colors.red;
  }
}
