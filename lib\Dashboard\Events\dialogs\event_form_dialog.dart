import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/event_isar.dart';

class EventFormDialog extends StatefulWidget {
  final String cattleId;
  final EventIsar? event;

  const EventFormDialog({
    Key? key,
    required this.cattleId,
    this.event,
  }) : super(key: key);

  @override
  State<EventFormDialog> createState() => _EventFormDialogState();
}

class _EventFormDialogState extends State<EventFormDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _titleController;
  late TextEditingController _notesController;
  late DateTime _selectedDate;
  DateTime? _selectedDueDate;
  late EventType _selectedType;
  late EventPriority _selectedPriority;
  late String _completionStatus;
  TimeOfDay? _selectedTime;

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController();
    _notesController = TextEditingController();
    _completionStatus = 'Pending'; // Set default status
    _initializeFromEvent();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  void _initializeFromEvent() {
    if (widget.event != null) {
      EventIsar event = widget.event!;
      _titleController.text = event.title ?? '';
      _selectedDate = event.eventDate ?? DateTime.now();
      _selectedDueDate = event.dueDate;
      _selectedType = event.type?.toEventType() ?? EventType.miscellaneous;
      _selectedPriority = event.priority;
      _completionStatus = event.isCompleted
          ? 'Completed'
          : (event.isMissed ? 'Missed' : 'Pending');
      _notesController.text = event.notes ?? '';

      // Safely handle time property
      if (event.time != null) {
        _selectedTime = event.time?.toTimeOfDay();
      } else {
        _selectedTime = null;
      }
    } else {
      // Set default values for new event
      _selectedDate = DateTime.now();
      _selectedType = EventType.miscellaneous;
      _selectedPriority = EventPriority.medium;
      _selectedTime = null;
    }
  }

  Future<EventIsar> _buildEventFromForm() async {
    final event = widget.event ?? EventIsar();

    event.title = _titleController.text.trim();
    event.eventDate = _selectedDate;
    event.dueDate = _selectedDueDate;

    // Setup event type
    event.type ??= EventTypeEmbedded();
    event.type!.fromEventType(_selectedType);

    event.priority = _selectedPriority;
    event.notes = _notesController.text.trim();

    // Set completion status
    event.isCompleted = _completionStatus == 'Completed';
    event.isMissed = _completionStatus == 'Missed';

    // Set time if selected
    if (_selectedTime != null) {
      event.time = TimeOfDayIsar.fromTimeOfDay(_selectedTime!);
    } else {
      event.time = null;
    }

    event.updatedAt = DateTime.now();

    return event;
  }

  Future<void> _selectDate(BuildContext context, bool isDueDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate:
          isDueDate ? (_selectedDueDate ?? DateTime.now()) : _selectedDate,
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );
    if (picked != null) {
      setState(() {
        if (isDueDate) {
          _selectedDueDate = picked;
        } else {
          _selectedDate = picked;
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.event == null ? 'Add New Event' : 'Edit Event'),
      content: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(labelText: 'Title'),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a title';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<EventType>(
                value: _selectedType,
                decoration: const InputDecoration(labelText: 'Event Type'),
                items: EventType.values.map((type) {
                  return DropdownMenuItem(
                    value: type,
                    child: Text(type.name),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedType = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<EventPriority>(
                value: _selectedPriority,
                decoration: const InputDecoration(labelText: 'Priority'),
                items: EventPriority.values.map((priority) {
                  return DropdownMenuItem(
                    value: priority,
                    child: Text(priority.name),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedPriority = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 16),
              ListTile(
                title: const Text('Event Date'),
                subtitle: Text(
                  DateFormat('MMM dd, yyyy').format(_selectedDate),
                ),
                trailing: const Icon(Icons.calendar_today),
                onTap: () => _selectDate(context, false),
              ),
              ListTile(
                title: const Text('Due Date (Optional)'),
                subtitle: Text(
                  _selectedDueDate != null
                      ? DateFormat('MMM dd, yyyy').format(_selectedDueDate!)
                      : 'Not set',
                ),
                trailing: const Icon(Icons.calendar_today),
                onTap: () => _selectDate(context, true),
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _notesController,
                decoration: const InputDecoration(labelText: 'Notes'),
                maxLines: 3,
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: () {
            if (_formKey.currentState != null && _formKey.currentState!.validate()) {
              // Capture the navigator before the async gap
              final navigator = Navigator.of(context);
              _buildEventFromForm().then((event) {
                // Use the captured navigator
                navigator.pop(event);
              });
            }
          },
          child: const Text('Save'),
        ),
      ],
    );
  }
}
