import 'package:flutter/material.dart';
import '../controllers/transaction_detail_controller.dart';
import '../services/transaction_service.dart';
import '../../../constants/app_bar.dart';
import '../../../utils/message_utils.dart';
import '../../../constants/app_tabs.dart';
import '../../widgets/index.dart';

import 'transaction_details_analytics_tab.dart';
import 'transaction_details_records_tab.dart';

class TransactionDetailScreen extends StatefulWidget {
  final String? categoryFilter;
  final String? typeFilter;
  final String title;
  final VoidCallback? onRefresh;

  const TransactionDetailScreen({
    Key? key,
    this.categoryFilter,
    this.typeFilter,
    required this.title,
    this.onRefresh,
  }) : super(key: key);

  @override
  State<TransactionDetailScreen> createState() => _TransactionDetailScreenState();
}

class _TransactionDetailScreenState extends State<TransactionDetailScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late TransactionDetailController _controller;
  late FilterController _filterController;

  // Tab configuration using the reusable widget
  late List<TabItem> _tabs;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(() {
      setState(() {}); // Rebuild to update colors
    });

    // Initialize controller
    _controller = TransactionDetailController(
      categoryFilter: widget.categoryFilter,
      typeFilter: widget.typeFilter,
      transactionService: TransactionService(),
    );
    _controller.addListener(_onControllerChanged);

    // Initialize filter controller
    _filterController = FilterController();

    // Define tabs using the reusable widget configuration with multicolor
    _tabs = TabConfigurations.twoTabDetail(
      tab1Label: 'Analytics',
      tab1Icon: Icons.analytics,
      tab1Color: UniversalEmptyStateTheme.transactions, // Blue for analytics
      tab2Label: 'Records',
      tab2Icon: Icons.list_alt,
      tab2Color: const Color(0xFF388E3C), // Green for records
    );

    _initializeController();
  }

  @override
  void dispose() {
    _controller.removeListener(_onControllerChanged);
    _controller.dispose();
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _initializeController() async {
    try {
      await _controller.initialize();
    } catch (e) {
      if (mounted) {
        FinancialMessageUtils.showError(context, 'Error loading transaction records: $e');
      }
    }
  }

  void _onControllerChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarConfig.withBack(
        title: widget.title,
        context: context,
        actions: _buildAppBarActions(),
      ),
      body: Column(
        children: [
          // Use the reusable tab bar widget with multicolor
          ReusableTabBar.controlled(
            controller: _tabController,
            tabs: _tabs,
            useMulticolor: true,
            indicatorColor: UniversalEmptyStateTheme.transactions,
          ),
          // TabBarView
          Expanded(
            child: _controller.isLoading
                ? UniversalLoadingIndicator.transactions()
                : _controller.error != null
                    ? UniversalErrorIndicator.transactions(
                        message: _controller.error!,
                        onRetry: () => _controller.refresh(),
                      )
                    : TabBarView(
                        controller: _tabController,
                        children: [
                          TransactionDetailsAnalyticsTab(
                            controller: _controller,
                          ),
                          TransactionDetailsRecordsTab(
                            controller: _controller,
                          ),
                        ],
                      ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildAppBarActions() {
    return [
      IconButton(
        icon: const Icon(Icons.refresh),
        onPressed: () async {
          try {
            await _controller.refresh();
            widget.onRefresh?.call();
          } catch (e) {
            if (mounted) {
              FinancialMessageUtils.showError(context, 'Error refreshing data: $e');
            }
          }
        },
        tooltip: 'Refresh',
      ),
      PopupMenuButton<String>(
        onSelected: _handleMenuAction,
        itemBuilder: (context) => [
          const PopupMenuItem(
            value: 'export',
            child: Row(
              children: [
                Icon(Icons.download),
                SizedBox(width: 8),
                Text('Export Data'),
              ],
            ),
          ),
          const PopupMenuItem(
            value: 'filter',
            child: Row(
              children: [
                Icon(Icons.filter_list),
                SizedBox(width: 8),
                Text('Advanced Filters'),
              ],
            ),
          ),
          const PopupMenuItem(
            value: 'sort',
            child: Row(
              children: [
                Icon(Icons.sort),
                SizedBox(width: 8),
                Text('Sort Options'),
              ],
            ),
          ),
        ],
      ),
    ];
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'export':
        _showExportDialog();
        break;
      case 'filter':
        _showAdvancedFilters();
        break;
      case 'sort':
        _showSortOptions();
        break;
    }
  }

  void _showExportDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export Transaction Data'),
        content: const Text('Export functionality will be implemented here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement export functionality
              FinancialMessageUtils.showInfo(context, 'Export feature coming soon!');
            },
            child: const Text('Export'),
          ),
        ],
      ),
    );
  }

  void _showAdvancedFilters() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Advanced Filters'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Advanced filtering options will be implemented here.'),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      _filterController.clearAllApplied();
                      _controller.applyFilters(_filterController);
                      Navigator.of(context).pop();
                    },
                    child: const Text('Clear All'),
                  ),
                ),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Apply advanced filters
            },
            child: const Text('Apply'),
          ),
        ],
      ),
    );
  }

  void _showSortOptions() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sort Options'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('Date'),
              leading: Radio<String>(
                value: 'date',
                groupValue: _filterController.sortBy,
                onChanged: (value) {
                  _filterController.setSortBy(value!);
                  _controller.applyFilters(_filterController);
                  Navigator.of(context).pop();
                },
              ),
            ),
            ListTile(
              title: const Text('Amount'),
              leading: Radio<String>(
                value: 'amount',
                groupValue: _filterController.sortBy,
                onChanged: (value) {
                  _filterController.setSortBy(value!);
                  _controller.applyFilters(_filterController);
                  Navigator.of(context).pop();
                },
              ),
            ),
            ListTile(
              title: const Text('Category'),
              leading: Radio<String>(
                value: 'category',
                groupValue: _filterController.sortBy,
                onChanged: (value) {
                  _filterController.setSortBy(value!);
                  _controller.applyFilters(_filterController);
                  Navigator.of(context).pop();
                },
              ),
            ),
            const Divider(),
            SwitchListTile(
              title: const Text('Ascending Order'),
              value: _filterController.isAscending,
              onChanged: (value) {
                _filterController.setSortDirection(value);
                _controller.applyFilters(_filterController);
                Navigator.of(context).pop();
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
