import 'package:flutter/material.dart';
import 'filters.dart';

/// Simple filter status bar that displays record count and clear functionality
/// Note: Sort arrows are now handled in the active filters row in filter_layout.dart
class FilterStatusBar extends StatelessWidget {
  final FilterController controller;
  final int? totalCount;
  final int? filteredCount;
  final EdgeInsets? padding;
  final bool showRecordCount;

  const FilterStatusBar({
    Key? key,
    required this.controller,
    this.totalCount,
    this.filteredCount,
    this.padding,
    this.showRecordCount = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: controller,
      builder: (context, child) {
        // Only show when filters are active
        final hasFilters = controller.isAnyFilterActive;

        if (!hasFilters) {
          return const SizedBox.shrink();
        }

        return Container(
          margin: padding ?? const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Status text
              Expanded(
                child: Text(
                  showRecordCount && totalCount != null && filteredCount != null
                      ? 'Showing $filteredCount of $totalCount records'
                      : 'Filters active',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey,
                  ),
                ),
              ),

              // Clear all button
              TextButton.icon(
                onPressed: () {
                  controller.clearAllApplied();
                },
                icon: const Icon(Icons.clear_all, size: 16, color: Colors.red),
                label: const Text(
                  'Clear All',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.red,
                  ),
                ),
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  minimumSize: Size.zero,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
