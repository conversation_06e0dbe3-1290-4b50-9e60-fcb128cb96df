import 'package:flutter/material.dart';

/// Trend Card Widget
/// 
/// A specialized card widget for displaying trend information and changes.
/// Provides consistent styling for trend visualizations.
class TrendCard extends StatelessWidget {
  final String title;
  final String currentValue;
  final String? previousValue;
  final double? changePercentage;
  final TrendDirection? trendDirection;
  final String? timeframe;
  final IconData? icon;
  final Color? color;
  final VoidCallback? onTap;

  const TrendCard({
    Key? key,
    required this.title,
    required this.currentValue,
    this.previousValue,
    this.changePercentage,
    this.trendDirection,
    this.timeframe,
    this.icon,
    this.color,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final primaryColor = color ?? theme.primaryColor;
    final trendColor = _getTrendColor();

    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  if (icon != null) ...[
                    Icon(
                      icon,
                      color: primaryColor,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                  ],
                  Expanded(
                    child: Text(
                      title,
                      style: theme.textTheme.titleSmall?.copyWith(
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  if (timeframe != null)
                    Text(
                      timeframe!,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: Colors.grey[500],
                      ),
                    ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Current value
              Text(
                currentValue,
                style: theme.textTheme.headlineMedium?.copyWith(
                  color: primaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
              
              // Trend information
              if (changePercentage != null || trendDirection != null) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    if (trendDirection != null) ...[
                      Icon(
                        _getTrendIcon(),
                        color: trendColor,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                    ],
                    if (changePercentage != null)
                      Text(
                        '${changePercentage!.abs().toStringAsFixed(1)}%',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: trendColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    if (previousValue != null) ...[
                      const SizedBox(width: 8),
                      Text(
                        'from $previousValue',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Color _getTrendColor() {
    if (trendDirection == null) return Colors.grey;
    
    switch (trendDirection!) {
      case TrendDirection.up:
        return Colors.green;
      case TrendDirection.down:
        return Colors.red;
      case TrendDirection.stable:
        return Colors.grey;
    }
  }

  IconData _getTrendIcon() {
    if (trendDirection == null) return Icons.trending_flat;
    
    switch (trendDirection!) {
      case TrendDirection.up:
        return Icons.trending_up;
      case TrendDirection.down:
        return Icons.trending_down;
      case TrendDirection.stable:
        return Icons.trending_flat;
    }
  }
}

/// Direction of trend for styling
enum TrendDirection {
  up,
  down,
  stable,
}
