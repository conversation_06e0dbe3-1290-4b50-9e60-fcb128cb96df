// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../controllers/transaction_controller.dart';
import '../models/transaction_isar.dart';
import '../dialogs/transaction_form_dialog.dart';
import '../../widgets/index.dart'; // Import Universal Components
import '../../../utils/message_utils.dart';
import '../../../constants/app_tabs.dart';

class TransactionRecordsTab extends StatefulWidget {
  final TransactionController controller;

  const TransactionRecordsTab({Key? key, required this.controller})
      : super(key: key);

  @override
  State<TransactionRecordsTab> createState() => _TransactionRecordsTabState();
}

class _TransactionRecordsTabState extends State<TransactionRecordsTab>
    with AutomaticKeepAliveClientMixin, UniversalScreenState, UniversalDataRefresh, UniversalPullToRefresh {

  late FilterController _filterController;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();

    // Initialize filter controller
    _filterController = FilterController();
    _filterController.addListener(_onFiltersChanged);


  }

  /// Handle filter changes and update transaction controller
  void _onFiltersChanged() {
    // Apply filters using the universal filter service
    widget.controller.applyFilters(_filterController);
  }

  /// Manual refresh method for pull-to-refresh
  Future<void> _handleRefresh() async {
    await performRefreshWithRetry(
      () => widget.controller.refresh(),
      maxRetries: 3,
      retryDelay: const Duration(seconds: 2),
    );
  }

  @override
  void dispose() {
    _filterController.removeListener(_onFiltersChanged);
    _filterController.dispose();
    super.dispose();
  }



  Future<void> _loadData() async {
    await widget.controller.loadData();
  }

  Future<void> _showAddTransactionDialog() async {
    await showDialog(
      context: context,
      builder: (context) => TransactionFormDialog(
        categories: widget.controller.allCategories,
        onTransactionAdded: () => performRefresh(() => _loadData()),
      ),
    );
  }

  Future<void> _editTransaction(TransactionIsar transaction) async {
    await showDialog(
      context: context,
      builder: (context) => TransactionFormDialog(
        transaction: transaction,
        categories: widget.controller.allCategories,
        onTransactionAdded: () => performRefresh(() => _loadData()),
      ),
    );
  }

  Future<void> _deleteTransaction(TransactionIsar transaction) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Transaction'),
        content: Text('Are you sure you want to delete "${transaction.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await widget.controller.deleteTransaction(transaction.transactionId);
        await _loadData();
        if (mounted) {
          FinancialMessageUtils.showSuccess(context, 'Transaction deleted successfully');
        }
      } catch (e) {
        debugPrint('Error deleting transaction: $e');
        if (mounted) {
          FinancialMessageUtils.showError(context, 'Error deleting transaction');
        }
      }
    }
  }

  void _showTransactionOptions(TransactionIsar transaction) {
    showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.edit),
              title: const Text('Edit Transaction'),
              onTap: () {
                Navigator.pop(context);
                _editTransaction(transaction);
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: const Text('Delete Transaction', style: TextStyle(color: Colors.red)),
              onTap: () {
                Navigator.pop(context);
                _deleteTransaction(transaction);
              },
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    return ListenableBuilder(
      listenable: widget.controller,
      builder: (context, child) {
        return Column(
          children: [
            // Filter and Search Section
            UniversalFilterLayout(
              controller: _filterController,
              theme: FilterTheme.transaction,
              moduleName: 'transaction',
              sortFields: [...SortField.commonFields, ...SortField.transactionFields],
              searchHint: 'Search transactions...',
              totalCount: widget.controller.allTransactions.length,
              filteredCount: widget.controller.filteredTransactions.length,
            ),

            // Filter Status Bar
            FilterStatusBar(
              controller: _filterController,
            ),

            // Transaction List
            Expanded(
              child: _buildTransactionList(),
            ),
          ],
        );
      },
    );
  }

  Widget _buildTransactionList() {
    const tabColor = Color(0xFF388E3C); // Green color for Records tab

    if (!widget.controller.hasData) {
      return UniversalTabEmptyState.forTab(
        title: 'No Transactions',
        message: 'Start by adding your first transaction to track farm finances.',
        tabColor: tabColor,
        tabIndex: 1, // Records tab
        moduleName: 'transactions',
        action: TabEmptyStateActions.addFirstRecord(
          onPressed: _showAddTransactionDialog,
          text: 'Add Transaction',
          tabColor: tabColor,
        ),
      );
    }

    if (widget.controller.filteredTransactions.isEmpty) {
      return UniversalTabEmptyState.forTab(
        title: 'No Results Found',
        message: 'No transactions match your current filters.',
        tabColor: tabColor,
        tabIndex: 1, // Records tab
        moduleName: 'transactions',
        action: TabEmptyStateActions.clearFilters(
          onPressed: () {
            debugPrint('🔍 TransactionRecordsTab - Clear filters from empty state');
            _filterController.clearAllApplied();
            // Filters will be automatically applied via the listener
          },
          tabColor: tabColor,
        ),
      );
    }

    // Use Universal List Builder System for better performance and consistency
    return UniversalListBuilder.transactions(
      items: widget.controller.filteredTransactions,
      onRefresh: _handleRefresh,
      emptyStateWidget: UniversalTabEmptyState.forTab(
        title: 'No Results Found',
        message: 'No transaction records match your current filters.',
        tabColor: tabColor,
        tabIndex: 1, // Records tab
        moduleName: 'transactions',
        action: TabEmptyStateActions.clearFilters(
          onPressed: () {
            debugPrint('🔍 TransactionRecordsTab - Clear filters from empty state (list builder)');
            _filterController.clearAllApplied();
            // Filters will be automatically applied via the listener
          },
          tabColor: tabColor,
        ),
      ),
      padding: const EdgeInsets.all(16.0),
      itemBuilder: (context, transaction, index) {
        // Determine if income or expense for dynamic colors
        final isIncome = transaction.categoryType.toLowerCase() == 'income';
        final amountColor = isIncome ? Colors.green : Colors.red;
        final formattedAmount = '${isIncome ? '+' : '-'}\$${transaction.amount.toStringAsFixed(2)}';
        final formattedDate = DateFormat('MMM dd, yyyy').format(transaction.date);

        // Use the simplified universal record card
        return UniversalRecordCard(
          row1Left: formattedDate,
          row1Right: formattedAmount,
          row1LeftIcon: Icons.calendar_today_outlined,
          row1RightIcon: isIncome ? Icons.arrow_upward : Icons.arrow_downward,
          row2Left: transaction.category,
          row2Right: transaction.paymentMethod,
          row2LeftIcon: transaction.icon,
          row2RightIcon: _getPaymentMethodIcon(transaction.paymentMethod),
          notes: transaction.description.isNotEmpty ? transaction.description : null,
          primaryColor: UniversalRecordTheme.transactionPrimary,
          row1RightColor: amountColor,
          onEdit: () => _editTransaction(transaction),
          onDelete: () => _deleteTransaction(transaction),
          onTap: () => _editTransaction(transaction),
          onLongPress: () => _showTransactionOptions(transaction),
        );
      },
    );
  }



  IconData _getPaymentMethodIcon(String paymentMethod) {
    switch (paymentMethod.toLowerCase()) {
      case 'cash':
        return Icons.money;
      case 'credit card':
        return Icons.credit_card;
      case 'debit card':
        return Icons.payment;
      case 'bank transfer':
        return Icons.account_balance;
      case 'mobile payment':
        return Icons.phone_android;
      case 'check':
        return Icons.receipt;
      default:
        return Icons.payment;
    }
  }
}
