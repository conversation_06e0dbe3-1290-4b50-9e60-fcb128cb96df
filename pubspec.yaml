name: cattle_manager
description: A comprehensive cattle management application for farmers and agricultural professionals.

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# Version and build number for the application.
version: 1.01.0+101

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  provider: ^6.1.1
  logging: ^1.3.0
  printing: ^5.11.0  # Add printing package for PDF generation
  flutter_localizations:
    sdk: flutter
  sqflite: ^2.3.0
  sqflite_common_ffi: ^2.3.0
  sqflite_common_ffi_web: ^0.4.0
  path: ^1.8.3
  uuid: ^4.5.1
  intl: 0.20.2
  shared_preferences: ^2.2.2
  flutter_svg: ^2.0.17
  cupertino_icons: ^1.0.2
  font_awesome_flutter: ^10.6.0
  excel: ^4.0.6
  csv: ^5.1.1  # For CSV file handling
  # hive: ^2.2.3
  # hive_flutter: ^1.1.0
  pdf: ^3.10.4
  path_provider: ^2.1.5
  share_plus: ^7.2.1
  file_picker: ^9.2.3
  universal_html: ^2.2.4
  fl_chart: ^0.70.0
  syncfusion_flutter_xlsio: ^28.2.5
  syncfusion_flutter_pdf: ^28.2.5
  image_picker: ^1.1.2
  collection: ^1.19.0
  geolocator: ^10.1.0  # For GPS location
  geocoding: ^2.1.1  # For converting coordinates to addresses
  http: ^1.1.0  # For making HTTP requests
  googleapis: ^12.0.0  # For Google Drive API integration
  googleapis_auth: ^1.4.1  # For Google authentication
  google_sign_in: ^6.1.6  # For Google Sign-In integration
  flutter_secure_storage: ^9.0.0  # For secure storage of credentials
  qr_flutter: ^4.1.0  # For QR code generation
  mobile_scanner: ^3.5.6  # For QR code scanning - modern replacement for qr_code_scanner
  url_launcher: ^6.2.5
  table_calendar: ^3.0.9  # For calendar functionality in health tracking
  keyboard_dismisser: ^2.0.0
  rxdart: ^0.28.0
  get_it: ^7.6.4
  # Using stable version of Isar
  isar: 3.1.0+1
  isar_flutter_libs: 3.1.0+1  # Contains native binaries
  json_annotation: ^4.8.1
  logger: ^2.5.0
  flutter_colorpicker: ^1.1.0
  permission_handler: ^11.4.0
  async: ^2.11.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
  # hive_generator: ^2.0.0
  build_runner: ^2.4.4
  # Stable Isar generator
  isar_generator: 3.1.0+1
  json_serializable: ^6.7.1

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/icons/
    - assets/
