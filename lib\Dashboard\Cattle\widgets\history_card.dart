import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'info_row.dart';

class HistoryCard extends StatelessWidget {
  final List<Map<String, dynamic>> records;
  final String title;
  final Color titleColor;
  final IconData titleIcon;
  final String emptyMessage;
  final Widget Function(BuildContext, Map<String, dynamic>)? recordBuilder;

  const HistoryCard({
    super.key,
    required this.records,
    this.title = 'History',
    this.titleColor = const Color(0xFF2E7D32),
    this.titleIcon = Icons.history,
    this.emptyMessage = 'No records found',
    this.recordBuilder,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      margin: const EdgeInsets.only(bottom: 16),
      child: <PERSON>umn(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: titleColor.withAlpha(26), // 0.1 * 255 = 26
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundColor: titleColor.withAlpha(51), // 0.2 * 255 = 51
                  child: Icon(
                    titleIcon,
                    color: titleColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: titleColor,
                  ),
                ),
              ],
            ),
          ),

          // Content
          if (records.isEmpty)
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Center(
                child: Text(
                  emptyMessage,
                  style: const TextStyle(fontStyle: FontStyle.italic),
                ),
              ),
            )
          else
            Padding(
              padding: const EdgeInsets.all(16),
              child: ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: records.length,
                separatorBuilder: (context, index) => const Divider(height: 32),
                itemBuilder: (context, index) {
                  final record = records[index];

                  // If a custom record builder is provided, use it
                  if (recordBuilder != null) {
                    return recordBuilder!(context, record);
                  }

                  // Otherwise, use the default record layout
                  return _buildDefaultRecordCard(context, record);
                },
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDefaultRecordCard(
      BuildContext context, Map<String, dynamic> record) {
    final recordDate = record['date'] != null
        ? DateTime.parse(record['date'])
        : DateTime.now();

    final recordTitle =
        record['title'] ?? DateFormat('MMMM dd, yyyy').format(recordDate);
    final recordSubtitle = record['subtitle'] ?? '';
    final recordIcon = record['icon'] ?? Icons.event_note;
    final recordColor = record['color'] ?? Colors.teal;

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 0),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Record Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: recordColor.withAlpha(51),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Row(
                    children: [
                      CircleAvatar(
                        radius: 20,
                        backgroundColor: recordColor.withAlpha(77),
                        child: Icon(
                          recordIcon,
                          color: recordColor,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              recordTitle,
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: recordColor,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                            if (recordSubtitle.isNotEmpty) ...[
                              const SizedBox(height: 4),
                              Text(
                                recordSubtitle,
                                style: TextStyle(
                                  fontSize: 15,
                                  fontWeight: FontWeight.w600,
                                  color: recordColor.withAlpha(179),
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Record Details
          if (record['details'] != null && record['details'] is List) ...[
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ...(record['details'] as List).map((detail) {
                    return Column(
                      children: [
                        InfoRow(
                          icon: detail['icon'] ?? Icons.info,
                          label: detail['label'] ?? '',
                          value: detail['value'] ?? '',
                          color: detail['color'] ?? recordColor,
                          isStatus: detail['isStatus'] ?? false,
                          isHighlighted: detail['isHighlighted'] ?? false,
                          isMultiline: detail['isMultiline'] ?? false,
                        ),
                        const SizedBox(height: 8),
                      ],
                    );
                  }).toList(),

                  // Notes
                  if (record['notes']?.isNotEmpty == true) ...[
                    const SizedBox(height: 8),
                    const Divider(),
                    const SizedBox(height: 8),
                    InfoRow(
                      icon: Icons.notes,
                      label: 'Notes',
                      value: record['notes'],
                      color: Colors.brown,
                      isMultiline: true,
                    ),
                  ],
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
}
