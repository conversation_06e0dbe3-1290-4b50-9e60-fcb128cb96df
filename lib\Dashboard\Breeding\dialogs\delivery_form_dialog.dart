/*
import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import 'package:intl/intl.dart';
import 'package:uuid/uuid.dart';

import '../../Cattle/models/cattle_isar.dart';
import '../../../utils/message_utils.dart';

final _logger = Logger('DeliveryFormDialog');

class DeliveryFormDialog extends StatefulWidget {
  final Map<String, dynamic>? record;
  final String? motherTagId;
  final List<CattleIsar> existingCattle;

  const DeliveryFormDialog({
    Key? key,
    this.record,
    this.motherTagId,
    required this.existingCattle,
  }) : super(key: key);

  @override
  State<DeliveryFormDialog> createState() => _DeliveryFormDialogState();
}

class _DeliveryFormDialogState extends State<DeliveryFormDialog> {
  final _databaseHelper = DatabaseHelper.instance;
  static const _inputDecorationConstraints = BoxConstraints(maxHeight: 56);
  static const _inputContentPadding =
      EdgeInsets.symmetric(horizontal: 12, vertical: 8);
  static const _animationDuration = Duration(milliseconds: 200);

  final _formKey = GlobalKey<FormState>();
  final _scrollController = ScrollController();
  final _notesController = TextEditingController();
  final List<Map<String, dynamic>> _calvesDetails = [];

  List<CattleIsar> _allCattle = [];
  Map<String, dynamic> _animalTypes = {};
  bool _isLoading = true;
  String? _selectedMotherTagId;
  DateTime _deliveryDate = DateTime.now();
  String _deliveryType = 'Normal';
  String _numberOfCalves = 'Single';

  // Delivery type options
  final List<String> _deliveryTypeOptions = [
    'Normal',
    'Assisted',
    'C-Section',
  ];

  // Number of calves options
  final List<String> _numberOfCalvesOptions = [
    'Single',
    'Twins',
    'Triplets',
  ];

  @override
  void initState() {
    super.initState();
    _logger.info('initState called');
    _loadData();

    // If editing, populate form with existing data
    if (widget.record != null) {
      _logger.info('widget.record is not null');
      _selectedMotherTagId = widget.motherTagId;

      // Handle birthDate which could be a string or DateTime
      if (widget.record!['birthDate'] != null) {
        if (widget.record!['birthDate'] is String) {
          _deliveryDate = DateTime.parse(widget.record!['birthDate']);
        } else {
          _deliveryDate = widget.record!['birthDate'];
        }
      } else {
        _deliveryDate = DateTime.now();
      }

      _deliveryType = widget.record!['deliveryType'] ?? 'Normal';
      _numberOfCalves = widget.record!['numberOfCalves'] ?? 'Single';
      _notesController.text = widget.record!['notes'] ?? '';

      // Load existing calf details if available
      if (widget.record!['calfDetails'] != null &&
          widget.record!['calfDetails'].isNotEmpty) {
        _calvesDetails.addAll(widget.record!['calfDetails']);
      } else {
        _initializeCalvesDetails();
      }
    } else {
      _selectedMotherTagId = widget.motherTagId;
      _initializeCalvesDetails();
    }
  }

  @override
  void dispose() {
    _notesController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _initializeCalvesDetails() {
    // Initialize with one calf by default with auto-generate tag ID enabled
    _calvesDetails.add({
      'name': '',
      'tagId': '',
      'gender': 'Female',
      'autoGenerateTagId': true,
    });
  }

  bool _isTagIdInUse(String tagId) {
    return widget.existingCattle.any((c) => c.tagId == tagId);
  }

  String _generateTagId(String animalTypePrefix) {
    // Get the highest number for this prefix
    int maxNumber = 0;
    for (var cattle in widget.existingCattle) {
      if (cattle.tagId?.startsWith(animalTypePrefix) ?? false) {
        final numberStr = cattle.tagId!.substring(1);
        final number = int.tryParse(numberStr) ?? 0;
        if (number > maxNumber) {
          maxNumber = number;
        }
      }
    }

    // Try numbers from 1 up to maxNumber to find any gaps
    for (int i = 1; i <= maxNumber + 1; i++) {
      final tagId = '$animalTypePrefix$i';
      if (!_isTagIdInUse(tagId)) {
        return tagId;
      }
    }

    return '$animalTypePrefix${maxNumber + 1}';
  }

  void _updateNumberOfCalves(String value) {
    setState(() {
      _numberOfCalves = value;

      // Update calves details based on number selected
      int numCalves = 1;
      if (value == 'Twins') numCalves = 2;
      if (value == 'Triplets') numCalves = 3;

      // Keep existing calves details if available
      while (_calvesDetails.length < numCalves) {
        _calvesDetails.add({
          'name': '',
          'tagId': '',
          'gender': 'Female',
          'autoGenerateTagId': true,
        });
      }

      // Remove extra calves if number decreased
      if (_calvesDetails.length > numCalves) {
        _calvesDetails.removeRange(numCalves, _calvesDetails.length);
      }
    });
  }

  void _updateCalfDetail(int index, String field, dynamic value) {
    setState(() {
      _calvesDetails[index][field] = value;
    });
  }

  Future<void> _loadData() async {
    _logger.info('_loadData called');
    try {
      final cattle = await _databaseHelper.cattleHandler.getAllCattle();
      _logger.info('allCattle loaded: ${cattle.length}');
      final animalTypes = await _databaseHelper.farmSetupHandler.getAllAnimalTypes();

      // Only show female cattle
      final femaleCattle =
          cattle.where((c) => c.gender?.toLowerCase() == 'female').toList();

      if (mounted) {
        setState(() {
          _allCattle = femaleCattle;
          _animalTypes = {for (var type in animalTypes) type.businessId ?? '': type};
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading data: $e')),
        );
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _selectDate(BuildContext context) async {
    final pickedDate = await showDatePicker(
      context: context,
      initialDate: _deliveryDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: ThemeData.light().copyWith(
            colorScheme: const ColorScheme.light(
              primary: Color(0xFF2E7D32),
            ),
          ),
          child: child!,
        );
      },
    );

    if (pickedDate != null && pickedDate != _deliveryDate) {
      setState(() {
        _deliveryDate = pickedDate;
      });
    }
  }

  // Create cattle objects for all calves
  List<CattleIsar> _createCattleForCalves() {
    final List<CattleIsar> newCattle = [];
    final mother = _allCattle.firstWhere(
      (c) => c.tagId == _selectedMotherTagId,
      orElse: () => _allCattle.first,
    );
    final motherAnimalTypeId = mother.animalTypeId;
    final animalType = _animalTypes[motherAnimalTypeId ?? ''];
    final animalTypePrefix =
        animalType?.name?.substring(0, 1).toUpperCase() ?? 'X';

    for (var i = 0; i < _calvesDetails.length; i++) {
      final calfDetail = _calvesDetails[i];
      final name = calfDetail['name'] as String;
      final gender = calfDetail['gender'] as String;
      String tagId = calfDetail['tagId'] as String;

      // Generate tag ID if auto-generate is enabled or if tag ID is empty
      if (calfDetail['autoGenerateTagId'] == true || tagId.isEmpty) {
        tagId = _generateTagId(animalTypePrefix);
        // Update the calves details with the generated tag ID
        _calvesDetails[i]['tagId'] = tagId;
      }

      // Create a new CattleIsar object for each calf
      final newCalf = CattleIsar()
        ..businessId = const Uuid().v4()
        ..name = name
        ..tagId = tagId
        ..gender = CattleGender.fromString(gender)
        ..breedId = mother.breedId // Inherit breed from mother
        ..animalTypeId = motherAnimalTypeId // Inherit animal type from mother
        ..dateOfBirth = _deliveryDate // Use delivery date as birth date
        ..source = CattleSource.fromString('Born at Farm')
        ..motherTagId = _selectedMotherTagId
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now()
        ..category = CattleCategory.fromString('Calf') // Set initial category as Calf
        ..status = CattleStatus.fromString('Active');

      newCattle.add(newCalf);
    }

    return newCattle;
  }

  Future<void> _submit() async {
    if (_formKey.currentState!.validate()) {
      try {
        // Check if the selected cattle is pregnant
        final selectedCattle = _allCattle.firstWhere(
          (c) => c.tagId == _selectedMotherTagId,
          orElse: () => throw Exception('Selected cattle not found'),
        );

        // Check for active pregnancy records instead of relying on breeding status
        if (widget.record == null) {
          try {
            final pregnancyRecords = await _databaseHelper.breedingHandler
                .getPregnancyRecordsForCattle(_selectedMotherTagId!);

            final hasActivePregnancy = pregnancyRecords.any((record) =>
                record.status?.toLowerCase() == 'confirmed' ||
                record.status?.toLowerCase() == 'active');

            if (!hasActivePregnancy && selectedCattle.breedingStatus?.isPregnant != true) {
              // Only show this error for new records, not when editing existing ones
              if (mounted) {
                BreedingMessageUtils.showError(context,
                    'Selected cattle is not pregnant. Cannot record birth.');
              }
              return;
            }
          } catch (e) {
            // If we can't check pregnancy records, fall back to breeding status
            if (selectedCattle.breedingStatus?.isPregnant != true) {
              if (mounted) {
                BreedingMessageUtils.showError(context,
                    'Selected cattle is not pregnant. Cannot record birth.');
              }
              return;
            }
          }
        }

        // Check if the pregnancy has progressed for a sufficient amount of time
        if (widget.record == null) {
          bool isGestationSufficient = false;

          if (selectedCattle.breedingStatus?.breedingDate != null) {
            final animalType = _animalTypes[selectedCattle.animalTypeId ?? ''];
            final gestationDays = animalType?.defaultGestationDays ??
                283; // Default to cow gestation period

            // Calculate minimum required pregnancy duration (at least 80% of full gestation)
            final minimumGestationDays = (gestationDays * 0.8).round();
            final daysSinceBreeding =
                DateTime.now().difference(selectedCattle.breedingStatus!.breedingDate!).inDays;

            if (daysSinceBreeding < minimumGestationDays) {
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                        'Pregnancy is too recent. At least $minimumGestationDays days required, but only $daysSinceBreeding days have passed (${(daysSinceBreeding / gestationDays * 100).toStringAsFixed(1)}% of gestation).'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
              return;
            }

            isGestationSufficient = true;
          } else if (selectedCattle.breedingStatus?.expectedCalvingDate != null) {
            // If breeding date is not available, check expected calving date
            final daysToCalving = selectedCattle.breedingStatus!.expectedCalvingDate!
                .difference(DateTime.now())
                .inDays;

            if (daysToCalving > 30) {
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                        'Too early to record birth. Expected calving date is ${DateFormat('MMM dd, yyyy').format(selectedCattle.breedingStatus!.expectedCalvingDate!)} (${daysToCalving - 30} days until recording is available).'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
              return;
            }

            isGestationSufficient = true;
          }

          // If neither breeding date nor expected calving date is available
          if (!isGestationSufficient && selectedCattle.breedingStatus?.isPregnant == true) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text(
                      'Cannot verify pregnancy duration. Please update the breeding date or expected calving date for this animal.'),
                  backgroundColor: Colors.orange,
                ),
              );
            }
            // Allow to continue in this case, but with a warning
          }
        }

        // Create cattle objects for each calf
        final newCalves = _createCattleForCalves();

        // Create updated breeding record with delivery details
        final updatedRecord = widget.record != null
            ? {
                'id': widget.record!['id'], // Ensure ID is preserved
                'date': _parseDate(widget.record!['date']),
                'type': widget.record!['type'] ??
                    'Natural', // Provide default value
                'status': widget.record!['status'] ??
                    'Completed', // Use status instead of breedingStatus
                'breedingStatus': widget.record!['breedingStatus'] ??
                    'Completed', // Provide default for backward compatibility
                'partner': widget.record!['partner'],
                'cost': widget.record!['cost'],
                'expectedCalvingDate':
                    _parseDate(widget.record!['expectedCalvingDate']),
                'nextHeatDate': _parseDate(widget.record!['nextHeatDate']),
                'notes': widget.record!['notes'],
                'birthDate': _deliveryDate,
                'birthRecorded': true,
                'numberOfCalves': _numberOfCalves,
                'deliveryType': _deliveryType,
                'calfDetails': _calvesDetails,
              }
            : null;

        // Create result data
        final result = {
          'motherTagId': _selectedMotherTagId!,
          'deliveryDate': _deliveryDate.toIso8601String(),
          'deliveryType': _deliveryType,
          'numberOfCalves': _numberOfCalves,
          'notes': widget.record == null
              ? _notesController.text.isEmpty
                  ? null
                  : _notesController.text
              : widget.record!['notes'],
          'calves': newCalves,
          'updatedRecord': updatedRecord,
        };

        // Return the result to the caller
        if (mounted) {
          Navigator.of(context).pop(result);
        }
      } catch (e) {
        if (mounted) {
          BreedingMessageUtils.showError(context, 'Error: $e');
        }
      }
    }
  }

  DateTime? _parseDate(dynamic date) {
    if (date == null) return null;
    if (date is DateTime) return date;
    if (date is String) {
      try {
        return DateTime.parse(date);
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: AnimatedContainer(
        duration: _animationDuration,
        curve: Curves.easeInOut,
        decoration: BoxDecoration(
          color: Theme.of(context).dialogTheme.backgroundColor ?? Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
        ),
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.9,
          maxHeight: MediaQuery.of(context).size.height * 0.9,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                color: Color(0xFF2E7D32), // Primary green color
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Column(
                children: [
                  Text(
                    widget.record == null
                        ? 'Record Calving'
                        : 'Edit Calving Record',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  if (_selectedMotherTagId != null)
                    Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Text(
                        'Mother: $_selectedMotherTagId',
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                ],
              ),
            ),

            // Content
            Flexible(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : Form(
                      key: _formKey,
                      child: SingleChildScrollView(
                        controller: _scrollController,
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Mother selection
                            DropdownButtonFormField<String>(
                              decoration: InputDecoration(
                                labelText: 'Mother',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                prefixIcon: const Icon(
                                  Icons.female,
                                  color: Colors.pink,
                                ),
                              ),
                              value: _selectedMotherTagId,
                              items: _allCattle.map((cattle) {
                                return DropdownMenuItem<String>(
                                  value: cattle.tagId,
                                  child: Text(
                                      '${cattle.name ?? 'No name'} (${cattle.tagId ?? 'No ID'})'),
                                );
                              }).toList(),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please select a mother';
                                }
                                return null;
                              },
                              onChanged: widget.record != null
                                  ? null // Disable if editing existing record
                                  : (value) {
                                      setState(() {
                                        _selectedMotherTagId = value;
                                      });
                                    },
                            ),
                            const SizedBox(height: 16),

                            // Delivery date
                            InkWell(
                              onTap: () => _selectDate(context),
                              child: InputDecorator(
                                decoration: InputDecoration(
                                  labelText: 'Delivery Date',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  prefixIcon: const Icon(
                                    Icons.calendar_today,
                                    color: Colors.green,
                                  ),
                                ),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      DateFormat('MMMM dd, yyyy')
                                          .format(_deliveryDate),
                                    ),
                                    Icon(
                                      Icons.arrow_drop_down,
                                      color: Colors.grey[600],
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            const SizedBox(height: 16),

                            // Delivery type
                            DropdownButtonFormField<String>(
                              decoration: const InputDecoration(
                                labelText: 'Delivery Type',
                                border: OutlineInputBorder(),
                                prefixIcon: Icon(Icons.medical_services),
                              ),
                              value: _deliveryType,
                              items: _deliveryTypeOptions.map((type) {
                                return DropdownMenuItem<String>(
                                  value: type,
                                  child: Text(type),
                                );
                              }).toList(),
                              onChanged: (value) {
                                if (value != null) {
                                  setState(() {
                                    _deliveryType = value;
                                  });
                                }
                              },
                            ),
                            const SizedBox(height: 16),

                            // Number of calves
                            DropdownButtonFormField<String>(
                              decoration: const InputDecoration(
                                labelText: 'Number of Calves',
                                border: OutlineInputBorder(),
                                prefixIcon: Icon(Icons.group),
                              ),
                              value: _numberOfCalves,
                              items: _numberOfCalvesOptions.map((number) {
                                return DropdownMenuItem<String>(
                                  value: number,
                                  child: Text(number),
                                );
                              }).toList(),
                              onChanged: (value) {
                                if (value != null) {
                                  _updateNumberOfCalves(value);
                                }
                              },
                            ),
                            const SizedBox(height: 20),

                            // Calves details
                            ..._buildCalvesDetailsWidgets(),

                            // Notes
                            Padding(
                              padding: const EdgeInsets.only(top: 16),
                              child: TextFormField(
                                controller: _notesController,
                                decoration: InputDecoration(
                                  labelText: 'Notes',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  alignLabelWithHint: true,
                                  prefixIcon: const Icon(
                                    Icons.note_alt,
                                    color: Colors.deepOrange,
                                  ),
                                ),
                                maxLines: 3,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
            ),

            // Actions
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('Cancel'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _submit,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF2E7D32),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        widget.record == null ? 'Record Birth' : 'Update Record',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildCalvesDetailsWidgets() {
    final widgets = <Widget>[];

    for (var i = 0; i < _calvesDetails.length; i++) {
      final isMultiple = _calvesDetails.length > 1;
      final calfDetail = _calvesDetails[i];

      widgets.add(
        Container(
          margin: const EdgeInsets.only(bottom: 16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Calf number header
              if (isMultiple)
                Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: Text(
                    'Calf ${i + 1}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ),

              // Gender
              DropdownButtonFormField<String>(
                decoration: const InputDecoration(
                  labelText: 'Gender',
                  border: OutlineInputBorder(),
                  constraints: _inputDecorationConstraints,
                  contentPadding: _inputContentPadding,
                ),
                value: calfDetail['gender'] as String,
                items: const [
                  DropdownMenuItem(value: 'Female', child: Text('Female')),
                  DropdownMenuItem(value: 'Male', child: Text('Male')),
                ],
                onChanged: (value) {
                  if (value != null) {
                    _updateCalfDetail(i, 'gender', value);
                  }
                },
              ),
              const SizedBox(height: 16),

              // Tag ID with auto-generate option
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: TextFormField(
                      initialValue: calfDetail['tagId'] as String,
                      enabled: !(calfDetail['autoGenerateTagId'] as bool),
                      decoration: const InputDecoration(
                        labelText: 'Tag ID',
                        border: OutlineInputBorder(),
                        constraints: _inputDecorationConstraints,
                        contentPadding: _inputContentPadding,
                      ),
                      validator: (value) {
                        if (calfDetail['autoGenerateTagId'] as bool) {
                          return null;
                        }
                        if (value == null || value.isEmpty) {
                          return 'Tag ID is required';
                        }
                        if (_isTagIdInUse(value) &&
                            widget.record == null) {
                          return 'Tag ID already in use';
                        }
                        return null;
                      },
                      onChanged: (value) {
                        _updateCalfDetail(i, 'tagId', value);
                      },
                    ),
                  ),
                  const SizedBox(width: 12),
                  SizedBox(
                    height: 56,
                    child: ElevatedButton.icon(
                      onPressed: () {
                        final currentState =
                            calfDetail['autoGenerateTagId'] as bool;
                        _updateCalfDetail(i, 'autoGenerateTagId', !currentState);
                      },
                      icon: Icon(
                        calfDetail['autoGenerateTagId'] as bool
                            ? Icons.app_registration
                            : Icons.edit,
                        size: 18,
                      ),
                      label: Text(
                        calfDetail['autoGenerateTagId'] as bool
                            ? 'Auto'
                            : 'Manual',
                        style: const TextStyle(fontSize: 12),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor:
                            calfDetail['autoGenerateTagId'] as bool
                                ? Colors.green
                                : Colors.orange,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              if (calfDetail['autoGenerateTagId'] as bool)
                Padding(
                  padding: const EdgeInsets.only(top: 4, left: 12),
                  child: Text(
                    'Tag ID will be generated automatically',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ),
              const SizedBox(height: 16),

              // Name
              TextFormField(
                initialValue: calfDetail['name'] as String,
                decoration: const InputDecoration(
                  labelText: 'Name (Optional)',
                  border: OutlineInputBorder(),
                  constraints: _inputDecorationConstraints,
                  contentPadding: _inputContentPadding,
                ),
                onChanged: (value) {
                  _updateCalfDetail(i, 'name', value);
                },
              ),
            ],
          ),
        ),
      );
    }

    return widgets;
  }
}
*/
