import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:isar/isar.dart';
import 'package:async/async.dart';
import '../models/weight_record_isar.dart';
import '../models/weight_insights_models.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../services/weight_repository.dart';
import '../services/weight_insights_service.dart';
import '../../Cattle/services/cattle_repository.dart';
import '../../Cattle/controllers/cattle_controller.dart'; // Import for ControllerState enum

/// Enum for sort criteria
enum WeightSortBy {
  measurementDate,
  weight,
  cattleName,
  createdAt,
}

/// Enum for sort direction
enum SortDirection {
  ascending,
  descending,
}

/// Reactive controller for the main weight screen using Dual-Stream Pattern
/// Following the cattle module template: separate unfiltered and filtered streams
/// Unfiltered stream feeds analytics, filtered stream feeds UI
class WeightController with ChangeNotifier {
  // Repositories
  final WeightRepository _weightRepo = GetIt.I<WeightRepository>();
  final CattleRepository _cattleRepo = GetIt.I<CattleRepository>();
  final Isar _isar = GetIt.I<Isar>();

  // State management
  ControllerState _state = ControllerState.loading;
  String? _errorMessage;

  // Stream subscriptions for real-time updates - Separated for filtered/unfiltered data
  StreamSubscription? _unfilteredStreamSubscription;
  StreamSubscription? _filteredStreamSubscription;

  // Search debounce timer
  Timer? _searchDebounceTimer;
  static const Duration _searchDebounceDelay = Duration(milliseconds: 300);

  // Data - Critical separation: filtered data for UI, unfiltered for analytics
  List<WeightRecordIsar> _unfilteredRecords = []; // Complete dataset for analytics calculations
  List<WeightRecordIsar> _filteredRecords = []; // Filtered dataset for UI display
  List<CattleIsar> _unfilteredCattle = []; // Complete cattle dataset for analytics
  bool _hasActiveFilters = false; // Track if filters are currently applied

  // Analytics result from the dedicated service - ALWAYS calculated on unfiltered data
  WeightInsightsData _insights = WeightInsightsData.empty;

  // Filter state
  DateTime? _startDate;
  DateTime? _endDate;
  String _searchQuery = '';

  // Sorting state
  WeightSortBy _sortBy = WeightSortBy.measurementDate;
  SortDirection _sortDirection = SortDirection.descending;

  // Getters
  ControllerState get state => _state;
  String? get errorMessage => _errorMessage;

  /// Returns the filtered weight records for UI display
  /// This is what the WeightRecordsTab should show
  List<WeightRecordIsar> get weightRecords => List.unmodifiable(_filteredRecords);

  /// Returns the complete unfiltered weight records for analytics
  /// This ensures analytics are always calculated on the full dataset
  List<WeightRecordIsar> get unfilteredWeightRecords => List.unmodifiable(_unfilteredRecords);

  /// Returns the complete unfiltered cattle list for analytics
  List<CattleIsar> get unfilteredCattle => List.unmodifiable(_unfilteredCattle);

  // Main analytics object - single source of truth
  WeightInsightsData get insights => _insights;

  // Filter getters
  DateTime? get startDate => _startDate;
  DateTime? get endDate => _endDate;
  String get searchQuery => _searchQuery;
  bool get hasData => _unfilteredRecords.isNotEmpty;
  WeightSortBy get sortBy => _sortBy;
  SortDirection get sortDirection => _sortDirection;

  WeightController() {
    _initializeStreamListeners();
  }

  /// Initialize stream listeners for real-time updates using Dual-Stream Pattern
  /// Critical: Separate streams for unfiltered (analytics) and filtered (UI) data
  void _initializeStreamListeners() {
    // Primary stream: Unfiltered data for analytics calculations
    // This stream NEVER changes and always provides the complete dataset
    // Using StreamZip for optimal efficiency - only fires when either weights or cattle change
    final combinedStream = StreamZip([
      _isar.weightRecordIsars.where().watch(fireImmediately: true),
      _cattleRepo.watchAllCattle(),
    ]);

    _unfilteredStreamSubscription = combinedStream.listen((data) {
      _handleUnfilteredDataUpdate(
        data[0] as List<WeightRecordIsar>,
        data[1] as List<CattleIsar>,
      );
    });

    // Initially, filtered data equals unfiltered data (no filters applied)
    _filteredRecords = _unfilteredRecords;
    _hasActiveFilters = false;
  }

  /// Handle unfiltered data updates - Used for analytics calculations
  /// Critical: This method ONLY updates analytics and unfiltered data
  /// Optimized: Receives cattle data directly from StreamZip, eliminating redundant fetches
  void _handleUnfilteredDataUpdate(
    List<WeightRecordIsar> unfilteredRecords,
    List<CattleIsar> unfilteredCattle,
  ) async {
    try {
      // Update the complete unfiltered datasets
      _unfilteredRecords = unfilteredRecords;
      _unfilteredCattle = unfilteredCattle;

      // ALWAYS calculate analytics on the complete unfiltered dataset
      // This ensures analytics remain accurate regardless of applied filters
      if (_unfilteredRecords.isEmpty) {
        _insights = WeightInsightsData.empty;
      } else {
        _calculateAnalytics();
      }

      // If no filters are active, update filtered data to match unfiltered
      if (!_hasActiveFilters) {
        _filteredRecords = List.from(_unfilteredRecords);
      }

      _setState(ControllerState.loaded);
      notifyListeners();
    } catch (e) {
      debugPrint('Error handling unfiltered data update: $e');
      _setState(ControllerState.error);
      _errorMessage = 'Failed to update unfiltered data: $e';
      notifyListeners();
    }
  }



  /// Calculate analytics using the dedicated service
  /// Critical: ALWAYS uses unfiltered data to ensure accurate analytics
  void _calculateAnalytics() {
    _insights = WeightInsightsService.calculateInsights(
      _unfilteredRecords, // Use unfiltered data for accurate analytics
      _unfilteredCattle,
    );
  }

  /// Apply filters at the database level for ultimate scalability
  /// Critical Fix: This method now creates a separate filtered stream without affecting analytics
  void applyFilters() async {
    // Cancel existing filtered subscription (but keep unfiltered stream for analytics)
    _filteredStreamSubscription?.cancel();

    // Check if filters are active
    final hasFilters = _searchQuery.isNotEmpty ||
                      _startDate != null ||
                      _endDate != null;

    _hasActiveFilters = hasFilters;

    if (hasFilters) {
      // Build filtered query for UI display
      final filteredQuery = _buildFilteredQuery();

      // Create separate stream for filtered data
      _filteredStreamSubscription = filteredQuery.watch(fireImmediately: true)
          .listen((filteredList) {
        _handleFilteredDataUpdate(filteredList);
      });
    } else {
      // No filters: filtered data equals unfiltered data
      _filteredRecords = List.from(_unfilteredRecords);
      _applySorting(_filteredRecords);
      notifyListeners();
    }
  }

  /// Handle filtered data updates - Used for UI display only
  /// Critical: This method NEVER affects analytics calculations
  void _handleFilteredDataUpdate(List<WeightRecordIsar> filteredList) async {
    try {
      // Update only the filtered dataset for UI display
      _filteredRecords = filteredList;

      // Apply sorting to filtered data
      _applySorting(_filteredRecords);

      // Do NOT recalculate analytics here - they're handled by unfiltered stream
      // This ensures analytics always reflect the complete dataset

      notifyListeners();
    } catch (e) {
      debugPrint('Error handling filtered data update: $e');
      // Don't change state to error for filtered data issues
      notifyListeners();
    }
  }

  /// Build filtered Isar query based on current filter state
  /// This method dynamically constructs database queries for optimal performance
  dynamic _buildFilteredQuery() {
    // Start with base query and build the chain using dynamic typing
    dynamic currentQuery = _isar.weightRecordIsars.where();

    // Apply search filter at database level - searches cattle name and tagId
    if (_searchQuery.isNotEmpty) {
      final searchTerm = _searchQuery.toLowerCase();
      currentQuery = currentQuery.filter().cattle((q) => q
          .nameContains(searchTerm, caseSensitive: false)
          .or()
          .tagIdContains(searchTerm, caseSensitive: false));
    }

    // Apply date range filters at database level
    if (_startDate != null) {
      currentQuery = currentQuery.filter().measurementDateGreaterThan(_startDate!);
    }
    if (_endDate != null) {
      // Add one day to make end date inclusive
      final inclusiveEndDate = _endDate!.add(const Duration(days: 1));
      currentQuery = currentQuery.filter().measurementDateLessThan(inclusiveEndDate);
    }

    // Apply sorting at database level for optimal performance
    switch (_sortBy) {
      case WeightSortBy.measurementDate:
        currentQuery = _sortDirection == SortDirection.ascending
            ? currentQuery.sortByMeasurementDate()
            : currentQuery.sortByMeasurementDateDesc();
        break;
      case WeightSortBy.weight:
        currentQuery = _sortDirection == SortDirection.ascending
            ? currentQuery.sortByWeight()
            : currentQuery.sortByWeightDesc();
        break;
      case WeightSortBy.createdAt:
        currentQuery = _sortDirection == SortDirection.ascending
            ? currentQuery.sortByCreatedAt()
            : currentQuery.sortByCreatedAtDesc();
        break;
      default:
        currentQuery = currentQuery.sortByMeasurementDateDesc(); // Default sort
    }

    return currentQuery;
  }

  /// Apply sorting to the records list (for client-side sorting)
  void _applySorting(List<WeightRecordIsar> records) {
    records.sort((a, b) {
      int comparison = 0;

      switch (_sortBy) {
        case WeightSortBy.measurementDate:
          comparison = (a.measurementDate ?? DateTime(0))
              .compareTo(b.measurementDate ?? DateTime(0));
          break;
        case WeightSortBy.weight:
          comparison = (a.weight ?? 0).compareTo(b.weight ?? 0);
          break;
        case WeightSortBy.cattleName:
          final nameA = a.cattle.value?.name ?? '';
          final nameB = b.cattle.value?.name ?? '';
          comparison = nameA.compareTo(nameB);
          break;
        case WeightSortBy.createdAt:
          comparison = (a.createdAt ?? DateTime(0))
              .compareTo(b.createdAt ?? DateTime(0));
          break;
      }

      // Apply sort direction
      if (_sortDirection == SortDirection.descending) {
        comparison = -comparison;
      }

      // Secondary sort by createdAt if primary comparison is equal
      if (comparison == 0) {
        final secondaryComparison = (a.createdAt ?? DateTime(0))
            .compareTo(b.createdAt ?? DateTime(0));
        return _sortDirection == SortDirection.descending
            ? -secondaryComparison
            : secondaryComparison;
      }

      return comparison;
    });
  }

  /// Set date range filter
  void setDateRange(DateTime? startDate, DateTime? endDate) {
    if (_startDate == startDate && _endDate == endDate) return;

    _startDate = startDate;
    _endDate = endDate;
    applyFilters();
  }

  /// Set search query filter with debouncing
  void setSearchQuery(String query) {
    if (_searchQuery == query) return;

    _searchQuery = query;

    // Cancel previous timer if it exists
    _searchDebounceTimer?.cancel();

    // Set up new debounced timer
    _searchDebounceTimer = Timer(_searchDebounceDelay, () {
      applyFilters();
    });
  }

  /// Set sorting criteria
  void setSort(WeightSortBy sortBy, SortDirection direction) {
    if (_sortBy == sortBy && _sortDirection == direction) return;

    _sortBy = sortBy;
    _sortDirection = direction;
    applyFilters();
  }

  /// Clear all filters
  void clearFilters() {
    _startDate = null;
    _endDate = null;
    _searchQuery = '';

    // Cancel any pending search debounce
    _searchDebounceTimer?.cancel();

    applyFilters();
  }

  // State management helpers
  void _setState(ControllerState newState) {
    _state = newState;
    _errorMessage = null;
    notifyListeners();
  }

  void _setError(String error) {
    _state = ControllerState.error;
    _errorMessage = error;
    notifyListeners();
  }

  @override
  void dispose() {
    _searchDebounceTimer?.cancel();
    _unfilteredStreamSubscription?.cancel();
    _filteredStreamSubscription?.cancel();
    super.dispose();
  }
}
