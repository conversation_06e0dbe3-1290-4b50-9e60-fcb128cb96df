import 'package:isar/isar.dart';

part 'notification_isar.g.dart';

/// Represents a notification in the system
@collection
class NotificationIsar {
  Id id = Isar.autoIncrement;

  /// Unique business identifier for the notification
  @Index(unique: true)
  String? businessId;

  /// Type of notification (e.g., 'health', 'breeding', etc.)
  @Index()
  String? type;

  /// Title of the notification
  String? title;

  /// Main content of the notification
  String? message;

  /// Whether the notification has been read
  @Index()
  bool isRead = false;

  /// Time when the notification was created
  @Index()
  DateTime? createdAt;

  /// Time when the notification was read
  DateTime? readAt;

  /// Related cattle ID if applicable
  @Index()
  String? cattleId;

  /// Related record ID if applicable
  String? recordId;

  /// Priority level (e.g., 'high', 'medium', 'low')
  @Index()
  String? priority;

  /// Constructor
  NotificationIsar({
    this.businessId,
    this.type,
    this.title,
    this.message,
    this.isRead = false,
    this.createdAt,
    this.readAt,
    this.cattleId,
    this.recordId,
    this.priority,
  });
} 