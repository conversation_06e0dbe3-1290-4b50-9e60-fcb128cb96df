import 'dart:convert';
import 'package:googleapis/drive/v3.dart' as drive;
import 'package:googleapis_auth/auth_io.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

class GoogleDriveService {
  static const _clientId =
      '1234567890-abcdefghijklmnopqrstuvwxyz.apps.googleusercontent.com';
  static const _clientSecret = 'GOCSPX-abcdefghijklmnopqrstuvwxyz1234';
  static const _scopes = [drive.DriveApi.driveFileScope];

  late drive.DriveApi _driveApi;
  late AuthClient _client;

  static final GoogleDriveService _instance = GoogleDriveService._internal();

  factory GoogleDriveService() {
    return _instance;
  }

  GoogleDriveService._internal();

  Future<bool> initialize() async {
    try {
      final clientId = ClientId(_clientId, _clientSecret);
      _client =
          await clientViaUserConsent(clientId, _scopes, _promptForConsent);
      _driveApi = drive.DriveApi(_client);
      return true;
    } catch (e) {
      debugPrint('Error initializing Google Drive service: $e');
      return false;
    }
  }

  void _promptForConsent(String url) {
    debugPrint('Please go to the following URL and grant access:');
    debugPrint(url);
    debugPrint('\nAfter granting access, return here and press enter.');
  }

  Future<String?> uploadBackup(String farmId, Map<String, dynamic> data) async {
    try {
      final timestamp = DateTime.now().toIso8601String();
      final fileName = 'cattle_manager_backup_${farmId}_$timestamp.json';
      final content = jsonEncode(data);

      final file = drive.File()
        ..name = fileName
        ..mimeType = 'application/json';

      final media = drive.Media(
        Stream.fromIterable([utf8.encode(content)]),
        content.length,
        contentType: 'application/json',
      );

      final result = await _driveApi.files.create(file, uploadMedia: media);
      return result.id;
    } catch (e) {
      debugPrint('Error uploading backup to Google Drive: $e');
      return null;
    }
  }

  Future<Map<String, dynamic>?> downloadBackup(String fileId) async {
    try {
      final content = await _driveApi.files.get(fileId,
          downloadOptions: drive.DownloadOptions.fullMedia) as drive.Media;

      final bytes = await content.stream.toList();
      final jsonString = utf8.decode(bytes.expand((x) => x).toList());
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      debugPrint('Error downloading backup from Google Drive: $e');
      return null;
    }
  }

  Future<List<drive.File>> listBackups() async {
    try {
      final result = await _driveApi.files.list(
        q: "name contains 'cattle_manager_backup' and mimeType = 'application/json'",
        orderBy: 'createdTime desc',
      );

      return result.files ?? [];
    } catch (e) {
      debugPrint('Error listing backups from Google Drive: $e');
      return [];
    }
  }

  Future<bool> deleteBackup(String fileId) async {
    try {
      await _driveApi.files.delete(fileId);
      return true;
    } catch (e) {
      debugPrint('Error deleting backup from Google Drive: $e');
      return false;
    }
  }

  Future<void> signOut() async {
    try {
      _client.close();
      _driveApi = drive.DriveApi(http.Client());
      _client = http.Client() as AuthClient;
    } catch (e) {
      debugPrint('Error signing out from Google Drive: $e');
    }
  }

  void dispose() {
    _client.close();
  }
}
