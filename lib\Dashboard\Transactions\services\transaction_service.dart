import '../models/transaction_isar.dart';
import 'package:get_it/get_it.dart';
import 'transactions_repository.dart';

class TransactionService {
  late final TransactionsRepository _transactionsRepository;

  TransactionService() {
    _initialize();
  }

  void _initialize() {
    _transactionsRepository = GetIt.instance<TransactionsRepository>();
  }

  Future<List<TransactionIsar>> getTransactions() async {
    return await GetIt.instance<Isar>().transactionIsars.where().findAll();
  }

  Future<void> addTransaction(TransactionIsar transaction) async {
    await _transactionsRepository.saveTransaction(transaction);
  }

  Future<void> updateTransaction(TransactionIsar transaction) async {
    await _transactionsRepository.saveTransaction(transaction);
  }

  Future<void> deleteTransaction(String transactionId) async {
    // Find transaction by businessId and delete by Isar ID
    final isar = GetIt.instance<Isar>();
    final transaction = await isar.transactionIsars
        .filter()
        .businessIdEqualTo(transactionId)
        .findFirst();
    if (transaction != null) {
      await _transactionsRepository.deleteTransaction(transaction.id);
    }
  }

  Future<List<TransactionIsar>> getTransactionsByType(
      String categoryType) async {
    final isar = GetIt.instance<Isar>();
    return await isar.transactionIsars
        .filter()
        .categoryTypeEqualTo(categoryType)
        .findAll();
  }

  Future<List<TransactionIsar>> getTransactionsByCategory(
      String category) async {
    final isar = GetIt.instance<Isar>();
    return await isar.transactionIsars
        .filter()
        .categoryEqualTo(category)
        .findAll();
  }

  Future<List<TransactionIsar>> getTransactionsByDateRange(
      DateTime startDate, DateTime endDate) async {
    final isar = GetIt.instance<Isar>();
    return await isar.transactionIsars
        .filter()
        .dateBetween(startDate, endDate)
        .findAll();
  }
}
