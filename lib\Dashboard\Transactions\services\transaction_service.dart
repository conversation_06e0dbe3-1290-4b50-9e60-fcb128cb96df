import '../models/transaction_isar.dart';
import 'package:get_it/get_it.dart';
import 'transactions_repository.dart';

class TransactionService {
  late final TransactionsRepository _transactionsRepository;

  TransactionService() {
    _initialize();
  }

  void _initialize() {
    _transactionsRepository = GetIt.instance<TransactionsRepository>();
  }

  Future<List<TransactionIsar>> getTransactions() async {
    return await _transactionsRepository.getAllTransactions();
  }

  Future<void> addTransaction(TransactionIsar transaction) async {
    await _transactionsRepository.addTransaction(transaction);
  }

  Future<void> updateTransaction(TransactionIsar transaction) async {
    await _transactionsRepository.updateTransaction(transaction);
  }

  Future<void> deleteTransaction(String transactionId) async {
    await _transactionsRepository.deleteTransaction(transactionId);
  }

  Future<List<TransactionIsar>> getTransactionsByType(
      String categoryType) async {
    return await _transactionsRepository.getTransactionsByType(categoryType);
  }

  Future<List<TransactionIsar>> getTransactionsByCategory(
      String category) async {
    return await _transactionsRepository.getTransactionsByCategory(category);
  }

  Future<List<TransactionIsar>> getTransactionsByDateRange(
      DateTime startDate, DateTime endDate) async {
    return await _transactionsRepository.getTransactionsForDateRange(
        startDate, endDate);
  }
}
