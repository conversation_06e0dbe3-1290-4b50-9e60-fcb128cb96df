import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import 'chart_data_isar.dart';

/// Base mixin class for report data with shared fields and methods
mixin ReportDataIsarMixin {
  String? businessId;
  String? reportType; // Type of report: 'cattle', 'milk', 'weight', etc.
  String? title;
  DateTime? startDate;
  DateTime? endDate;
  String? filterCriteria;
  DateTime? generatedAt;
  DateTime? createdAt;
  DateTime? updatedAt;

  /// Initialize basic report properties
  void initializeReport({
    required String reportType,
    required String title,
    DateTime? startDate,
    DateTime? endDate,
    String? filterCriteria,
  }) {
    final now = DateTime.now();
    businessId = const Uuid().v4();
    this.reportType = reportType;
    this.title = title;
    this.startDate = startDate;
    this.endDate = endDate;
    this.filterCriteria = filterCriteria;
    generatedAt = now;
    createdAt = now;
    updatedAt = now;
  }

  /// Base implementation of toMap
  Map<String, dynamic> toMap() {
    return {
      'id': businessId,
      'reportType': reportType,
      'title': title,
      'startDate': startDate?.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'filterCriteria': filterCriteria,
      'generatedAt': generatedAt?.toIso8601String(),
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  /// Initialize from map
  void initFromMap(Map<String, dynamic> map) {
    businessId = map['id'] as String?;
    reportType = map['reportType'] as String?;
    title = map['title'] as String?;
    startDate = map['startDate'] != null
        ? DateTime.parse(map['startDate'] as String)
        : null;
    endDate = map['endDate'] != null
        ? DateTime.parse(map['endDate'] as String)
        : null;
    filterCriteria = map['filterCriteria'] as String?;
    generatedAt = map['generatedAt'] != null
        ? DateTime.parse(map['generatedAt'] as String)
        : null;
    createdAt = map['createdAt'] != null
        ? DateTime.parse(map['createdAt'] as String)
        : DateTime.now();
    updatedAt = map['updatedAt'] != null
        ? DateTime.parse(map['updatedAt'] as String)
        : DateTime.now();
  }

  /// Abstract methods that each report type must implement in inheriting classes
  List<DataColumn> getTableColumns() {
    throw UnimplementedError(
        'getTableColumns must be implemented by subclasses');
  }

  List<DataRow> getTableRows() {
    throw UnimplementedError('getTableRows must be implemented by subclasses');
  }

  Map<String, dynamic> getSummaryData() {
    throw UnimplementedError(
        'getSummaryData must be implemented by subclasses');
  }

  List<ChartDataIsar> getChartData() {
    throw UnimplementedError('getChartData must be implemented by subclasses');
  }
}
