import 'package:isar/isar.dart';

part 'milk_record_isar.g.dart';

@collection
class MilkRecordIsar {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  String? businessId; // Renamed from recordId to match naming convention

  @Index()
  String? cattleBusinessId; // For compatibility with cattleBusinessId

  @Index()
  String? cattleTagId; // For compatibility with cattleTagId

  @Index()
  DateTime? date;

  double? morningAmount = 0; // Renamed from morning

  double? afternoonAmount = 0; // Renamed from afternoon

  double? eveningAmount = 0; // Renamed from evening

  double? fatContent; // Added from cattle model

  double? totalAmount; // Added from cattle model

  double? pricePerLiter; // Added from cattle model

  String? notes;

  DateTime? recordedAt;

  DateTime? createdAt;

  DateTime? updatedAt;

  // Additional properties for compatibility
  String? session;

  MilkRecordIsar();

  // Backward compatibility getters and setters
  double? get morning => morningAmount;
  set morning(double? value) => morningAmount = value;

  double? get afternoon => afternoonAmount;
  set afternoon(double? value) => afternoonAmount = value;

  double? get evening => eveningAmount;
  set evening(double? value) => eveningAmount = value;

  String? get recordId => businessId;
  set recordId(String? value) => businessId = value;

  String? get cattleId => cattleBusinessId;
  set cattleId(String? value) => cattleBusinessId = value;

  double? get morningYield => morningAmount;
  set morningYield(double? value) => morningAmount = value;

  double? get eveningYield => eveningAmount;
  set eveningYield(double? value) => eveningAmount = value;

  String? get tagId => cattleTagId;
  set tagId(String? value) => cattleTagId = value;

  // Additional compatibility getters
  double? get quantity => totalAmount ?? totalYield;
  set quantity(double? value) => totalAmount = value;

  // Calculate total yield (computed property)
  double get totalYield =>
      (morningAmount ?? 0) + (afternoonAmount ?? 0) + (eveningAmount ?? 0);

  /// Generate a deterministic business ID for milk records to ensure consistency
  /// across app reinstallations. Based on cattle ID and date.
  static String generateBusinessId(String cattleBusinessId, DateTime date) {
    // Format the date in a consistent way (YYYYMMDD)
    final dateStr =
        "${date.year}${date.month.toString().padLeft(2, '0')}${date.day.toString().padLeft(2, '0')}";

    // Create a unique ID combining cattle ID and date
    final uniqueKey = "$cattleBusinessId-$dateStr";

    // Return with a prefix to distinguish milk records
    return "milk_$uniqueKey";
  }

  factory MilkRecordIsar.create({
    String? businessId,
    required String cattleBusinessId,
    String? cattleTagId,
    required DateTime date,
    required double morningAmount,
    double? afternoonAmount,
    required double eveningAmount,
    double? fatContent,
    double? pricePerLiter,
    String? notes,
  }) {
    return MilkRecordIsar()
      ..businessId = businessId ?? generateBusinessId(cattleBusinessId, date)
      ..cattleBusinessId = cattleBusinessId
      ..cattleTagId = cattleTagId
      ..date = date
      ..morningAmount = morningAmount
      ..afternoonAmount = afternoonAmount ?? 0
      ..eveningAmount = eveningAmount
      ..fatContent = fatContent
      ..totalAmount = morningAmount + (afternoonAmount ?? 0) + eveningAmount
      ..pricePerLiter = pricePerLiter
      ..notes = notes
      ..recordedAt = DateTime.now()
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();
  }

  // Legacy factory constructor for backward compatibility
  factory MilkRecordIsar.createLegacy({
    required String recordId,
    required String cattleId,
    required double morning,
    required double afternoon,
    required double evening,
    required DateTime date,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return MilkRecordIsar()
      ..businessId = recordId
      ..cattleBusinessId = cattleId
      ..date = date
      ..morningAmount = morning
      ..afternoonAmount = afternoon
      ..eveningAmount = evening
      ..totalAmount = morning + afternoon + evening
      ..notes = notes
      ..createdAt = createdAt ?? DateTime.now()
      ..updatedAt = updatedAt ?? DateTime.now();
  }

  Map<String, dynamic> toMap() {
    return {
      'id': businessId,
      'cattleBusinessId': cattleBusinessId,
      'cattleTagId': cattleTagId,
      'date': date?.toIso8601String(),
      'morningAmount': morningAmount,
      'afternoonAmount': afternoonAmount,
      'eveningAmount': eveningAmount,
      'fatContent': fatContent,
      'totalAmount': totalAmount,
      'pricePerLiter': pricePerLiter,
      'notes': notes,
      'recordedAt': recordedAt?.toIso8601String(),
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  Map<String, dynamic> toJson() => toMap();

  factory MilkRecordIsar.fromMap(Map<String, dynamic> map) {
    final morningVal =
        map['morningAmount'] ?? map['morningYield'] ?? map['morning'];
    final eveningVal =
        map['eveningAmount'] ?? map['eveningYield'] ?? map['evening'];
    final afternoonVal = map['afternoonAmount'] ?? map['afternoon'] ?? 0.0;

    return MilkRecordIsar()
      ..businessId = map['id'] as String?
      ..cattleBusinessId =
          map['cattleBusinessId'] as String? ?? map['cattleId'] as String?
      ..cattleTagId = map['cattleTagId'] as String? ?? map['tagId'] as String?
      ..date =
          map['date'] != null ? DateTime.parse(map['date'].toString()) : null
      ..morningAmount =
          morningVal != null ? double.parse(morningVal.toString()) : null
      ..afternoonAmount =
          afternoonVal != null ? double.parse(afternoonVal.toString()) : 0.0
      ..eveningAmount =
          eveningVal != null ? double.parse(eveningVal.toString()) : null
      ..fatContent = map['fatContent'] != null
          ? double.parse(map['fatContent'].toString())
          : null
      ..totalAmount = map['totalAmount'] != null
          ? double.parse(map['totalAmount'].toString())
          : null
      ..pricePerLiter =
          map['pricePerLiter'] != null || map['totalRevenue'] != null
              ? double.parse(
                  (map['pricePerLiter'] ?? map['totalRevenue']).toString())
              : null
      ..notes = map['notes'] as String?
      ..recordedAt = map['recordedAt'] != null
          ? DateTime.parse(map['recordedAt'].toString())
          : DateTime.now()
      ..createdAt = map['createdAt'] != null
          ? DateTime.parse(map['createdAt'].toString())
          : DateTime.now()
      ..updatedAt = map['updatedAt'] != null
          ? DateTime.parse(map['updatedAt'].toString())
          : DateTime.now();
  }

  factory MilkRecordIsar.fromJson(Map<String, dynamic> json) =>
      MilkRecordIsar.fromMap(json);

  MilkRecordIsar copyWith({
    String? businessId,
    String? cattleBusinessId,
    String? cattleTagId,
    DateTime? date,
    double? morningAmount,
    double? afternoonAmount,
    double? eveningAmount,
    double? fatContent,
    double? totalAmount,
    double? pricePerLiter,
    String? notes,
  }) {
    return MilkRecordIsar()
      ..id = id
      ..businessId = businessId ?? this.businessId
      ..cattleBusinessId = cattleBusinessId ?? this.cattleBusinessId
      ..cattleTagId = cattleTagId ?? this.cattleTagId
      ..date = date ?? this.date
      ..morningAmount = morningAmount ?? this.morningAmount
      ..afternoonAmount = afternoonAmount ?? this.afternoonAmount
      ..eveningAmount = eveningAmount ?? this.eveningAmount
      ..fatContent = fatContent ?? this.fatContent
      ..totalAmount = totalAmount ?? this.totalAmount
      ..pricePerLiter = pricePerLiter ?? this.pricePerLiter
      ..notes = notes ?? this.notes
      ..recordedAt = recordedAt
      ..createdAt = createdAt
      ..updatedAt = DateTime.now();
  }
}
