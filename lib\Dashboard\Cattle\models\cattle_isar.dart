import 'package:isar/isar.dart';
import 'package:uuid/uuid.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../constants/app_colors.dart';

part 'cattle_isar.g.dart';

/// Enum for cattle gender
enum CattleGender {
  male,
  female,
  unknown;

  static CattleGender fromString(String? value) {
    if (value == null) return CattleGender.unknown;
    switch (value.toLowerCase()) {
      case 'male':
      case 'm':
        return CattleGender.male;
      case 'female':
      case 'f':
        return CattleGender.female;
      default:
        return CattleGender.unknown;
    }
  }

  String get displayName {
    switch (this) {
      case CattleGender.male:
        return 'Male';
      case CattleGender.female:
        return 'Female';
      case CattleGender.unknown:
        return 'Unknown';
    }
  }


}

/// Enum for cattle source
enum CattleSource {
  bornOnFarm,
  purchased,
  gift,
  transfer,
  unknown;

  static CattleSource fromString(String? value) {
    if (value == null) return CattleSource.unknown;
    switch (value.toLowerCase().replaceAll(' ', '')) {
      case 'bornonfarm':
      case 'born':
        return CattleSource.bornOnFarm;
      case 'purchased':
      case 'bought':
        return CattleSource.purchased;
      case 'gift':
        return CattleSource.gift;
      case 'transfer':
        return CattleSource.transfer;
      default:
        return CattleSource.unknown;
    }
  }

  String get displayName {
    switch (this) {
      case CattleSource.bornOnFarm:
        return 'Born on Farm';
      case CattleSource.purchased:
        return 'Purchased';
      case CattleSource.gift:
        return 'Gift';
      case CattleSource.transfer:
        return 'Transfer';
      case CattleSource.unknown:
        return 'Unknown';
    }
  }


}

/// Enum for cattle category
enum CattleCategory {
  heifer,
  cow,
  bull,
  steer,
  calf,
  unknown;

  static CattleCategory fromString(String? value) {
    if (value == null) return CattleCategory.unknown;
    switch (value.toLowerCase()) {
      case 'heifer':
        return CattleCategory.heifer;
      case 'cow':
        return CattleCategory.cow;
      case 'bull':
        return CattleCategory.bull;
      case 'steer':
        return CattleCategory.steer;
      case 'calf':
        return CattleCategory.calf;
      default:
        return CattleCategory.unknown;
    }
  }

  String get displayName {
    switch (this) {
      case CattleCategory.heifer:
        return 'Heifer';
      case CattleCategory.cow:
        return 'Cow';
      case CattleCategory.bull:
        return 'Bull';
      case CattleCategory.steer:
        return 'Steer';
      case CattleCategory.calf:
        return 'Calf';
      case CattleCategory.unknown:
        return 'Unknown';
    }
  }
}

/// Enum for cattle status
enum CattleStatus {
  active,
  sold,
  deceased,
  transferred,
  unknown;

  static CattleStatus fromString(String? value) {
    if (value == null) return CattleStatus.unknown;
    switch (value.toLowerCase()) {
      case 'active':
        return CattleStatus.active;
      case 'sold':
        return CattleStatus.sold;
      case 'deceased':
      case 'dead':
        return CattleStatus.deceased;
      case 'transferred':
      case 'transfer':
        return CattleStatus.transferred;
      default:
        return CattleStatus.unknown;
    }
  }

  String get displayName {
    switch (this) {
      case CattleStatus.active:
        return 'Active';
      case CattleStatus.sold:
        return 'Sold';
      case CattleStatus.deceased:
        return 'Deceased';
      case CattleStatus.transferred:
        return 'Transferred';
      case CattleStatus.unknown:
        return 'Unknown';
    }
  }


}

/// Enum for cattle stage
enum CattleStage {
  calf,
  weaned,
  yearling,
  breeding,
  lactating,
  dry,
  finished,
  unknown;

  static CattleStage fromString(String? value) {
    if (value == null) return CattleStage.unknown;
    switch (value.toLowerCase()) {
      case 'calf':
        return CattleStage.calf;
      case 'weaned':
        return CattleStage.weaned;
      case 'yearling':
        return CattleStage.yearling;
      case 'breeding':
        return CattleStage.breeding;
      case 'lactating':
        return CattleStage.lactating;
      case 'dry':
        return CattleStage.dry;
      case 'finished':
        return CattleStage.finished;
      default:
        return CattleStage.unknown;
    }
  }

  String get displayName {
    switch (this) {
      case CattleStage.calf:
        return 'Calf';
      case CattleStage.weaned:
        return 'Weaned';
      case CattleStage.yearling:
        return 'Yearling';
      case CattleStage.breeding:
        return 'Breeding';
      case CattleStage.lactating:
        return 'Lactating';
      case CattleStage.dry:
        return 'Dry';
      case CattleStage.finished:
        return 'Finished';
      case CattleStage.unknown:
        return 'Unknown';
    }
  }
}

/// Enum for breeding status
enum BreedingStatusType {
  open,
  bred,
  pregnant,
  fresh,
  dry,
  unknown;

  static BreedingStatusType fromString(String? value) {
    if (value == null) return BreedingStatusType.unknown;
    switch (value.toLowerCase()) {
      case 'open':
        return BreedingStatusType.open;
      case 'bred':
        return BreedingStatusType.bred;
      case 'pregnant':
        return BreedingStatusType.pregnant;
      case 'fresh':
        return BreedingStatusType.fresh;
      case 'dry':
        return BreedingStatusType.dry;
      default:
        return BreedingStatusType.unknown;
    }
  }

  String get displayName {
    switch (this) {
      case BreedingStatusType.open:
        return 'Open';
      case BreedingStatusType.bred:
        return 'Bred';
      case BreedingStatusType.pregnant:
        return 'Pregnant';
      case BreedingStatusType.fresh:
        return 'Fresh';
      case BreedingStatusType.dry:
        return 'Dry';
      case BreedingStatusType.unknown:
        return 'Unknown';
    }
  }
}

/// Enum for health status
enum HealthStatusType {
  healthy,
  sick,
  recovering,
  quarantine,
  unknown;

  static HealthStatusType fromString(String? value) {
    if (value == null) return HealthStatusType.unknown;
    switch (value.toLowerCase()) {
      case 'healthy':
        return HealthStatusType.healthy;
      case 'sick':
        return HealthStatusType.sick;
      case 'recovering':
        return HealthStatusType.recovering;
      case 'quarantine':
        return HealthStatusType.quarantine;
      default:
        return HealthStatusType.unknown;
    }
  }

  String get displayName {
    switch (this) {
      case HealthStatusType.healthy:
        return 'Healthy';
      case HealthStatusType.sick:
        return 'Sick';
      case HealthStatusType.recovering:
        return 'Recovering';
      case HealthStatusType.quarantine:
        return 'Quarantine';
      case HealthStatusType.unknown:
        return 'Unknown';
    }
  }
}

/// Extension for HealthStatusType UI presentation
extension HealthStatusUIMapper on HealthStatusType {
  /// Get the display color for this health status
  Color get displayColor {
    switch (this) {
      case HealthStatusType.healthy:
        return AppColors.success;
      case HealthStatusType.sick:
        return AppColors.error;
      case HealthStatusType.recovering:
        return AppColors.cattleKpiColors[3]; // Cyan
      case HealthStatusType.quarantine:
        return AppColors.warning;
      case HealthStatusType.unknown:
        return AppColors.success.withValues(alpha: 0.4);
    }
  }

  /// Get the display icon for this health status
  IconData get displayIcon {
    switch (this) {
      case HealthStatusType.healthy:
        return Icons.check_circle_outline;
      case HealthStatusType.sick:
        return Icons.sick;
      case HealthStatusType.recovering:
        return Icons.healing;
      case HealthStatusType.quarantine:
        return Icons.warning;
      case HealthStatusType.unknown:
        return Icons.help_outline;
    }
  }
}

/// Enum for birth health status
enum BirthHealthStatus {
  healthy,
  weak,
  stillborn,
  unknown;

  static BirthHealthStatus fromString(String? value) {
    if (value == null) return BirthHealthStatus.unknown;
    switch (value.toLowerCase()) {
      case 'healthy':
        return BirthHealthStatus.healthy;
      case 'weak':
        return BirthHealthStatus.weak;
      case 'stillborn':
        return BirthHealthStatus.stillborn;
      default:
        return BirthHealthStatus.unknown;
    }
  }

  String get displayName {
    switch (this) {
      case BirthHealthStatus.healthy:
        return 'Healthy';
      case BirthHealthStatus.weak:
        return 'Weak';
      case BirthHealthStatus.stillborn:
        return 'Stillborn';
      case BirthHealthStatus.unknown:
        return 'Unknown';
    }
  }
}

/// Enum for birth type
enum BirthType {
  single,
  twin,
  triplet,
  unknown;

  static BirthType fromString(String? value) {
    if (value == null) return BirthType.unknown;
    switch (value.toLowerCase()) {
      case 'single':
        return BirthType.single;
      case 'twin':
        return BirthType.twin;
      case 'triplet':
        return BirthType.triplet;
      default:
        return BirthType.unknown;
    }
  }

  String get displayName {
    switch (this) {
      case BirthType.single:
        return 'Single';
      case BirthType.twin:
        return 'Twin';
      case BirthType.triplet:
        return 'Triplet';
      case BirthType.unknown:
        return 'Unknown';
    }
  }
}

/// Enum for breeding method
enum BreedingMethod {
  natural,
  artificialInsemination,
  embryoTransfer,
  unknown;

  static BreedingMethod fromString(String? value) {
    if (value == null) return BreedingMethod.unknown;
    switch (value.toLowerCase().replaceAll(' ', '')) {
      case 'natural':
        return BreedingMethod.natural;
      case 'artificialinsemination':
      case 'ai':
        return BreedingMethod.artificialInsemination;
      case 'embryotransfer':
      case 'et':
        return BreedingMethod.embryoTransfer;
      default:
        return BreedingMethod.unknown;
    }
  }

  String get displayName {
    switch (this) {
      case BreedingMethod.natural:
        return 'Natural';
      case BreedingMethod.artificialInsemination:
        return 'Artificial Insemination';
      case BreedingMethod.embryoTransfer:
        return 'Embryo Transfer';
      case BreedingMethod.unknown:
        return 'Unknown';
    }
  }
}

/// Breeding status for cattle
@embedded
@JsonSerializable()
class BreedingStatus {
  /// Default constructor
  BreedingStatus();
  
  /// The current breeding status
  @enumerated
  BreedingStatusType status = BreedingStatusType.unknown;

  /// Date of the last heat/estrus
  DateTime? lastHeatDate;

  /// Is the animal currently pregnant
  bool isPregnant = false;

  /// Date of the most recent breeding
  DateTime? breedingDate;

  /// Expected date of calving based on breeding date
  DateTime? expectedCalvingDate;

  /// Next expected heat date if not pregnant
  DateTime? nextHeatDate;

  /// Last method used for breeding (Natural, AI, etc.)
  @enumerated
  BreedingMethod lastBreedingMethod = BreedingMethod.unknown;

  /// Date of the last calving
  DateTime? lastCalvingDate;

  factory BreedingStatus.fromJson(Map<String, dynamic> json) =>
      _$BreedingStatusFromJson(json);
  Map<String, dynamic> toJson() => _$BreedingStatusToJson(this);
}

/// Details about the birth of a calf
@embedded
@JsonSerializable()
class BirthDetails {
  /// Default constructor
  BirthDetails();
  
  /// The birth weight in kg
  double? birthWeight;

  /// Health status at birth (Healthy, Weak, Stillborn)
  @enumerated
  BirthHealthStatus healthStatus = BirthHealthStatus.unknown;

  /// Birth type (Single, Twin, etc.)
  @enumerated
  BirthType birthType = BirthType.unknown;

  /// Any complications during birth
  String? complications;

  /// Assistance required during birth
  bool assistanceRequired = false;

  factory BirthDetails.fromJson(Map<String, dynamic> json) =>
      _$BirthDetailsFromJson(json);
  Map<String, dynamic> toJson() => _$BirthDetailsToJson(this);
}

/// Health status information
@embedded
@JsonSerializable()
class HealthInfo {
  /// Default constructor
  HealthInfo();
  
  /// Current health status
  @enumerated
  HealthStatusType status = HealthStatusType.unknown;

  /// Current medication
  String? currentMedication;

  /// Date of last health check
  DateTime? lastCheckupDate;

  /// Date of last vaccination
  DateTime? lastVaccinationDate;

  /// Any chronic conditions
  String? chronicConditions;

  factory HealthInfo.fromJson(Map<String, dynamic> json) =>
      _$HealthInfoFromJson(json);
  Map<String, dynamic> toJson() => _$HealthInfoToJson(this);
}

/// Production information for dairy animals
@embedded
@JsonSerializable()
class ProductionInfo {
  /// Default constructor
  ProductionInfo();
  
  /// Current lactation number
  int? lactationNumber;

  /// Average daily milk production in liters
  double? avgDailyProduction;

  /// Peak milk production in liters
  double? peakProduction;

  /// Date of peak production
  DateTime? peakProductionDate;

  /// Days in milk for current lactation
  int? daysInMilk;

  /// Expected dry off date
  DateTime? expectedDryOffDate;

  factory ProductionInfo.fromJson(Map<String, dynamic> json) =>
      _$ProductionInfoFromJson(json);
  Map<String, dynamic> toJson() => _$ProductionInfoToJson(this);
}

/// Main Cattle model for Isar
@collection
@JsonSerializable()
class CattleIsar {

  /// Isar database ID
  Id id = Isar.autoIncrement;

  /// Business ID (UUID) - indexed for quick lookup
  @Index(unique: true)
  String? businessId;

  /// Tag ID / Ear tag number - indexed for quick lookup
  @Index(unique: true, caseSensitive: false)
  String? tagId;

  /// Name of the animal - indexed for search
  @Index(caseSensitive: false)
  String? name;

  /// Reference to the animal type business ID - indexed for filtering
  @Index()
  String? animalTypeId;

  /// Reference to the breed business ID - indexed for filtering
  @Index()
  String? breedId;

  /// Gender of the animal - indexed for filtering
  @Index()
  @enumerated
  CattleGender gender = CattleGender.unknown;

  /// Source of the animal (Born on farm, Purchased, etc.)
  @enumerated
  CattleSource source = CattleSource.unknown;

  /// Tag ID of the mother if known
  String? motherTagId;

  /// Business ID of the mother if known
  @Index()
  String? motherBusinessId;

  /// Date of birth
  DateTime? dateOfBirth;

  /// Date of purchase if purchased
  DateTime? purchaseDate;

  /// Purchase price
  double? purchasePrice;

  /// Current weight in kg
  double? weight;

  /// Color of the animal
  String? color;

  /// General notes
  String? notes;

  /// Path to photo of the animal
  String? photoPath;

  /// Date when record was created
  DateTime? createdAt;

  /// Date when record was last updated
  DateTime? updatedAt;

  /// Category of the animal (Heifer, Cow, Bull, Steer, etc.)
  @Index()
  @enumerated
  CattleCategory category = CattleCategory.unknown;

  /// Status of the animal (Active, Sold, Deceased, etc.)
  @Index()
  @enumerated
  CattleStatus status = CattleStatus.active;

  /// Production/growth stage of the animal
  @Index()
  @enumerated
  CattleStage stage = CattleStage.unknown;

  /// Breeding status information
  BreedingStatus? breedingStatus;

  /// Birth details if recorded
  BirthDetails? birthDetails;

  /// Health information
  HealthInfo? healthInfo;

  /// Production information for dairy animals
  ProductionInfo? productionInfo;

  /// Default constructor
  CattleIsar();

  /// Factory constructor for JSON deserialization
  factory CattleIsar.fromJson(Map<String, dynamic> json) =>
      _$CattleIsarFromJson(json);

  /// Method for JSON serialization
  Map<String, dynamic> toJson() => _$CattleIsarToJson(this);



  /// Generate a deterministic ID based on the tag ID to ensure consistency across reinstalls
  static String generateBusinessId(String tagId) {
    // Clean the tag ID (remove spaces and special characters)
    final cleanTagId = tagId.trim().replaceAll(RegExp(r'[^a-zA-Z0-9]'), '');
    
    // If the tag ID is empty after cleaning, fall back to a random UUID
    if (cleanTagId.isEmpty) {
      return const Uuid().v4();
    }
    
    // Create a static prefix to distinguish cattle IDs
    const prefix = "cattle_";
    
    // Return a deterministic ID based on the tag ID
    return '$prefix$cleanTagId';
  }

  /// Factory constructor for creating a new cattle record
  factory CattleIsar.create({
    required String tagId,
    required String name,
    required String animalTypeId,
    required String breedId,
    required CattleGender gender,
    required CattleSource source,
    String? motherTagId,
    String? motherBusinessId,
    DateTime? dateOfBirth,
    DateTime? purchaseDate,
    double? purchasePrice,
    double? weight,
    String? color,
    String? notes,
    String? photoPath,
    CattleCategory? category,
    CattleStatus? status,
    CattleStage? stage,
    BreedingStatus? breedingStatus,
    BirthDetails? birthDetails,
    HealthInfo? healthInfo,
    ProductionInfo? productionInfo,
  }) {
    final cattle = CattleIsar()
      ..businessId = generateBusinessId(tagId)
      ..tagId = tagId
      ..name = name
      ..animalTypeId = animalTypeId
      ..breedId = breedId
      ..gender = gender
      ..source = source
      ..motherTagId = motherTagId
      ..motherBusinessId = motherBusinessId
      ..dateOfBirth = dateOfBirth
      ..purchaseDate = purchaseDate
      ..purchasePrice = purchasePrice
      ..weight = weight
      ..color = color
      ..notes = notes
      ..photoPath = photoPath
      ..category = category ?? CattleCategory.unknown
      ..status = status ?? CattleStatus.active
      ..stage = stage ?? CattleStage.unknown
      ..breedingStatus = breedingStatus ?? BreedingStatus()
      ..birthDetails = birthDetails ?? BirthDetails()
      ..healthInfo = healthInfo ?? HealthInfo()
      ..productionInfo = productionInfo ?? ProductionInfo()
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();

    return cattle;
  }







  // Getters for icon conversion
  @ignore
  @JsonKey(includeFromJson: false, includeToJson: false)
  IconData get icon => IconData(iconCodePoint ?? 0, fontFamily: iconFontFamily);

  set icon(IconData value) {
    iconCodePoint = value.codePoint;
    iconFontFamily = value.fontFamily ?? 'MaterialIcons';
  }
  
  /// Icon code point for serialization 
  int? iconCodePoint;
  
  /// Icon font family for serialization
  String? iconFontFamily;
}

/// Extension methods for CattleIsar to provide clean formatting utilities
extension CattleFormatting on CattleIsar {
  /// Format birth date for display
  String get formattedBirthDate {
    if (dateOfBirth == null) return 'N/A';
    return DateFormat('MMM dd, yyyy').format(dateOfBirth!);
  }

  /// Format purchase date for display
  String get formattedPurchaseDate {
    if (purchaseDate == null) return 'N/A';
    return DateFormat('MMM dd, yyyy').format(purchaseDate!);
  }

  /// Calculate and format days owned for display
  String get formattedDaysOwned {
    DateTime? startDate = purchaseDate ?? dateOfBirth;
    if (startDate == null) return 'Unknown';

    final days = DateTime.now().difference(startDate).inDays;
    final years = days ~/ 365;

    if (years >= 1) {
      return '$years years ($days days)';
    } else {
      return '$days days';
    }
  }
}

/// Extension for cattle display and insights
extension CattleDisplayExtensions on CattleIsar {
  /// Format tag ID for display
  String get formattedTagId => tagId ?? 'N/A';

  /// Get gender insight
  String get genderInsight {
    final gender = this.gender.name.toLowerCase();
    switch (gender) {
      case 'male': return 'Bull/Steer';
      case 'female': return 'Cow/Heifer';
      default: return 'Not specified';
    }
  }

  /// Get age insight based on current age
  String get ageInsight {
    if (dateOfBirth == null) return 'Unknown birth date';
    final age = DateTime.now().difference(dateOfBirth!);
    final years = age.inDays ~/ 365;

    if (years < 1) return 'Young calf';
    if (years < 2) return 'Growing stage';
    if (years < 5) return 'Prime age';
    return 'Mature cattle';
  }

  /// Get status insight
  String get statusInsight {
    final status = this.status.name.toLowerCase();
    switch (status) {
      case 'active': return 'Good condition';
      case 'sold': return 'No longer on farm';
      case 'deceased': return 'Passed away';
      case 'transferred': return 'Moved to another location';
      default: return 'Status unknown';
    }
  }

  /// Get weight insight
  String get weightInsight {
    if (weight == null) return 'No weight recorded';
    final weightValue = weight!;
    if (weightValue < 100) return 'Light weight';
    if (weightValue < 300) return 'Average weight';
    if (weightValue < 500) return 'Heavy weight';
    return 'Very heavy';
  }

  /// Get source insight
  String get sourceInsight {
    final source = this.source.name.toLowerCase();
    switch (source) {
      case 'purchased': return 'Bought externally';
      case 'bornonfarm': return 'Born on farm';
      case 'gift': return 'Received as gift';
      case 'transfer': return 'Transferred from elsewhere';
      default: return 'Origin unknown';
    }
  }

  /// Get birth date insight
  String get birthDateInsight {
    if (dateOfBirth == null) return 'No birth date';
    final age = DateTime.now().difference(dateOfBirth!);
    return '${age.inDays} days old';
  }

  /// Get purchase price insight
  String get purchasePriceInsight {
    if (purchasePrice == null) return 'No price data';
    final price = purchasePrice!;
    if (price < 500) return 'Low cost';
    if (price < 1500) return 'Average cost';
    if (price < 3000) return 'High cost';
    return 'Premium cost';
  }

  /// Get purchase date insight
  String get purchaseDateInsight {
    if (purchaseDate == null) return 'No purchase date';
    final daysSince = DateTime.now().difference(purchaseDate!).inDays;
    return '$daysSince days ago';
  }

  /// Get ownership insight
  String get ownershipInsight {
    DateTime? startDate = purchaseDate ?? dateOfBirth;
    if (startDate == null) return 'Unknown period';
    final days = DateTime.now().difference(startDate).inDays;
    final years = days ~/ 365;
    if (years > 0) return '$years year${years > 1 ? 's' : ''}';
    final months = days ~/ 30;
    if (months > 0) return '$months month${months > 1 ? 's' : ''}';
    return '$days day${days > 1 ? 's' : ''}';
  }

  /// Format weight for display
  String get formattedWeight => weight?.toString() ?? 'Not recorded';

  /// Format color for display
  String get formattedColor => color ?? 'Not specified';

  /// Calculate simple days owned as string
  String get simpleDaysOwned {
    DateTime? startDate = purchaseDate ?? dateOfBirth;
    if (startDate == null) return 'Unknown';
    return DateTime.now().difference(startDate).inDays.toString();
  }

  /// Format purchase price as currency
  String get formattedPurchasePrice {
    if (purchasePrice == null) return 'N/A';
    return NumberFormat.currency(locale: 'en_US', symbol: '\$').format(purchasePrice);
  }
}