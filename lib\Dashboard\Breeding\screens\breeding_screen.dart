import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../controllers/breeding_controller.dart';
import '../tabs/breeding_records_tab.dart';
import '../tabs/pregnancy_records_tab.dart';
import '../tabs/delivery_records_tab.dart';
import '../tabs/breeding_analytics_tab.dart';
import '../tabs/breeding_insights_tab.dart';
import '../dialogs/breeding_record_form_dialog.dart';
import '../../../routes/app_routes.dart';
import '../../widgets/index.dart';
import '../../widgets/mixins/screen_state_mapper.dart'; // Import Screen State Mapper
import '../../../constants/app_layout.dart'; // Import Universal Layout
import '../../../constants/app_tabs.dart'; // Import Universal Tabs
import '../../../constants/app_colors.dart'; // Import Universal Colors

/// Breeding screen with Provider-managed controller lifecycle
/// Following the CattleScreen pattern: StatelessWidget with ChangeNotifierProvider
class BreedingScreen extends StatelessWidget {
  const BreedingScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => BreedingController(),
      child: const _BreedingScreenContent(),
    );
  }
}

/// Internal content widget that accesses the controller via Provider
class _BreedingScreenContent extends StatefulWidget {
  const _BreedingScreenContent();

  @override
  State<_BreedingScreenContent> createState() => _BreedingScreenContentState();
}

class _BreedingScreenContentState extends State<_BreedingScreenContent>
    with TickerProviderStateMixin, UniversalScreenState, ScreenStateMapper {
  late TabController _tabController;
  UniversalTabManager? _tabManager;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _showAddBreedingDialog() {
    final breedingController = context.read<BreedingController>();

    showDialog(
      context: context,
      builder: (context) => BreedingRecordFormDialog(
        cattle: breedingController.cattle,
        // No onRecordAdded callback needed - reactive streams handle updates!
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return UniversalLayout.tabScreen(
      title: 'Breeding Management',
      body: Consumer<BreedingController>(
        builder: (context, breedingController, child) {
          // Initialize tab manager here where Provider context is available
          _tabManager ??= UniversalTabManager.fiveTabs(
            controller: _tabController,
            tabViews: [
              // Use Builder widgets for lazy initialization
              Builder(
                builder: (context) => BreedingAnalyticsTab(controller: breedingController),
              ),
              Builder(
                builder: (context) => BreedingRecordsTab(controller: breedingController),
              ),
              Builder(
                builder: (context) => PregnancyRecordsTab(controller: breedingController),
              ),
              Builder(
                builder: (context) => DeliveryRecordsTab(controller: breedingController),
              ),
              Builder(
                builder: (context) => BreedingInsightsTab(controller: breedingController),
              ),
            ],
            labels: const ['Analytics', 'Breeding', 'Pregnancy', 'Delivery', 'Insights'],
            icons: const [Icons.analytics, Icons.favorite, Icons.pregnant_woman, Icons.child_care, Icons.lightbulb],
            colors: [
              Colors.blue, // Tab 0: Analytics (Blue)
              const Color(0xFF388E3C), // Tab 1: Breeding (Green)
              Colors.purple, // Tab 2: Pregnancy (Purple)
              Colors.indigo, // Tab 3: Delivery (Indigo)
              Colors.pink, // Tab 4: Insights (Pink)
            ],
            showFABs: const [false, true, true, true, false], // FAB on Records, Pregnancy, Delivery tabs
            indicatorColor: UniversalEmptyStateTheme.breeding,
          );

          return UniversalStateBuilder(
            state: getScreenStateFromController(breedingController),
            errorMessage: breedingController.errorMessage,
            onRetry: () {}, // No manual retry needed - reactive streams auto-recover
            moduleColor: UniversalEmptyStateTheme.breeding,
            loadingWidget: UniversalLoadingIndicator.breeding(),
            errorWidget: UniversalErrorIndicator.breeding(
              message: breedingController.errorMessage ?? 'Failed to load breeding data',
              onRetry: () {}, // No manual retry needed - reactive streams auto-recover
            ),
            child: _tabManager!, // Tab manager is guaranteed to be initialized above
          );
        },
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.bar_chart),
          onPressed: () => Navigator.pushNamed(
            context,
            AppRoutes.breedingReport,
          ),
          tooltip: 'View Breeding Reports',
        ),
        // Removed manual refresh - reactive streams handle all updates automatically
      ],
      floatingActionButton: AnimatedBuilder(
        animation: _tabController,
        builder: (context, child) {
          // Only rebuild FAB when tab changes, not the entire screen
          return _tabManager?.getCurrentFAB(
            onPressed: _showAddBreedingDialog,
            tooltip: 'Add Record',
            backgroundColor: UniversalEmptyStateTheme.breeding,
          ) ?? const SizedBox.shrink(); // Handle null case
        },
      ), // Optimized FAB management with AnimatedBuilder
      // No onRefresh needed - reactive streams handle all data updates automatically
    );
  }

  // State mapping is now handled by ScreenStateMapper mixin
}
