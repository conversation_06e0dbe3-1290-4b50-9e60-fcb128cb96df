import 'package:intl/intl.dart';

/// Utility class for date formatting and manipulation
class AppDateUtils {
  static final DateFormat _dateFormat = DateFormat('MMM dd, yyyy');
  static final DateFormat _dateTimeFormat = DateFormat('MMM dd, yyyy HH:mm');
  static final DateFormat _shortDateFormat = DateFormat('MM/dd/yyyy');
  static final DateFormat _timeFormat = DateFormat('HH:mm');

  /// Format a date to a readable string (e.g., "Jan 15, 2024")
  static String formatDate(DateTime? date) {
    if (date == null) return 'Unknown';
    return _dateFormat.format(date);
  }

  /// Format a date and time to a readable string (e.g., "Jan 15, 2024 14:30")
  static String formatDateTime(DateTime? dateTime) {
    if (dateTime == null) return 'Unknown';
    return _dateTimeFormat.format(dateTime);
  }

  /// Format a date to a short string (e.g., "01/15/2024")
  static String formatShortDate(DateTime? date) {
    if (date == null) return 'Unknown';
    return _shortDateFormat.format(date);
  }

  /// Format time only (e.g., "14:30")
  static String formatTime(DateTime? time) {
    if (time == null) return 'Unknown';
    return _timeFormat.format(time);
  }

  /// Get the number of days between two dates
  static int daysBetween(DateTime from, DateTime to) {
    from = DateTime(from.year, from.month, from.day);
    to = DateTime(to.year, to.month, to.day);
    return (to.difference(from).inHours / 24).round();
  }

  /// Check if a date is today
  static bool isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year && date.month == now.month && date.day == now.day;
  }

  /// Check if a date is yesterday
  static bool isYesterday(DateTime date) {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return date.year == yesterday.year && 
           date.month == yesterday.month && 
           date.day == yesterday.day;
  }

  /// Get a relative date string (e.g., "Today", "Yesterday", "2 days ago")
  static String getRelativeDateString(DateTime date) {
    if (isToday(date)) return 'Today';
    if (isYesterday(date)) return 'Yesterday';
    
    final daysDiff = daysBetween(date, DateTime.now());
    if (daysDiff < 7) {
      return '$daysDiff days ago';
    } else if (daysDiff < 30) {
      final weeks = (daysDiff / 7).round();
      return weeks == 1 ? '1 week ago' : '$weeks weeks ago';
    } else if (daysDiff < 365) {
      final months = (daysDiff / 30).round();
      return months == 1 ? '1 month ago' : '$months months ago';
    } else {
      final years = (daysDiff / 365).round();
      return years == 1 ? '1 year ago' : '$years years ago';
    }
  }

  /// Get the start of the day (00:00:00)
  static DateTime startOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day);
  }

  /// Get the end of the day (23:59:59)
  static DateTime endOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day, 23, 59, 59);
  }

  /// Get the start of the week (Monday)
  static DateTime startOfWeek(DateTime date) {
    final daysFromMonday = date.weekday - 1;
    return startOfDay(date.subtract(Duration(days: daysFromMonday)));
  }

  /// Get the end of the week (Sunday)
  static DateTime endOfWeek(DateTime date) {
    final daysToSunday = 7 - date.weekday;
    return endOfDay(date.add(Duration(days: daysToSunday)));
  }

  /// Get the start of the month
  static DateTime startOfMonth(DateTime date) {
    return DateTime(date.year, date.month, 1);
  }

  /// Get the end of the month
  static DateTime endOfMonth(DateTime date) {
    return DateTime(date.year, date.month + 1, 0, 23, 59, 59);
  }

  /// Get the start of the year
  static DateTime startOfYear(DateTime date) {
    return DateTime(date.year, 1, 1);
  }

  /// Get the end of the year
  static DateTime endOfYear(DateTime date) {
    return DateTime(date.year, 12, 31, 23, 59, 59);
  }

  /// Parse a date string in various formats
  static DateTime? parseDate(String dateString) {
    try {
      // Try different formats
      final formats = [
        DateFormat('yyyy-MM-dd'),
        DateFormat('MM/dd/yyyy'),
        DateFormat('dd/MM/yyyy'),
        DateFormat('MMM dd, yyyy'),
        DateFormat('yyyy-MM-dd HH:mm:ss'),
      ];

      for (final format in formats) {
        try {
          return format.parse(dateString);
        } catch (e) {
          // Continue to next format
        }
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Get age in years from a birth date
  static int getAgeInYears(DateTime birthDate) {
    final now = DateTime.now();
    int age = now.year - birthDate.year;
    if (now.month < birthDate.month || 
        (now.month == birthDate.month && now.day < birthDate.day)) {
      age--;
    }
    return age;
  }

  /// Get age string with years and months
  static String getAgeString(DateTime birthDate) {
    final now = DateTime.now();
    int years = now.year - birthDate.year;
    int months = now.month - birthDate.month;
    
    if (months < 0) {
      years--;
      months += 12;
    }
    
    if (years == 0) {
      return months == 1 ? '1 month' : '$months months';
    } else if (months == 0) {
      return years == 1 ? '1 year' : '$years years';
    } else {
      return '$years years, $months months';
    }
  }

  /// Check if a date is in the future
  static bool isFuture(DateTime date) {
    return date.isAfter(DateTime.now());
  }

  /// Check if a date is in the past
  static bool isPast(DateTime date) {
    return date.isBefore(DateTime.now());
  }

  /// Get the number of days until a future date
  static int daysUntil(DateTime futureDate) {
    if (!isFuture(futureDate)) return 0;
    return daysBetween(DateTime.now(), futureDate);
  }

  /// Get the number of days since a past date
  static int daysSince(DateTime pastDate) {
    if (!isPast(pastDate)) return 0;
    return daysBetween(pastDate, DateTime.now());
  }
}
