import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../controllers/events_controller.dart';
import '../../widgets/universal_state_builder.dart';
import '../../widgets/insights/insight_card.dart';
import '../../widgets/insights/recommendation_card.dart';
import '../../widgets/insights/trend_card.dart';
import '../../widgets/mixins/screen_state_mapper.dart';

/// Events Insights Tab - Shows actionable insights and recommendations
/// Follows the universal insights tab pattern with trend analysis and recommendations
class EventsInsightsTab extends StatelessWidget {
  const EventsInsightsTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<EventsController>(
      builder: (context, controller, child) {
        return UniversalStateBuilder(
          state: controller.state,
          errorMessage: controller.errorMessage,
          onRetry: () {
            // Stream handles refresh automatically
          },
          child: _buildInsightsContent(context, controller),
        );
      },
    );
  }

  Widget _buildInsightsContent(BuildContext context, EventsController controller) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Performance Overview
          Text(
            'Performance Overview',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Key Performance Insights
          Row(
            children: [
              Expanded(
                child: InsightCard(
                  title: 'Completion Rate',
                  value: '${(controller.completionRate * 100).toStringAsFixed(1)}%',
                  trend: controller.completionRate > 0.8 ? 'positive' : 'negative',
                  insight: controller.completionRate > 0.8
                      ? 'Excellent event completion rate'
                      : 'Room for improvement in event completion',
                  color: controller.completionRate > 0.8 ? Colors.green : Colors.orange,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: InsightCard(
                  title: 'Efficiency Score',
                  value: '${(controller.eventEfficiencyScore * 100).toStringAsFixed(0)}%',
                  trend: controller.eventEfficiencyScore > 0.7 ? 'positive' : 'negative',
                  insight: controller.eventEfficiencyScore > 0.7
                      ? 'High event management efficiency'
                      : 'Consider optimizing event scheduling',
                  color: controller.eventEfficiencyScore > 0.7 ? Colors.green : Colors.orange,
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Trends Section
          Text(
            'Trends & Patterns',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          TrendCard(
            title: 'Event Volume Trend',
            subtitle: 'Events scheduled over time',
            value: controller.eventsThisMonth.toString(),
            previousValue: controller.eventsThisMonth - controller.eventsThisWeek,
            unit: 'events this month',
            icon: Icons.trending_up,
          ),
          const SizedBox(height: 16),

          TrendCard(
            title: 'Busiest Period',
            subtitle: 'Peak event scheduling time',
            value: controller.busiestMonth,
            unit: 'highest activity month',
            icon: Icons.calendar_month,
            color: Colors.purple,
          ),
          const SizedBox(height: 24),

          // Recommendations Section
          Text(
            'Recommendations',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Dynamic Recommendations
          ..._buildRecommendations(controller),
        ],
      ),
    );
  }

  List<Widget> _buildRecommendations(EventsController controller) {
    List<Widget> recommendations = [];

    // Overdue Events Recommendation
    if (controller.overdueEvents > 0) {
      recommendations.add(
        RecommendationCard(
          title: 'Address Overdue Events',
          description: 'You have ${controller.overdueEvents} overdue events that need attention.',
          priority: 'high',
          actionText: 'View Overdue',
          onAction: () {
            // Navigate to filtered view of overdue events
          },
          icon: Icons.warning,
        ),
      );
      recommendations.add(const SizedBox(height: 12));
    }

    // Upcoming Events Recommendation
    if (controller.eventsNextWeek > 5) {
      recommendations.add(
        RecommendationCard(
          title: 'Busy Week Ahead',
          description: 'You have ${controller.eventsNextWeek} events scheduled for next week. Consider preparing in advance.',
          priority: 'medium',
          actionText: 'View Schedule',
          onAction: () {
            // Navigate to calendar view
          },
          icon: Icons.schedule,
        ),
      );
      recommendations.add(const SizedBox(height: 12));
    }

    // Recurring Events Recommendation
    if (controller.recurringEvents > 0) {
      recommendations.add(
        RecommendationCard(
          title: 'Optimize Recurring Events',
          description: 'You have ${controller.recurringEvents} recurring events. Consider automating reminders.',
          priority: 'low',
          actionText: 'Set Reminders',
          onAction: () {
            // Navigate to notification settings
          },
          icon: Icons.repeat,
        ),
      );
      recommendations.add(const SizedBox(height: 12));
    }

    // Low Completion Rate Recommendation
    if (controller.completionRate < 0.7) {
      recommendations.add(
        RecommendationCard(
          title: 'Improve Event Completion',
          description: 'Your completion rate is ${(controller.completionRate * 100).toStringAsFixed(1)}%. Consider setting up reminders or adjusting schedules.',
          priority: 'medium',
          actionText: 'Setup Reminders',
          onAction: () {
            // Navigate to reminder settings
          },
          icon: Icons.notifications,
        ),
      );
      recommendations.add(const SizedBox(height: 12));
    }

    // Default recommendation if no specific issues
    if (recommendations.isEmpty) {
      recommendations.add(
        RecommendationCard(
          title: 'Great Event Management!',
          description: 'Your event management is performing well. Keep up the good work!',
          priority: 'low',
          actionText: 'View Analytics',
          onAction: () {
            // Switch to analytics tab
          },
          icon: Icons.check_circle,
        ),
      );
    }

    return recommendations;
  }
}
