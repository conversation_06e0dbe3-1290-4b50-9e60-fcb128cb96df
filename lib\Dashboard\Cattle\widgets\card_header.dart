import 'package:flutter/material.dart';

/// A reusable card header widget with consistent styling
class Card<PERSON>eader extends StatelessWidget {
  final String title;
  final Color color;
  final IconData icon;
  final Widget? trailing;

  const CardHeader({
    super.key,
    required this.title,
    required this.color,
    required this.icon,
    this.trailing,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withAlpha(26), // 0.1 * 255 = 26
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 20,
            backgroundColor: color.withAlpha(51), // 0.2 * 255 = 51
            child: Icon(
              icon,
              color: color,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              title,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color.withAlpha(204), // 0.8 * 255 = 204
              ),
            ),
          ),
          if (trailing != null) trailing!,
        ],
      ),
    );
  }
}
