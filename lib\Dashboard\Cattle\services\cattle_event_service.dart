import '../../Events/models/event_isar.dart';
import 'package:isar/isar.dart';

class CattleEventService {
  final Isar isar;

  CattleEventService(this.isar);

  // Get all events for a specific cattle
  Future<List<EventIsar>> getEventsForCattle(String cattleBusinessId) async {
    return await isar.eventIsars
        .filter()
        .cattleIdEqualTo(cattleBusinessId)
        .findAll();
  }

  // Add a new event
  Future<void> addEvent(EventIsar event) async {
    await isar.writeTxn(() async {
      await isar.eventIsars.put(event);
    });
  }

  // Update an existing event
  Future<void> updateEvent(EventIsar event) async {
    await isar.writeTxn(() async {
      await isar.eventIsars.put(event);
    });
  }

  // Delete an event
  Future<void> deleteEvent(EventIsar event) async {
    await isar.writeTxn(() async {
      await isar.eventIsars.delete(event.id);
    });
  }

  // Mark event as completed
  Future<void> markEventAsCompleted(EventIsar event) async {
    event.isCompleted = true;
    event.updatedAt = DateTime.now();
    await updateEvent(event);
  }

  // Mark event as missed
  Future<void> markEventAsMissed(EventIsar event) async {
    event.isMissed = true;
    event.updatedAt = DateTime.now();
    await updateEvent(event);
  }
} 