import 'package:isar/isar.dart';

part 'currency_settings_isar.g.dart';

@Collection()
class CurrencySettingsIsar {
  Id id = Isar.autoIncrement;

  @Index()
  String farmId = '';

  String? businessId;

  String currencyCode = 'USD';

  String currencySymbol = '\$';

  String currencyName = 'US Dollar';

  bool symbolBeforeAmount = true;

  DateTime? createdAt;

  DateTime? updatedAt;

  String farmBusinessId = '';
} 