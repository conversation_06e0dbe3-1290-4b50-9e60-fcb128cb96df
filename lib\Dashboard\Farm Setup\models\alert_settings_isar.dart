import 'package:isar/isar.dart';
import 'package:uuid/uuid.dart';
import 'dart:convert';

part 'alert_settings_isar.g.dart';

@embedded
class ConfigMap {
  String? jsonData;

  // Helper to get the Map representation
  @ignore
  Map<String, dynamic> get data => 
      jsonData != null ? json.decode(jsonData!) as Map<String, dynamic> : {};

  // Helper to set the Map
  set data(Map<String, dynamic> value) {
    jsonData = json.encode(value);
  }
}

@collection
class AlertChannelIsar {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  String? businessId;

  @Index(caseSensitive: false)
  String? name;

  @Index()
  String? farmBusinessId; // Reference to the farm this channel belongs to

  bool enabled = true;

  ConfigMap? config = ConfigMap();

  DateTime? createdAt;
  DateTime? updatedAt;

  AlertChannelIsar() {
    createdAt = DateTime.now();
    updatedAt = DateTime.now();
  }

  factory AlertChannelIsar.create({
    required String farmBusinessId,
    required String name,
    bool enabled = true,
    required Map<String, dynamic> config,
  }) {
    final channel = AlertChannelIsar()
      ..businessId = const Uuid().v4()
      ..farmBusinessId = farmBusinessId
      ..name = name
      ..enabled = enabled
      ..config = ConfigMap()
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();
    
    channel.config!.data = config;
    return channel;
  }

  Map<String, dynamic> toMap() {
    return {
      'id': businessId,
      'farmBusinessId': farmBusinessId,
      'name': name,
      'enabled': enabled,
      'config': config?.data ?? {},
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  factory AlertChannelIsar.fromMap(Map<String, dynamic> map) {
    final channel = AlertChannelIsar()
      ..businessId = map['id'] as String?
      ..farmBusinessId = map['farmBusinessId'] as String?
      ..name = map['name'] as String?
      ..enabled = map['enabled'] as bool? ?? true
      ..config = ConfigMap()
      ..createdAt = map['createdAt'] != null ? DateTime.parse(map['createdAt'] as String) : DateTime.now()
      ..updatedAt = map['updatedAt'] != null ? DateTime.parse(map['updatedAt'] as String) : DateTime.now();
    
    if (map['config'] != null) {
      channel.config!.data = Map<String, dynamic>.from(map['config'] as Map);
    }
    
    return channel;
  }

  AlertChannelIsar copyWith({
    String? businessId,
    String? farmBusinessId,
    String? name,
    bool? enabled,
    Map<String, dynamic>? config,
  }) {
    final channel = AlertChannelIsar()
      ..id = id
      ..businessId = businessId ?? this.businessId
      ..farmBusinessId = farmBusinessId ?? this.farmBusinessId
      ..name = name ?? this.name
      ..enabled = enabled ?? this.enabled
      ..config = this.config ?? ConfigMap()
      ..createdAt = createdAt
      ..updatedAt = DateTime.now();
    
    if (config != null) {
      channel.config!.data = config;
    }
    
    return channel;
  }

  static List<AlertChannelIsar> defaultChannels(String farmBusinessId) => [
    AlertChannelIsar.create(
      farmBusinessId: farmBusinessId,
      name: 'Push Notifications',
      config: {'token': ''},
    ),
    AlertChannelIsar.create(
      farmBusinessId: farmBusinessId,
      name: 'Email',
      config: {'email': ''},
    ),
    AlertChannelIsar.create(
      farmBusinessId: farmBusinessId,
      name: 'SMS',
      config: {'phone': ''},
    ),
  ];
}

@collection
class AlertTypeIsar {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  String? businessId;

  @Index(caseSensitive: false)
  String? name;

  String? description;

  @Index()
  String? farmBusinessId; // Reference to the farm this alert type belongs to

  bool enabled = true;

  // List of channel IDs (businessIds of AlertChannelIsar) as a comma-separated string
  String? enabledChannelsString;

  ConfigMap? config = ConfigMap();

  DateTime? createdAt;
  DateTime? updatedAt;

  AlertTypeIsar();

  // Helper method to get/set enabled channels list
  List<String> get enabledChannels => 
      enabledChannelsString?.split(',').where((c) => c.isNotEmpty).toList() ?? [];

  set enabledChannels(List<String> value) {
    enabledChannelsString = value.join(',');
  }

  factory AlertTypeIsar.create({
    required String farmBusinessId,
    required String name,
    required String description,
    bool enabled = true,
    List<String>? enabledChannels,
    Map<String, dynamic>? config,
  }) {
    final alertType = AlertTypeIsar()
      ..businessId = const Uuid().v4()
      ..farmBusinessId = farmBusinessId
      ..name = name
      ..description = description
      ..enabled = enabled
      ..config = ConfigMap()
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();
    
    if (enabledChannels != null) {
      alertType.enabledChannels = enabledChannels;
    } else {
      alertType.enabledChannels = ['push'];
    }
    
    if (config != null) {
      alertType.config!.data = config;
    }
    
    return alertType;
  }

  Map<String, dynamic> toMap() {
    return {
      'id': businessId,
      'farmBusinessId': farmBusinessId,
      'name': name,
      'description': description,
      'enabled': enabled,
      'enabledChannels': enabledChannels,
      'config': config?.data ?? {},
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  factory AlertTypeIsar.fromMap(Map<String, dynamic> map) {
    final alertType = AlertTypeIsar()
      ..businessId = map['id'] as String?
      ..farmBusinessId = map['farmBusinessId'] as String?
      ..name = map['name'] as String?
      ..description = map['description'] as String?
      ..enabled = map['enabled'] as bool? ?? true
      ..config = ConfigMap()
      ..createdAt = map['createdAt'] != null ? DateTime.parse(map['createdAt'] as String) : DateTime.now()
      ..updatedAt = map['updatedAt'] != null ? DateTime.parse(map['updatedAt'] as String) : DateTime.now();
    
    if (map['enabledChannels'] != null) {
      alertType.enabledChannels = List<String>.from(map['enabledChannels'] as List);
    } else {
      alertType.enabledChannels = ['push'];
    }
    
    if (map['config'] != null) {
      alertType.config!.data = Map<String, dynamic>.from(map['config'] as Map);
    }
    
    return alertType;
  }

  AlertTypeIsar copyWith({
    String? businessId,
    String? farmBusinessId,
    String? name,
    String? description,
    bool? enabled,
    List<String>? enabledChannels,
    Map<String, dynamic>? config,
  }) {
    final alertType = AlertTypeIsar()
      ..id = id
      ..businessId = businessId ?? this.businessId
      ..farmBusinessId = farmBusinessId ?? this.farmBusinessId
      ..name = name ?? this.name
      ..description = description ?? this.description
      ..enabled = enabled ?? this.enabled
      ..config = this.config ?? ConfigMap()
      ..createdAt = createdAt
      ..updatedAt = DateTime.now();
    
    if (enabledChannels != null) {
      alertType.enabledChannels = enabledChannels;
    } else {
      alertType.enabledChannelsString = enabledChannelsString;
    }
    
    if (config != null) {
      alertType.config!.data = config;
    }
    
    return alertType;
  }

  static List<AlertTypeIsar> defaultAlertTypes(String farmBusinessId) => [
    AlertTypeIsar.create(
      farmBusinessId: farmBusinessId,
      name: 'Health Check Reminders',
      description: 'Get notified when cattle health checks are due',
      config: {
        'days_before': 3,
        'repeat_interval': 24, // hours
      },
    ),
    AlertTypeIsar.create(
      farmBusinessId: farmBusinessId,
      name: 'Milk Collection Reminders',
      description: 'Get reminders for scheduled milk collection times',
      config: {
        'morning_time': '06:00',
        'evening_time': '18:00',
        'remind_before': 30, // minutes
      },
    ),
    AlertTypeIsar.create(
      farmBusinessId: farmBusinessId,
      name: 'Vaccination Due',
      description: 'Get notified when vaccinations are due',
      config: {
        'days_before': 7,
        'repeat_interval': 24, // hours
      },
    ),
    AlertTypeIsar.create(
      farmBusinessId: farmBusinessId,
      name: 'Breeding Events',
      description: 'Get notified about breeding-related events',
      config: {
        'heat_detection': true,
        'pregnancy_check': true,
        'calving_due': true,
        'days_before': 7,
      },
    ),
    AlertTypeIsar.create(
      farmBusinessId: farmBusinessId,
      name: 'Low Inventory Alerts',
      description: 'Get notified when feed or medicine inventory is low',
      config: {
        'threshold_percentage': 20,
      },
    ),
    AlertTypeIsar.create(
      farmBusinessId: farmBusinessId,
      name: 'Abnormal Milk Production',
      description: 'Get notified when milk production is significantly different from average',
      config: {
        'threshold_percentage': 15,
      },
    ),
  ];
}

@collection
class AlertSettingsIsar {
  Id id = Isar.autoIncrement;
  
  @Index(unique: true)
  String? businessId;
  
  @Index(caseSensitive: false)
  String? name;
  
  @Index()
  String? farmBusinessId; // Reference to the farm these settings belong to
  
  bool enabled = true;
  bool allAlertsEnabled = true;
  
  // Store selected categories and animals as comma-separated strings
  String? selectedCategoriesString;
  String? selectedAnimalsString;
  
  // Health alerts
  bool vaccinationAlerts = true;
  bool healthCheckAlerts = true;
  bool dewormingAlerts = true;
  
  // Reproduction alerts
  bool pregnancyCheckAlerts = true;
  bool calvingAlerts = true;
  bool heatDetectionAlerts = true;
  
  // Production alerts
  bool milkProductionAlerts = true;
  bool weightChangeAlerts = true;
  
  // Notification types
  bool pushNotifications = true;
  bool inAppNotifications = true;
  
  DateTime? createdAt;
  DateTime? updatedAt;
  
  // Default constructor
  AlertSettingsIsar();
  
  // Named constructor to create a new instance with defaults
  factory AlertSettingsIsar.create() {
    return AlertSettingsIsar()
      ..businessId = const Uuid().v4()
      ..name = const Uuid().v4()
      ..allAlertsEnabled = true
      ..vaccinationAlerts = true
      ..healthCheckAlerts = true
      ..dewormingAlerts = true
      ..pregnancyCheckAlerts = true
      ..calvingAlerts = true
      ..heatDetectionAlerts = true
      ..milkProductionAlerts = true
      ..weightChangeAlerts = true
      ..pushNotifications = true
      ..inAppNotifications = true
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();
  }
  
  // Convert to Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'businessId': businessId,
      'name': name,
      'enabled': enabled,
      'allAlertsEnabled': allAlertsEnabled,
      'vaccinationAlerts': vaccinationAlerts,
      'healthCheckAlerts': healthCheckAlerts,
      'dewormingAlerts': dewormingAlerts,
      'pregnancyCheckAlerts': pregnancyCheckAlerts,
      'calvingAlerts': calvingAlerts,
      'heatDetectionAlerts': heatDetectionAlerts,
      'milkProductionAlerts': milkProductionAlerts,
      'weightChangeAlerts': weightChangeAlerts,
      'pushNotifications': pushNotifications,
      'inAppNotifications': inAppNotifications,
      'selectedCategories': selectedCategories,
      'selectedAnimals': selectedAnimals,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }
  
  // Create from Map
  factory AlertSettingsIsar.fromMap(Map<String, dynamic> map) {
    return AlertSettingsIsar()
      ..id = map['id'] ?? Isar.autoIncrement
      ..businessId = map['businessId']
      ..name = map['name']
      ..enabled = map['enabled'] ?? true
      ..allAlertsEnabled = map['allAlertsEnabled'] ?? true
      ..vaccinationAlerts = map['vaccinationAlerts'] ?? true
      ..healthCheckAlerts = map['healthCheckAlerts'] ?? true
      ..dewormingAlerts = map['dewormingAlerts'] ?? true
      ..pregnancyCheckAlerts = map['pregnancyCheckAlerts'] ?? true
      ..calvingAlerts = map['calvingAlerts'] ?? true
      ..heatDetectionAlerts = map['heatDetectionAlerts'] ?? true
      ..milkProductionAlerts = map['milkProductionAlerts'] ?? true
      ..weightChangeAlerts = map['weightChangeAlerts'] ?? true
      ..pushNotifications = map['pushNotifications'] ?? true
      ..inAppNotifications = map['inAppNotifications'] ?? true
      ..selectedCategoriesString = map['selectedCategories']
      ..selectedAnimalsString = map['selectedAnimals']
      ..createdAt = map['createdAt'] != null
          ? DateTime.parse(map['createdAt'])
          : null
      ..updatedAt = map['updatedAt'] != null
          ? DateTime.parse(map['updatedAt'])
          : null;
  }

  // Helper methods for the categories config
  List<String> get selectedCategories => 
      selectedCategoriesString?.split(',').where((c) => c.isNotEmpty).toList() ?? [];

  set selectedCategories(List<String> value) {
    selectedCategoriesString = value.join(',');
  }

  // Helper methods for animals config
  List<String> get selectedAnimals => 
      selectedAnimalsString?.split(',').where((a) => a.isNotEmpty).toList() ?? [];

  set selectedAnimals(List<String> value) {
    selectedAnimalsString = value.join(',');
  }
} 