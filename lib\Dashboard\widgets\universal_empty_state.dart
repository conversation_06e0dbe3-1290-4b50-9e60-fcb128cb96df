import 'package:flutter/material.dart';

/// Universal Empty State Widget
/// 
/// A reusable widget for displaying empty states across the application.
/// Provides consistent styling and behavior for when there's no data to display.
class UniversalEmptyState extends StatelessWidget {
  final String title;
  final String message;
  final Widget? icon;
  final Widget? action;
  final Color? backgroundColor;
  final Color? textColor;

  const UniversalEmptyState({
    Key? key,
    required this.title,
    required this.message,
    this.icon,
    this.action,
    this.backgroundColor,
    this.textColor,
  }) : super(key: key);

  const UniversalEmptyState.noData({
    Key? key,
    String? title,
    String? message,
    Widget? action,
    Color? backgroundColor,
    Color? textColor,
  }) : this(
          key: key,
          title: title ?? 'No Data Available',
          message: message ?? 'There are no records to display.',
          icon: const Icon(Icons.inbox_outlined, size: 64),
          action: action,
          backgroundColor: backgroundColor,
          textColor: textColor,
        );

  const UniversalEmptyState.noResults({
    Key? key,
    String? title,
    String? message,
    Widget? action,
    Color? backgroundColor,
    Color? textColor,
  }) : this(
          key: key,
          title: title ?? 'No Results Found',
          message: message ?? 'Try adjusting your search or filters.',
          icon: const Icon(Icons.search_off_outlined, size: 64),
          action: action,
          backgroundColor: backgroundColor,
          textColor: textColor,
        );

  const UniversalEmptyState.error({
    Key? key,
    String? title,
    String? message,
    Widget? action,
    Color? backgroundColor,
    Color? textColor,
  }) : this(
          key: key,
          title: title ?? 'Something went wrong',
          message: message ?? 'Please try again later.',
          icon: const Icon(Icons.error_outline, size: 64),
          action: action,
          backgroundColor: backgroundColor,
          textColor: textColor,
        );

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (icon != null) ...[
              IconTheme(
                data: IconThemeData(
                  color: textColor ?? Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                  size: 64,
                ),
                child: icon!,
              ),
              const SizedBox(height: 24),
            ],
            Text(
              title,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: textColor ?? Theme.of(context).colorScheme.onSurface,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              message,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: textColor ?? Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            if (action != null) ...[
              const SizedBox(height: 32),
              action!,
            ],
          ],
        ),
      ),
    );
  }
}
