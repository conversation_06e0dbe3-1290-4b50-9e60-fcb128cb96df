import 'package:flutter/material.dart';

/// A more robust approach to managing icons in the app
class AppIcons {
  // Animal icons
  static const IconData cow = Icons.pets;
  static const IconData buffalo = Icons.cruelty_free;
  static const IconData goat = Icons.pets;
  static const IconData sheep = Icons.spa;
  static const IconData horse = Icons.emoji_nature;
  
  // Farm-related icons
  static const IconData farm = Icons.agriculture;
  static const IconData pasture = Icons.grass;
  static const IconData fence = Icons.fence;
  static const IconData water = Icons.water_drop;
  static const IconData nature = Icons.eco;
  static const IconData grass = Icons.grass;
  
  // Breeding and health icons
  static const IconData breeding = Icons.favorite;
  static const IconData health = Icons.monitor_heart;
  static const IconData medicine = Icons.medication;
  static const IconData vaccination = Icons.vaccines;
  static const IconData weight = Icons.monitor_weight;
  
  // Transaction and business icons
  static const IconData transaction = Icons.attach_money;
  static const IconData expense = Icons.money_off;
  static const IconData income = Icons.payments;
  static const IconData money = Icons.monetization_on;
  static const IconData milk = Icons.local_drink;
  static const IconData receipt = Icons.receipt_long;
  static const IconData store = Icons.store;
  static const IconData people = Icons.people;
  
  // Generic utility icons
  static const IconData defaultIcon = Icons.pets;
  static const IconData warning = Icons.warning;
  static const IconData success = Icons.check_circle;
  static const IconData error = Icons.error;
  
  /// Map animal type names to their icons
  static IconData getAnimalIcon(String? animalTypeName) {
    if (animalTypeName == null) return defaultIcon;
    
    switch (animalTypeName.toLowerCase()) {
      case 'cow':
      case 'cattle':
      case 'bull':
      case 'calf':
        return cow;
      case 'buffalo':
      case 'water buffalo':
        return buffalo;
      case 'goat':
      case 'kid': 
        return goat;
      case 'sheep':
      case 'lamb':
        return sheep;
      case 'horse':
      case 'pony':
      case 'foal':
        return horse;
      default:
        return defaultIcon;
    }
  }
  
  /// Get a color for an animal type
  static Color getAnimalColor(String? animalTypeName) {
    if (animalTypeName == null) return Colors.grey;
    
    switch (animalTypeName.toLowerCase()) {
      case 'cow':
      case 'bull': 
      case 'cattle':
      case 'calf':
        return Colors.brown;
      case 'buffalo':
      case 'water buffalo':
        return Colors.orange;
      case 'goat':
      case 'kid':
        return Colors.red;
      case 'sheep':
      case 'lamb':
        return Colors.lightBlue;
      case 'horse':
      case 'pony':
      case 'foal':
        return Colors.amber;
      default:
        return Colors.grey;
    }
  }
  
  /// Map transaction category names to their icons
  static IconData getCategoryIcon(String? categoryName) {
    if (categoryName == null) return defaultIcon;
    
    final name = categoryName.toLowerCase();
    
    // Income categories
    if (name.contains('milk') || name.contains('dairy')) {
      return milk;
    }
    if (name.contains('animal') && name.contains('sale')) {
      return income;
    }
    if (name.contains('income')) {
      return income;
    }
    
    // Expense categories
    if (name.contains('feed') || name.contains('food') || name.contains('hay')) {
      return grass;
    }
    if (name.contains('medicine') || name.contains('vet') || name.contains('health') || name.contains('drug')) {
      return medicine;
    }
    if (name.contains('supply') || name.contains('supplies') || name.contains('equipment')) {
      return store;
    }
    if (name.contains('labor') || name.contains('staff') || name.contains('worker') || name.contains('people')) {
      return people;
    }
    if (name.contains('expense')) {
      return expense;
    }
    
    // Default case
    return defaultIcon;
  }
} 