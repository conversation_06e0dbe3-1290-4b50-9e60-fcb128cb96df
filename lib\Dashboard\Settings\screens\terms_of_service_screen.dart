import 'package:flutter/material.dart';

class TermsOfServiceScreen extends StatelessWidget {
  const TermsOfServiceScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Terms of Service'),
        backgroundColor: Theme.of(context).primaryColor,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Terms of Service',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Last Updated: ${DateTime.now().toString().substring(0, 10)}',
              style: const TextStyle(
                color: Colors.grey,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 24),
            _buildSection(
              title: '1. Acceptance of Terms',
              content:
                  'By accessing or using the Cattle Manager App ("App"), you agree to be bound by these Terms of Service. If you do not agree to these terms, please do not use the App.',
            ),
            _buildSection(
              title: '2. Description of Service',
              content:
                  'The Cattle Manager App provides tools for managing cattle farms, including tracking animals, breeding records, health records, milk production, and financial transactions. The App stores data locally on your device and may offer cloud synchronization features in future updates.',
            ),
            _buildSection(
              title: '3. User Accounts',
              content:
                  'In future versions, the App may require you to create a user account with different roles (Admin, Manager, Staff, Veterinarian, Consultant). You are responsible for maintaining the confidentiality of your account information and for all activities that occur under your account.',
            ),
            _buildSection(
              title: '4. User Data',
              content:
                  'You retain all rights to your data. The App collects and stores information that you provide directly. We do not sell your personal information to third parties. Please refer to our Privacy Policy for more information on how we handle your data.',
            ),
            _buildSection(
              title: '5. Intellectual Property',
              content:
                  'All content included in the App, such as text, graphics, logos, and software, is the property of the App developers or its content suppliers and is protected by copyright laws. You may not modify, reproduce, or create derivative works based on the App or its content.',
            ),
            _buildSection(
              title: '6. Prohibited Uses',
              content:
                  'You agree not to use the App for any illegal purpose or in any way that could damage, disable, or impair the App. You also agree not to attempt to gain unauthorized access to any part of the App or any server, computer, or database connected to the App.',
            ),
            _buildSection(
              title: '7. Disclaimer of Warranties',
              content:
                  'The App is provided "as is" and "as available" without any warranties of any kind, either express or implied. We do not guarantee that the App will be error-free or uninterrupted.',
            ),
            _buildSection(
              title: '8. Limitation of Liability',
              content:
                  'To the fullest extent permitted by law, we shall not be liable for any indirect, incidental, special, consequential, or punitive damages, including loss of profits, data, or goodwill, arising out of or in connection with your use of the App.',
            ),
            _buildSection(
              title: '9. Changes to Terms',
              content:
                  'We reserve the right to modify these Terms at any time. We will provide notice of significant changes by updating the "Last Updated" date at the top of these Terms. Your continued use of the App after such changes constitutes your acceptance of the new Terms.',
            ),
            _buildSection(
              title: '10. Governing Law',
              content:
                  'These Terms shall be governed by and construed in accordance with the laws of the jurisdiction in which the App developers operate, without regard to its conflict of law provisions.',
            ),
            const SizedBox(height: 24),
            const Text(
              'Contact Us',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'If you have any questions about these Terms, please contact us at:',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 8),
            const Text(
              '<EMAIL>',
              style: TextStyle(
                fontSize: 16,
                color: Colors.blue,
              ),
            ),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSection({required String title, required String content}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: const TextStyle(
              fontSize: 16,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }
}
