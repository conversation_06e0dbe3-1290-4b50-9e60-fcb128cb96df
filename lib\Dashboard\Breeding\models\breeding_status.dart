import 'package:isar/isar.dart';
import 'package:json_annotation/json_annotation.dart';

part 'breeding_status.g.dart';

/// Represents the breeding status of a cattle
@embedded
@JsonSerializable()
class BreedingStatus {
  /// The current breeding status
  String? status; // 'Open', 'Bred', 'Pregnant', 'Fresh', etc.

  /// Is the animal currently pregnant
  bool isPregnant = false;

  /// Date of the most recent breeding
  DateTime? breedingDate;

  /// Expected date of calving based on breeding date
  DateTime? expectedCalvingDate;

  /// Last method used for breeding (Natural, AI, etc.)
  String? lastBreedingMethod;

  /// Default constructor
  BreedingStatus({
    this.status,
    this.isPregnant = false,
    this.breedingDate,
    this.expectedCalvingDate,
    this.lastBreedingMethod,
  });

  /// Create from a Map (deserialization)
  factory BreedingStatus.fromJson(Map<String, dynamic> json) =>
      _$BreedingStatusFromJson(json);

  /// Convert to a Map (serialization)
  Map<String, dynamic> toJson() => _$BreedingStatusToJson(this);
} 