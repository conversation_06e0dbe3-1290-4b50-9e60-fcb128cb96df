import 'package:logging/logging.dart';
import 'package:isar/isar.dart';
import 'package:flutter/foundation.dart';

import '../models/app_settings_isar.dart';
import '../models/user_preferences_isar.dart';
import '../../../services/database/isar_service.dart';
import '../../../services/database/exceptions/database_exceptions.dart';

// Legacy alias for compatibility
typedef SettingsHandler = SettingsRepository;

/// Consolidated repository for all Settings module database operations
class SettingsRepository extends ChangeNotifier {
  static final Logger _logger = Logger('SettingsRepository');
  final IsarService _isarService;
  bool _isInitialized = false;
  AppSettingsIsar? _appSettings;
  UserPreferencesIsar? _userPreferences;
  
  // Getters for UI access
  bool get isInitialized => _isInitialized;
  bool get darkMode => _appSettings?.theme == 'dark';
  String get language => _appSettings?.language ?? 'English';
  bool get notifications => _appSettings?.notificationsEnabled ?? true;
  int get dataRefreshInterval => 15; // Default value, not stored in model yet
  
  // Additional user preferences getters
  bool get showWelcomeScreen => _userPreferences?.showWelcomeScreen ?? true;
  bool get enableTutorials => _userPreferences?.enableTutorials ?? true;
  String get dashboardLayout => _userPreferences?.dashboardLayout ?? 'default';
  
  // Public constructor with explicit dependency injection
  SettingsRepository(this._isarService);
  
  // Getter for Isar instance
  Isar get _isar => _isarService.isar;
  
  /// Initialize the settings handler
  Future<void> init() async {
    if (_isInitialized) return;
    
    try {
      await initializeDefaultAppSettings();
      await initializeDefaultUserPreferences();
      
      // Load settings
      _appSettings = await getAppSettings();
      _userPreferences = await getUserPreferences();
      
      _isInitialized = true;
      notifyListeners();
    } catch (e) {
      _logger.severe('Error initializing settings: $e');
      rethrow;
    }
  }
  
  // UI Action Methods
  
  /// Set dark mode
  Future<void> setDarkMode(bool value) async {
    try {
      var settings = await getAppSettings();
      if (settings == null) {
        await initializeDefaultAppSettings();
        settings = await getAppSettings();
      }
      
      if (settings != null) {
        settings.theme = value ? 'dark' : 'light';
        await saveAppSettings(settings);
        _appSettings = settings;
        notifyListeners();
      }
    } catch (e) {
      _logger.severe('Error setting dark mode: $e');
      rethrow;
    }
  }
  
  /// Set language
  Future<void> setLanguage(String value) async {
    try {
      var settings = await getAppSettings();
      if (settings == null) {
        await initializeDefaultAppSettings();
        settings = await getAppSettings();
      }
      
      if (settings != null) {
        // Map display name to language code
        String langCode = 'en';
        switch (value) {
          case 'English': langCode = 'en'; break;
          case 'Spanish': langCode = 'es'; break;
          case 'French': langCode = 'fr'; break;
          case 'German': langCode = 'de'; break;
          default: langCode = 'en';
        }
        
        settings.language = langCode;
        await saveAppSettings(settings);
        _appSettings = settings;
        notifyListeners();
      }
    } catch (e) {
      _logger.severe('Error setting language: $e');
      rethrow;
    }
  }
  
  /// Set notifications
  Future<void> setNotifications(bool value) async {
    try {
      var settings = await getAppSettings();
      if (settings == null) {
        await initializeDefaultAppSettings();
        settings = await getAppSettings();
      }
      
      if (settings != null) {
        settings.notificationsEnabled = value;
        await saveAppSettings(settings);
        _appSettings = settings;
        notifyListeners();
      }
    } catch (e) {
      _logger.severe('Error setting notifications: $e');
      rethrow;
    }
  }
  
  /// Set data refresh interval
  Future<void> setDataRefreshInterval(int minutes) async {
    // This would be added to the AppSettingsIsar model in a real app
    // For now just notify listeners to update UI
    notifyListeners();
  }
  
  /// Clear application cache 
  Future<void> clearCache() async {
    try {
      // This is a stub method that would actually clear cache in a real app
      _logger.info('Cache cleared');
      
      // Show a success notification by updating the UI
      notifyListeners();
    } catch (e) {
      _logger.severe('Error clearing cache: $e');
      rethrow;
    }
  }
  
  //=== APP SETTINGS ===//
  
  /// Get application settings
  Future<AppSettingsIsar?> getAppSettings() async {
    try {
      return await _isar.collection<AppSettingsIsar>()
          .where()
          .findFirst();
    } catch (e) {
      _logger.severe('Error getting application settings: $e');
      throw DatabaseException('Failed to retrieve application settings', e.toString());
    }
  }

  /// Save application settings
  Future<void> saveAppSettings(AppSettingsIsar settings) async {
    try {
      await _validateAppSettings(settings);

      await _isar.writeTxn(() async {
        await _isar.collection<AppSettingsIsar>().put(settings);
      });

      _logger.info('Successfully saved application settings');
    } catch (e) {
      _logger.severe('Error saving application settings: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to save application settings', e.toString());
    }
  }

  /// Initialize default application settings
  Future<void> initializeDefaultAppSettings() async {
    try {
      final existingSettings = await getAppSettings();
      if (existingSettings == null) {
        final defaultSettings = AppSettingsIsar()
          ..language = 'en'
          ..theme = 'light'
          ..dateFormat = 'dd/MM/yyyy'
          ..timeFormat = '24h'
          ..weightUnit = 'kg'
          ..temperatureUnit = 'celsius'
          ..currencySymbol = '\$'
          ..notificationsEnabled = true
          ..autoBackupEnabled = true
          ..backupFrequency = 'daily'
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now();

        await saveAppSettings(defaultSettings);
        _logger.info('Successfully initialized default application settings');
      }
    } catch (e) {
      _logger.severe('Error initializing default application settings: $e');
      throw DatabaseException('Failed to initialize default settings', e.toString());
    }
  }

  //=== USER PREFERENCES ===//

  /// Get user preferences
  Future<UserPreferencesIsar?> getUserPreferences() async {
    try {
      return await _isar.collection<UserPreferencesIsar>()
          .where()
          .findFirst();
    } catch (e) {
      _logger.severe('Error getting user preferences: $e');
      throw DatabaseException('Failed to retrieve user preferences', e.toString());
    }
  }

  /// Save user preferences
  Future<void> saveUserPreferences(UserPreferencesIsar preferences) async {
    try {
      await _validateUserPreferences(preferences);

      await _isar.writeTxn(() async {
        await _isar.collection<UserPreferencesIsar>().put(preferences);
      });

      _logger.info('Successfully saved user preferences');
    } catch (e) {
      _logger.severe('Error saving user preferences: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to save user preferences', e.toString());
    }
  }

  /// Initialize default user preferences
  Future<void> initializeDefaultUserPreferences() async {
    try {
      final existingPreferences = await getUserPreferences();
      if (existingPreferences == null) {
        final defaultPreferences = UserPreferencesIsar()
          ..dashboardLayout = 'default'
          ..showWelcomeScreen = true
          ..enableTutorials = true
          ..enableReminders = true
          ..reminderTime = '09:00'
          ..defaultViewMode = 'list'
          ..itemsPerPage = 20
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now();

        await saveUserPreferences(defaultPreferences);
        _logger.info('Successfully initialized default user preferences');
      }
    } catch (e) {
      _logger.severe('Error initializing default user preferences: $e');
      throw DatabaseException('Failed to initialize default preferences', e.toString());
    }
  }

  //=== SETTINGS MANAGEMENT ===//

  /// Reset all settings to default values
  Future<void> resetAllSettings() async {
    try {
      await _isar.writeTxn(() async {
        // Delete all existing settings
        await _isar.collection<AppSettingsIsar>().clear();
        await _isar.collection<UserPreferencesIsar>().clear();

        // Initialize default settings
        await initializeDefaultAppSettings();
        await initializeDefaultUserPreferences();
      });

      _logger.info('Successfully reset all settings to default values');
      notifyListeners();
    } catch (e) {
      _logger.severe('Error resetting settings: $e');
      throw DatabaseException('Failed to reset settings', e.toString());
    }
  }

  /// Export settings as JSON
  Future<Map<String, dynamic>> exportSettings() async {
    try {
      final appSettings = await getAppSettings();
      final userPreferences = await getUserPreferences();

      return {
        'appSettings': appSettings?.toJson(),
        'userPreferences': userPreferences?.toJson(),
        'exportedAt': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      _logger.severe('Error exporting settings: $e');
      throw DatabaseException('Failed to export settings', e.toString());
    }
  }

  /// Import settings from JSON
  Future<void> importSettings(Map<String, dynamic> data) async {
    try {
      await _isar.writeTxn(() async {
        if (data['appSettings'] != null) {
          final appSettings = AppSettingsIsar.fromJson(data['appSettings']);
          await saveAppSettings(appSettings);
        }

        if (data['userPreferences'] != null) {
          final userPreferences = UserPreferencesIsar.fromJson(data['userPreferences']);
          await saveUserPreferences(userPreferences);
        }
      });

      _logger.info('Successfully imported settings');
      notifyListeners();
    } catch (e) {
      _logger.severe('Error importing settings: $e');
      throw DatabaseException('Failed to import settings', e.toString());
    }
  }

  //=== PRIVATE HELPER METHODS ===//

  /// Validate application settings
  Future<void> _validateAppSettings(AppSettingsIsar settings) async {
    if (settings.language == null || settings.language!.isEmpty) {
      throw ValidationException('Language is required');
    }

    if (settings.theme == null || settings.theme!.isEmpty) {
      throw ValidationException('Theme is required');
    }

    if (settings.dateFormat == null || settings.dateFormat!.isEmpty) {
      throw ValidationException('Date format is required');
    }

    if (settings.timeFormat == null || settings.timeFormat!.isEmpty) {
      throw ValidationException('Time format is required');
    }

    settings.updatedAt = DateTime.now();
  }

  /// Validate user preferences
  Future<void> _validateUserPreferences(UserPreferencesIsar preferences) async {
    if (preferences.dashboardLayout == null || preferences.dashboardLayout!.isEmpty) {
      throw ValidationException('Dashboard layout is required');
    }

    if (preferences.defaultViewMode == null || preferences.defaultViewMode!.isEmpty) {
      throw ValidationException('Default view mode is required');
    }

    if (preferences.itemsPerPage == null || preferences.itemsPerPage! <= 0) {
      throw ValidationException('Items per page must be greater than 0');
    }

    preferences.updatedAt = DateTime.now();
  }
} 