import 'package:flutter/material.dart';

class AppColors {
  // ============================================================================
  // GLOBAL APP COLORS
  // ============================================================================

  /// Primary app colors used throughout the application
  static const Color primary = Color(0xFF2E7D32);
  static const Color primaryColor = Color(0xFF2E7D32);
  static const Color accent = Color(0xFF4CAF50);
  static const Color accentColor = Color(0xFF4CAF50);
  static const Color background = Color(0xFFF5F5F5);
  static const Color error = Colors.red;
  static const Color success = Color(0xFF2E7D32);
  static const Color fallback = Color(0xFF2196F3); // Blue

  // Material Design color system compatibility
  static const Color secondary = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color info = Color(0xFF2196F3);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color onPrimary = Color(0xFFFFFFFF);
  static const Color onSecondary = Color(0xFFFFFFFF);
  static const Color onSurface = Color(0xFF000000);
  static const Color onBackground = Color(0xFF000000);

  // ============================================================================
  // MAIN DASHBOARD COLORS
  // ============================================================================

  /// Main dashboard specific colors
  static const Color dashboardHeader = Color(0xFF2E7D32); // Primary green
  static const Color dashboardCard = Color(0xFFF5F5F5); // Light background

  // ============================================================================
  // CATTLE MODULE COLORS
  // ============================================================================

  /// Cattle module header and primary color
  static const Color cattleHeader = Color(0xFF1976D2); // Blue

  /// Cattle Analytics - KPI Dashboard Colors (6 colors for main metrics)
  static const List<Color> cattleKpiColors = [
    Color(0xFF1976D2), // Blue
    Color(0xFF388E3C), // Green
    Color(0xFF7B1FA2), // Purple
    Color(0xFFD32F2F), // Red
    Color(0xFF00796B), // Teal
    Color(0xFFE91E63), // Pink
  ];

  /// Cattle Analytics - Age Demographics Colors (4 colors for age breakdown)
  static const List<Color> cattleAgeInsightColors = [
    Color(0xFF2196F3), // Light Blue
    Color(0xFF5E35B1), // Deep Purple
    Color(0xFFE53935), // Red
    Color(0xFF00796B), // Teal
  ];

  /// Cattle Analytics - Gender Distribution Colors
  static const Map<String, Color> cattleGenderColors = {
    'Male': Color(0xFF1976D2), // Blue
    'Female': Color(0xFFE91E63), // Pink
    'Unknown': Color(0xFF7B1FA2), // Purple
  };

  /// Cattle Analytics - Animal Type Colors
  static const Map<String, Color> cattleAnimalTypeColors = {
    'Cow': Color(0xFF388E3C), // Green
    'Bull': Color(0xFFD32F2F), // Red
    'Calf': Color(0xFF7B1FA2), // Purple
    'Heifer': Color(0xFF00796B), // Teal
    'Unknown': Color(0xFF424242), // Dark grey
  };

  /// Cattle Analytics - Age Distribution Colors (for pie charts)
  static const Map<String, Color> cattleAgeDistributionColors = {
    '0-6 months': Color(0xFF4CAF50), // Green
    '6-12 months': Color(0xFF2196F3), // Blue
    '1-2 years': Color(0xFF9C27B0), // Purple
    '2-5 years': Color(0xFF00796B), // Teal
    '5+ years': Color(0xFFE91E63), // Pink
  };

  /// Cattle Analytics - Section-specific colors
  static const Color cattleKpiSection = Color(0xFF1976D2); // Blue
  static const Color cattleAgeDemographics = Color(0xFF7B1FA2); // Purple
  static const Color cattleFinancialOverview = Color(0xFFD32F2F); // Red
  static const Color cattleHerdComposition = Color(0xFF388E3C); // Green

  // ============================================================================
  // BREEDING MODULE COLORS
  // ============================================================================

  /// Breeding module header and primary color
  static const Color breedingHeader = Color(0xFF7B1FA2); // Purple

  // TODO: Add breeding-specific colors when working on breeding module
  // static const List<Color> breedingKpiColors = [...];
  // static const Map<String, Color> breedingStatusColors = {...};

  // ============================================================================
  // HEALTH MODULE COLORS
  // ============================================================================

  /// Health module header and primary color
  static const Color healthHeader = Color(0xFFD32F2F); // Red

  // TODO: Add health-specific colors when working on health module
  // static const List<Color> healthKpiColors = [...];
  // static const Map<String, Color> healthStatusColors = {...};

  // ============================================================================
  // EVENTS MODULE COLORS
  // ============================================================================

  /// Events module header and primary color
  static const Color eventsHeader = Color(0xFF9C27B0); // Purple
  static const Color eventsColor = Color(0xFF9C27B0); // Purple (alias for compatibility)

  // ============================================================================
  // MILK MODULE COLORS
  // ============================================================================

  /// Milk module header and primary color
  static const Color milkHeader = Color(0xFF00796B); // Teal

  // TODO: Add milk-specific colors when working on milk module
  // static const List<Color> milkKpiColors = [...];
  // static const Map<String, Color> milkProductionColors = {...};

  // ============================================================================
  // WEIGHT MODULE COLORS
  // ============================================================================

  /// Weight module header and primary color
  static const Color weightHeader = Color(0xFF388E3C); // Green

  // TODO: Add weight-specific colors when working on weight module
  // static const List<Color> weightKpiColors = [...];
  // static const Map<String, Color> weightTrendColors = {...};

  // ============================================================================
  // TRANSACTION MODULE COLORS
  // ============================================================================

  /// Transaction module header and primary color
  static const Color transactionHeader = Color(0xFFE91E63); // Pink

  // TODO: Add transaction-specific colors when working on transaction module
  // static const List<Color> transactionKpiColors = [...];
  // static const Map<String, Color> transactionTypeColors = {...};
}