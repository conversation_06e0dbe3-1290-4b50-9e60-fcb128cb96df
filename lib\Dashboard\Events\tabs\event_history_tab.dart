/*
import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/event_isar.dart';
import '../../../services/database/database_helper.dart';
import 'package:intl/intl.dart';

class EventHistoryTab extends StatefulWidget {
  const EventHistoryTab({Key? key}) : super(key: key);

  @override
  State<EventHistoryTab> createState() => _EventHistoryTabState();
}

class _EventHistoryTabState extends State<EventHistoryTab> {
  final DatabaseHelper _dbHelper = DatabaseHelper.instance;
  List<EventIsar> _events = [];
  List<EventIsar> _filteredEvents = [];
  bool _isLoading = true;

  // Filter states
  String? _selectedCattleId;
  DateTimeRange? _selectedDateRange;
  EventType? _selectedEventType;
  String _searchQuery = '';

  // Chart view states
  bool _showCharts = false;
  String _selectedChartType = 'pie'; // 'pie' or 'line'

  @override
  void initState() {
    super.initState();
    _loadEvents();
  }

  Future<void> _loadEvents() async {
    try {
      setState(() => _isLoading = true);
      final events = await _dbHelper.eventsHandler.getAllEvents();
      final completedEvents = events.where((event) => event.isCompleted).toList();
      setState(() {
        _events = completedEvents;
        _applyFilters();
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error loading events: $e');
      setState(() {
        _events = [];
        _filteredEvents = [];
        _isLoading = false;
      });
    }
  }

  void _applyFilters() {
    var filtered = List<EventIsar>.from(_events);

    // Apply cattle filter
    if (_selectedCattleId != null) {
      filtered = filtered.where((e) => e.cattleId == _selectedCattleId).toList();
    }

    // Apply date range filter
    if (_selectedDateRange != null) {
      filtered = filtered.where((e) {
        if (e.eventDate == null) return false;
        final eventDate = DateTime(
          e.eventDate!.year, 
          e.eventDate!.month, 
          e.eventDate!.day
        );
        return eventDate.isAfter(
                _selectedDateRange!.start.subtract(const Duration(days: 1))) &&
            eventDate
                .isBefore(_selectedDateRange!.end.add(const Duration(days: 1)));
      }).toList();
    }

    // Apply event type filter
    if (_selectedEventType != null && _selectedEventType != null) {
      filtered = filtered.where((e) => 
        e.type != null && e.type!.toEventType() == _selectedEventType
      ).toList();
    }

    // Apply search query
    if (_searchQuery.isNotEmpty) {
      final query = _searchQuery.toLowerCase();
      filtered = filtered
          .where((e) =>
              (e.title != null && e.title!.toLowerCase().contains(query)) ||
              (e.notes != null && e.notes!.toLowerCase().contains(query)))
          .toList();
    }

    setState(() => _filteredEvents = filtered);
  }

  Widget _buildFilterBar() {
    return Card(
      margin: const EdgeInsets.all(8),
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: TextField(
                    decoration: const InputDecoration(
                      hintText: 'Search events...',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                        _applyFilters();
                      });
                    },
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  icon: Icon(_showCharts ? Icons.list : Icons.bar_chart),
                  onPressed: () => setState(() => _showCharts = !_showCharts),
                  tooltip: _showCharts ? 'Show List' : 'Show Charts',
                ),
              ],
            ),
            const SizedBox(height: 8),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  // Cattle filter
                  DropdownButton<String>(
                    hint: const Text('Select Cattle'),
                    value: _selectedCattleId,
                    items: [
                      const DropdownMenuItem<String>(
                        value: null,
                        child: Text('All Cattle'),
                      ),
                      ..._events
                          .where((e) => e.cattleId != null && e.cattleId!.isNotEmpty)
                          .map((e) => e.cattleId!)
                          .toSet()
                          .map((id) => DropdownMenuItem<String>(
                                value: id,
                                child: Text('Cattle #$id'),
                              )),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedCattleId = value;
                        _applyFilters();
                      });
                    },
                  ),
                  const SizedBox(width: 16),
                  // Event type filter
                  DropdownButton<EventType>(
                    hint: const Text('Event Type'),
                    value: _selectedEventType,
                    items: [
                      const DropdownMenuItem<EventType>(
                        value: null,
                        child: Text('All Types'),
                      ),
                      ...EventType.values.map((type) => DropdownMenuItem<EventType>(
                            value: type,
                            child: Text(type.toString().split('.').last),
                          )),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedEventType = value;
                        _applyFilters();
                      });
                    },
                  ),
                  const SizedBox(width: 16),
                  // Date range filter
                  OutlinedButton.icon(
                    icon: const Icon(Icons.date_range),
                    label: Text(_selectedDateRange != null
                        ? '${_formatDate(_selectedDateRange!.start)} - ${_formatDate(_selectedDateRange!.end)}'
                        : 'Select Date Range'),
                    onPressed: () => _selectDateRange(context),
                  ),
                  const SizedBox(width: 8),
                  if (_selectedDateRange != null || _selectedEventType != null || _selectedCattleId != null)
                    TextButton.icon(
                      icon: const Icon(Icons.clear),
                      label: const Text('Clear Filters'),
                      onPressed: _clearFilters,
                    ),
                ],
              ),
            ),
            if (_showCharts) 
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: SegmentedButton<String>(
                  segments: const [
                    ButtonSegment<String>(
                      value: 'pie',
                      label: Text('Type Distribution'),
                      icon: Icon(Icons.pie_chart),
                    ),
                    ButtonSegment<String>(
                      value: 'line',
                      label: Text('Timeline'),
                      icon: Icon(Icons.show_chart),
                    ),
                  ],
                  selected: {_selectedChartType},
                  onSelectionChanged: (value) {
                    setState(() {
                      _selectedChartType = value.first;
                    });
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }

  void _clearFilters() {
    setState(() {
      _selectedCattleId = null;
      _selectedDateRange = null;
      _selectedEventType = null;
      _searchQuery = '';
      _applyFilters();
    });
  }

  Future<void> _selectDateRange(BuildContext context) async {
    final initialDateRange = _selectedDateRange ?? 
        DateTimeRange(
          start: DateTime.now().subtract(const Duration(days: 30)),
          end: DateTime.now(),
        );
    
    final newDateRange = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: initialDateRange,
    );
    
    if (newDateRange != null) {
      setState(() {
        _selectedDateRange = newDateRange;
        _applyFilters();
      });
    }
  }

  Widget _buildCharts() {
    if (_filteredEvents.isEmpty) {
      return const Center(
        child: Text('No data to display'),
      );
    }

    if (_selectedChartType == 'pie') {
      return _buildPieChart();
    } else {
      return _buildLineChart();
    }
  }

  Widget _buildPieChart() {
    // Count events by type
    final Map<String, int> typeCount = {};
    for (var event in _filteredEvents) {
      final type = event.type?.getDisplayName() ?? 'Unknown';
      typeCount[type] = (typeCount[type] ?? 0) + 1;
    }
    
    // Sort by count descending
    final sortedTypes = typeCount.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    // Create pie chart sections
    final sections = <PieChartSectionData>[];
    final colors = [
      Colors.blue,
      Colors.green,
      Colors.red,
      Colors.yellow,
      Colors.purple,
      Colors.orange,
      Colors.teal,
      Colors.pink,
    ];
    
    for (var i = 0; i < sortedTypes.length; i++) {
      final entry = sortedTypes[i];
      final color = colors[i % colors.length];
      sections.add(
        PieChartSectionData(
          color: color,
          value: entry.value.toDouble(),
          title: '${entry.key}\n${entry.value}',
          radius: 100,
          titleStyle: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          const Text(
            'Events by Type',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          AspectRatio(
            aspectRatio: 1.5,
            child: PieChart(
              PieChartData(
                sections: sections,
                centerSpaceRadius: 40,
                sectionsSpace: 2,
              ),
            ),
          ),
          const SizedBox(height: 16),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: List.generate(
              sortedTypes.length,
              (index) {
                final entry = sortedTypes[index];
                final color = colors[index % colors.length];
                return Chip(
                  label: Text('${entry.key}: ${entry.value}'),
                  backgroundColor: color.withAlpha(20),
                  avatar: CircleAvatar(
                    backgroundColor: color,
                    radius: 8,
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLineChart() {
    // Group events by month
    final Map<DateTime, int> eventsByMonth = {};
    for (var event in _filteredEvents) {
      if (event.eventDate != null) {
        final month = DateTime(event.eventDate!.year, event.eventDate!.month);
        eventsByMonth[month] = (eventsByMonth[month] ?? 0) + 1;
      }
    }
    
    // Sort by date
    final sortedMonths = eventsByMonth.entries.toList()
      ..sort((a, b) => a.key.compareTo(b.key));
    
    if (sortedMonths.isEmpty) {
      return const Center(
        child: Text('No date data available'),
      );
    }
    
    // Create line chart data
    final spots = sortedMonths
        .map((e) => FlSpot(
              e.key.millisecondsSinceEpoch.toDouble(),
              e.value.toDouble(),
            ))
        .toList();

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          const Text(
            'Events Timeline',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          AspectRatio(
            aspectRatio: 1.5,
            child: LineChart(
              LineChartData(
                gridData: const FlGridData(show: true),
                titlesData: FlTitlesData(
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      getTitlesWidget: (value, meta) {
                        final date = DateTime.fromMillisecondsSinceEpoch(value.toInt());
                        return Text(
                          DateFormat('MMM yyyy').format(date),
                          style: const TextStyle(fontSize: 10),
                        );
                      },
                      interval: 30 * 24 * 60 * 60 * 1000, // Approximately 1 month
                    ),
                  ),
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      getTitlesWidget: (value, meta) {
                        return Text(
                          value.toInt().toString(),
                          style: const TextStyle(fontSize: 10),
                        );
                      },
                      reservedSize: 30,
                    ),
                  ),
                  topTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  rightTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                ),
                borderData: FlBorderData(show: true),
                lineBarsData: [
                  LineChartBarData(
                    spots: spots,
                    isCurved: true,
                    color: Colors.blue,
                    barWidth: 3,
                    belowBarData: BarAreaData(
                      show: true,
                      color: Colors.blue.withAlpha(51),
                    ),
                    dotData: const FlDotData(show: true),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showEventDetails(BuildContext context, EventIsar event) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(event.title ?? 'Event Details'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              if (event.eventDate != null)
                ListTile(
                  leading: const Icon(Icons.calendar_today),
                  title: const Text('Date'),
                  subtitle: Text(_formatDate(event.eventDate!)),
                ),
              if (event.type != null)
                ListTile(
                  leading: Icon(event.type?.getIcon() ?? Icons.event),
                  title: const Text('Type'),
                  subtitle: Text(event.type?.getDisplayName() ?? 'Unknown'),
                ),
              if (event.cattleId != null && event.cattleId!.isNotEmpty)
                ListTile(
                  leading: const Icon(Icons.pets),
                  title: const Text('Cattle ID'),
                  subtitle: Text(event.cattleId!),
                ),
              if (event.notes != null && event.notes!.isNotEmpty)
                ListTile(
                  leading: const Icon(Icons.notes),
                  title: const Text('Notes'),
                  subtitle: Text(event.notes!),
                ),
              ListTile(
                leading: const Icon(Icons.check_circle, color: Colors.green),
                title: const Text('Completed'),
                subtitle: Text(
                  event.updatedAt != null
                      ? _formatDate(event.updatedAt!)
                      : 'Unknown',
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return Column(
      children: [
        _buildFilterBar(),
        Expanded(
          child: _showCharts
              ? _buildCharts()
              : _filteredEvents.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.history,
                            size: 64,
                            color: Theme.of(context).primaryColor.withAlpha(128),
                          ),
                          const SizedBox(height: 16),
                          const Text(
                            'No completed events found',
                            style: TextStyle(fontSize: 18),
                          ),
                          if (_selectedDateRange != null || _selectedEventType != null || _selectedCattleId != null)
                            TextButton.icon(
                              icon: const Icon(Icons.filter_alt_off),
                              label: const Text('Clear Filters'),
                              onPressed: _clearFilters,
                            ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      itemCount: _filteredEvents.length,
                      itemBuilder: (context, index) {
                        final event = _filteredEvents[index];
                        final icon = event.type?.getIcon() ?? Icons.event;
                        final color = event.type?.getColor() ?? Colors.grey;
                        
                        return Card(
                          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          child: ListTile(
                            leading: CircleAvatar(
                              backgroundColor: color.withAlpha(40),
                              child: Icon(icon, color: color),
                            ),
                            title: Text(
                              event.title ?? 'Untitled Event',
                              style: const TextStyle(fontWeight: FontWeight.bold),
                            ),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                if (event.type != null)
                                  Text(event.type!.getDisplayName()),
                                if (event.eventDate != null)
                                  Text('Date: ${_formatDate(event.eventDate!)}'),
                                if (event.notes != null && event.notes!.isNotEmpty)
                                  Text(
                                    event.notes!,
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                              ],
                            ),
                            onTap: () => _showEventDetails(context, event),
                            isThreeLine: true,
                          ),
                        );
                      },
                    ),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    final formatter = DateFormat('yyyy-MM-dd');
    return formatter.format(date);
  }
}
*/
