import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:get_it/get_it.dart';
import '../models/cattle_isar.dart';

import '../services/cattle_repository.dart';
import '../../Health/services/health_repository.dart';
import '../../Milk Records/services/milk_repository.dart';

class QRCodeService {

  static final CattleRepository _cattleRepository = GetIt.instance<CattleRepository>();
  static final HealthRepository _healthRepository = GetIt.instance<HealthRepository>();
  static final MilkRepository _milkRepository = GetIt.instance<MilkRepository>();
  
  static Future<Widget> generateQRCode(CattleIsar cattle,
      {double size = 200}) async {
    final data = await generateQRData(cattle);
    final jsonData = Uri.encodeComponent(data);

    return QrImageView(
      data: jsonData,
      version: QrVersions.auto,
      size: size,
      backgroundColor: Colors.white,
      errorStateBuilder: (context, error) => const Center(
        child: Text(
          'Error generating QR code',
          style: TextStyle(color: Colors.red),
        ),
      ),
    );
  }

  static Future<String> generateQRData(CattleIsar cattle) async {
    // Fetch all related data using the repositories
    final healthRecords =
        await _healthRepository.getHealthRecordsForCattle(cattle.tagId ?? '');
    final milkRecords =
        await _milkRepository.getMilkRecordsForCattle(cattle.tagId ?? '');
    final offspring =
        await _cattleRepository.getCattleByMotherTagId(cattle.tagId ?? '');

    // Create comprehensive data map
    final Map<String, dynamic> data = {
      'tagId': cattle.tagId,
      'name': cattle.name,
      'breedId': cattle.breedId,
      'animalTypeId': cattle.animalTypeId,
      'gender': cattle.gender,
      'source': cattle.source,
      'dateOfBirth': cattle.dateOfBirth?.toIso8601String(),
      'purchaseDate': cattle.purchaseDate?.toIso8601String(),
      'purchasePrice': cattle.purchasePrice,
      'motherTagId': cattle.motherTagId,
      'weight': cattle.weight,
      'color': cattle.color,
      'notes': cattle.notes,
      'photoPath': cattle.photoPath,
      'reproductiveStatus': cattle.breedingStatus?.status,
      'lastHeatDate': cattle.breedingStatus?.lastHeatDate?.toIso8601String(),
      'isPregnant': cattle.breedingStatus?.isPregnant,
      'breedingDate': cattle.breedingStatus?.breedingDate?.toIso8601String(),
      'healthRecords': healthRecords
          .map((record) => {
                'date': record.date?.toIso8601String(),
                'type': record.recordType,
                'description': record.details,
                'medication': record.medicine,
                'cost': record.cost,
              })
          .toList(),
      'milkRecords': milkRecords
          .map((record) => {
                'date': record.date?.toIso8601String(),
                'morning': record.morning,
                'afternoon': record.afternoon,
                'evening': record.evening,
                'total': record.totalYield,
              })
          .toList(),
      'offspring': offspring
          .map((child) => {
                'tagId': child.tagId,
                'name': child.name,
                'dateOfBirth': child.dateOfBirth?.toIso8601String(),
                'gender': child.gender,
                'breedId': child.breedId,
              })
          .toList(),
      'lastUpdated': DateTime.now().toIso8601String(),
    };

    return jsonEncode(data);
  }

  static Future<Map<String, dynamic>?> parseQRData(String qrData) async {
    try {
      final decodedData = Uri.decodeComponent(qrData);
      return jsonDecode(decodedData) as Map<String, dynamic>;
    } catch (e) {
      debugPrint('Error parsing QR data: $e');
      return null;
    }
  }

  static Future<Widget> generateEnhancedQRCode(CattleIsar cattle,
      {double size = 200}) async {
    final data = await generateQRData(cattle);
    final jsonData = Uri.encodeComponent(data);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(76),
            spreadRadius: 2,
            blurRadius: 5,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          QrImageView(
            data: jsonData,
            version: QrVersions.auto,
            size: size,
            backgroundColor: Colors.white,
            errorStateBuilder: (context, error) => const Center(
              child: Text(
                'Error generating QR code',
                style: TextStyle(color: Colors.red),
              ),
            ),
          ),
          const SizedBox(height: 12),
          Text(
            cattle.tagId ?? 'Unknown ID',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          if (cattle.name != null && cattle.name!.isNotEmpty)
            Text(
              cattle.name!,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
        ],
      ),
    );
  }

  // Store QR code data using Isar
  static Future<void> storeQRCode(String cattleTagId, String qrData) async {
    try {
      // Store QR data in preferences or a dedicated collection
      // Implementation will depend on how you want to store this data
      // For now, we'll just log that this method is called
      debugPrint('QR code data stored for cattle: $cattleTagId');
      return;
    } catch (e) {
      debugPrint('Error storing QR code data: $e');
      throw Exception('Failed to store QR code data: $e');
    }
  }

  // Retrieve QR code data
  static Future<String?> retrieveQRCode(String cattleTagId) async {
    try {
      // In a real implementation, you would retrieve the data from Isar
      // For now, we'll generate it on the fly
      final cattle = await _cattleRepository.getCattleByTagId(cattleTagId);
      if (cattle != null) {
        return await generateQRData(cattle);
      }
      return null;
    } catch (e) {
      debugPrint('Error retrieving QR code data: $e');
      return null;
    }
  }
}
