import 'package:flutter/material.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../models/treatment_isar.dart';

class TreatmentFormDialog extends StatefulWidget {
  final TreatmentIsar? treatment;
  final List<CattleIsar> cattle;
  final String? cattleId;
  final Future<void> Function(TreatmentIsar)? onSave;

  const TreatmentFormDialog({
    Key? key,
    this.treatment,
    required this.cattle,
    this.cattleId,
    this.onSave,
  }) : super(key: key);

  @override
  State<TreatmentFormDialog> createState() => _TreatmentFormDialogState();
}

class _TreatmentFormDialogState extends State<TreatmentFormDialog> {
  static const _inputDecorationConstraints = BoxConstraints(maxHeight: 56);
  static const _inputContentPadding =
      EdgeInsets.symmetric(horizontal: 12, vertical: 8);
  static const _animationDuration = Duration(milliseconds: 200);

  final _formKey = GlobalKey<FormState>();
  final _scrollController = ScrollController();

  String _cattleId = '';
  String _condition = '';
  String _treatment = '';
  String _veterinarian = '';
  String _dosage = '';
  DateTime _date = DateTime.now();
  double _cost = 0.0;
  String _notes = '';
  String? _status = 'Active';
  int? _isarId;

  List<CattleIsar> _cattleList = [];
  bool _isLoading = true;
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _loadCattleData();
    if (widget.treatment != null) {
      _isarId = widget.treatment!.id;
      _cattleId = widget.treatment!.cattleId ?? '';
      _condition = widget.treatment!.condition ?? '';
      _treatment = widget.treatment!.treatment ?? '';
      _veterinarian = widget.treatment!.veterinarian ?? '';
      _dosage = widget.treatment!.dosage ?? '';
      _date = widget.treatment!.date ?? DateTime.now();
      _cost = double.tryParse(widget.treatment!.cost ?? '0.0') ?? 0.0;
      _notes = widget.treatment!.notes ?? '';
      _status = widget.treatment!.status;
    } else if (widget.cattleId != null) {
      _cattleId = widget.cattleId!;
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadCattleData() async {
    try {
      if (mounted) {
        setState(() {
          _cattleList = widget.cattle;
          if (_cattleList.isNotEmpty && _cattleId.isEmpty) {
            // Initialize with the first cattle's business ID (not Isar ID)
            _cattleId = _cattleList.first.businessId ?? '';
          }
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Could not load cattle information. Please try again.')),
        );
      }
    }
  }

  Future<void> _saveTreatment() async {
    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();

      setState(() {
        _isSaving = true;
      });

      try {
        final treatmentIsar = TreatmentIsar(
          cattleId: _cattleId,
          date: _date,
          condition: _condition,
          treatment: _treatment,
          veterinarian: _veterinarian,
          dosage: _dosage,
          cost: _cost.toString(), // Model expects String
          notes: _notes,
          status: _status,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Preserve Isar ID for edits
        if (_isarId != null) {
          treatmentIsar.id = _isarId!;
        }

        if (mounted) {
          if (widget.onSave != null) {
            // Use the onSave callback if provided
            await widget.onSave!(treatmentIsar);
            if (mounted) {
              setState(() {
                _isSaving = false;
              });
              Navigator.pop(context);
            }
          } else {
            // Return the treatment object directly
            setState(() {
              _isSaving = false;
            });
            Navigator.pop(context, treatmentIsar);
          }
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            _isSaving = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Failed to save treatment. Please check your inputs and try again.')),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: AnimatedContainer(
        duration: _animationDuration,
        curve: Curves.easeInOut,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
          maxWidth: MediaQuery.of(context).size.width * 0.9,
        ),
        decoration: BoxDecoration(
          color: Theme.of(context).dialogTheme.backgroundColor ?? Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
        ),
        child: _isLoading
            ? const SizedBox(
                height: 200,
                child: Center(child: CircularProgressIndicator()),
              )
            : Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Header
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: const BoxDecoration(
                      color: Color(0xFF2E7D32),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(16),
                        topRight: Radius.circular(16),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          widget.treatment == null
                              ? Icons.add_circle
                              : Icons.edit,
                          color: Colors.white,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          widget.treatment == null
                              ? 'Add Treatment'
                              : 'Edit Treatment',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Form Content
                  Flexible(
                    child: NotificationListener<ScrollNotification>(
                      onNotification: (notification) {
                        if (notification is ScrollUpdateNotification) {
                          if (notification.dragDetails != null) {
                            FocusScope.of(context).unfocus();
                          }
                        }
                        return false;
                      },
                      child: SingleChildScrollView(
                        controller: _scrollController,
                        padding: const EdgeInsets.all(16.0),
                        physics: const ClampingScrollPhysics(),
                        keyboardDismissBehavior:
                            ScrollViewKeyboardDismissBehavior.onDrag,
                        child: Form(
                          key: _formKey,
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              // Cattle Selection Dropdown
                              DropdownButtonFormField<String>(
                                isExpanded: true,
                                menuMaxHeight: 300,
                                decoration: InputDecoration(
                                  labelText: 'Select Cattle',
                                  border: const OutlineInputBorder(),
                                  contentPadding: _inputContentPadding,
                                  constraints: _inputDecorationConstraints,
                                  isDense: true,
                                  prefixIcon: Icon(
                                    Icons.pets,
                                    color: Colors.brown.shade400,
                                  ),
                                ),
                                value: _cattleList.any((c) => c.businessId == _cattleId)
                                    ? _cattleId
                                    : null,
                                items: _cattleList.map((cattle) {
                                  return DropdownMenuItem<String>(
                                    value: cattle.businessId,
                                    child: Text(
                                      '${cattle.name} (Tag ID: ${cattle.tagId})',
                                      overflow: TextOverflow.ellipsis,
                                      style: const TextStyle(fontSize: 14),
                                    ),
                                  );
                                }).toList(),
                                onChanged: _isSaving
                                    ? null
                                    : (value) {
                                        if (value != null) {
                                          setState(() {
                                            _cattleId = value;
                                          });
                                        }
                                      },
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please select a cattle';
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: 16),

                              // Condition Field
                              TextFormField(
                                decoration: const InputDecoration(
                                  labelText: 'Condition',
                                  border: OutlineInputBorder(),
                                  hintText: 'Enter condition',
                                  contentPadding: _inputContentPadding,
                                  constraints: _inputDecorationConstraints,
                                  prefixIcon: Icon(
                                    Icons.medical_information,
                                    color: Colors.red,
                                  ),
                                ),
                                initialValue: _condition,
                                textInputAction: TextInputAction.next,
                                enabled: !_isSaving,
                                onSaved: (value) {
                                  _condition = value ?? '';
                                },
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please enter a condition';
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: 16),

                              // Treatment Field
                              TextFormField(
                                decoration: const InputDecoration(
                                  labelText: 'Treatment',
                                  border: OutlineInputBorder(),
                                  hintText: 'Enter treatment details',
                                  contentPadding: _inputContentPadding,
                                  constraints: _inputDecorationConstraints,
                                  prefixIcon: Icon(
                                    Icons.healing,
                                    color: Colors.green,
                                  ),
                                ),
                                initialValue: _treatment,
                                textInputAction: TextInputAction.next,
                                enabled: !_isSaving,
                                onSaved: (value) {
                                  _treatment = value ?? '';
                                },
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please enter a treatment';
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: 16),

                              // Dosage Field
                              TextFormField(
                                decoration: const InputDecoration(
                                  labelText: 'Dosage',
                                  border: OutlineInputBorder(),
                                  hintText: 'Enter dosage details',
                                  contentPadding: _inputContentPadding,
                                  constraints: _inputDecorationConstraints,
                                  prefixIcon: Icon(
                                    Icons.medication,
                                    color: Colors.purple,
                                  ),
                                ),
                                initialValue: _dosage,
                                textInputAction: TextInputAction.next,
                                enabled: !_isSaving,
                                onSaved: (value) {
                                  _dosage = value ?? '';
                                },
                              ),
                              const SizedBox(height: 16),

                              // Date Picker
                              InkWell(
                                onTap: _isSaving
                                    ? null
                                    : () async {
                                        final pickedDate = await showDatePicker(
                                          context: context,
                                          initialDate: _date,
                                          firstDate: DateTime(2000),
                                          lastDate: DateTime.now()
                                              .add(const Duration(days: 365)),
                                        );
                                        if (pickedDate != null && mounted) {
                                          setState(() {
                                            _date = pickedDate;
                                          });
                                        }
                                      },
                                child: InputDecorator(
                                  decoration: const InputDecoration(
                                    labelText: 'Date',
                                    border: OutlineInputBorder(),
                                    hintText: 'Select date',
                                    contentPadding: _inputContentPadding,
                                    constraints: _inputDecorationConstraints,
                                    prefixIcon: Icon(
                                      Icons.calendar_today,
                                      color: Colors.blue,
                                    ),
                                  ),
                                  child: Text(
                                    '${_date.day}/${_date.month}/${_date.year}',
                                    style: const TextStyle(fontSize: 16),
                                  ),
                                ),
                              ),
                              const SizedBox(height: 16),

                              // Veterinarian Field
                              TextFormField(
                                decoration: const InputDecoration(
                                  labelText: 'Veterinarian',
                                  border: OutlineInputBorder(),
                                  hintText: 'Enter veterinarian name',
                                  contentPadding: _inputContentPadding,
                                  constraints: _inputDecorationConstraints,
                                  prefixIcon: Icon(
                                    Icons.person,
                                    color: Colors.indigo,
                                  ),
                                ),
                                initialValue: _veterinarian,
                                textInputAction: TextInputAction.next,
                                enabled: !_isSaving,
                                onSaved: (value) {
                                  _veterinarian = value ?? '';
                                },
                              ),
                              const SizedBox(height: 16),

                              // Cost Field
                              TextFormField(
                                decoration: const InputDecoration(
                                  labelText: 'Cost',
                                  border: OutlineInputBorder(),
                                  prefixText: '\$',
                                  hintText: '0.00',
                                  contentPadding: _inputContentPadding,
                                  constraints: _inputDecorationConstraints,
                                  prefixIcon: Icon(
                                    Icons.attach_money,
                                    color: Colors.amber,
                                  ),
                                ),
                                initialValue: _cost.toString(),
                                keyboardType:
                                    const TextInputType.numberWithOptions(
                                        decimal: true),
                                textInputAction: TextInputAction.next,
                                enabled: !_isSaving,
                                onSaved: (value) {
                                  _cost = double.tryParse(value ?? '0') ?? 0;
                                },
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return null;
                                  }
                                  if (double.tryParse(value) == null) {
                                    return 'Please enter a valid number';
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: 16),

                              // Notes Field
                              TextFormField(
                                decoration: const InputDecoration(
                                  labelText: 'Notes',
                                  border: OutlineInputBorder(),
                                  hintText: 'Enter any additional notes',
                                  contentPadding: EdgeInsets.all(16),
                                ),
                                initialValue: _notes,
                                maxLines: 3,
                                keyboardType: TextInputType.multiline,
                                enabled: !_isSaving,
                                onSaved: (value) {
                                  _notes = value ?? '';
                                },
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                  // Action Buttons
                  Container(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        Expanded(
                          child: TextButton(
                            onPressed:
                                _isSaving ? null : () => Navigator.pop(context),
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: const Text('Cancel'),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _isSaving ? null : _saveTreatment,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF2E7D32),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: _isSaving
                                ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      color: Colors.white,
                                    ),
                                  )
                                : const Text('Save'),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}
