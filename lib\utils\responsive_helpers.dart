import 'package:flutter/material.dart';

/// Responsive design helpers for consistent sizing across the application
///
/// These utilities provide consistent responsive behavior for:
/// - Padding and spacing
/// - Icon sizes
/// - Font sizes
/// - Layout breakpoints
///
/// Usage Example:
/// ```dart
/// double padding = ResponsiveHelpers.getPadding(context);
/// double iconSize = ResponsiveHelpers.getIconSize(context);
/// ```
class ResponsiveHelpers {
  ResponsiveHelpers._(); // Private constructor to prevent instantiation

  // Breakpoints for responsive design
  static const double _smallScreenBreakpoint = 600;
  static const double _mediumScreenBreakpoint = 900;

  /// Get responsive padding based on screen width
  static double getPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < _smallScreenBreakpoint) return 12.0;
    if (screenWidth < _mediumScreenBreakpoint) return 16.0;
    return 20.0;
  }

  /// Get responsive vertical padding based on screen width
  static double getVerticalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < _smallScreenBreakpoint) return 8.0;
    return 12.0;
  }

  /// Get responsive spacing between elements
  static double getSpacing(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < _smallScreenBreakpoint) return 8.0;
    if (screenWidth < _mediumScreenBreakpoint) return 12.0;
    return 16.0;
  }

  /// Get responsive icon size
  static double getIconSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < _smallScreenBreakpoint) return 18.0;
    return 20.0;
  }

  /// Get responsive font size
  static double getFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < _smallScreenBreakpoint) return 12.0;
    if (screenWidth < _mediumScreenBreakpoint) return 13.0;
    return 14.0;
  }

  /// Get responsive button height
  static double getButtonHeight(BuildContext context, {bool compact = false}) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (compact) {
      return screenWidth < _smallScreenBreakpoint ? 36.0 : 40.0;
    }
    return screenWidth < _smallScreenBreakpoint ? 44.0 : 48.0;
  }

  /// Check if screen is small (mobile)
  static bool isSmallScreen(BuildContext context) {
    return MediaQuery.of(context).size.width < _smallScreenBreakpoint;
  }

  /// Check if screen is medium (tablet)
  static bool isMediumScreen(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    return screenWidth >= _smallScreenBreakpoint && 
           screenWidth < _mediumScreenBreakpoint;
  }

  /// Check if screen is large (desktop)
  static bool isLargeScreen(BuildContext context) {
    return MediaQuery.of(context).size.width >= _mediumScreenBreakpoint;
  }

  /// Get responsive border radius
  static double getBorderRadius(BuildContext context) {
    return isSmallScreen(context) ? 6.0 : 8.0;
  }

  /// Get responsive elevation
  static double getElevation(BuildContext context) {
    return isSmallScreen(context) ? 1.0 : 2.0;
  }
}
