/// Unified age calculation utility for cattle
/// 
/// This utility provides consistent age calculation logic across the application,
/// eliminating duplication and ensuring business rules are applied uniformly.

import '../models/cattle_isar.dart';

/// Constants for age calculation
class CattleAgeConstants {
  /// Default age assumption when cattle is purchased without known date of birth
  static const int defaultAgeAtPurchaseInDays = 365; // Assume 1 year old

  /// Average days per month for age calculations
  static const double averageDaysPerMonth = 30.44;
}

/// Represents an age range for categorization
class AgeRange {
  final String label;
  final int minMonths;
  final int maxMonths;

  const AgeRange(this.label, this.minMonths, this.maxMonths);
}

/// Unified cattle age calculator
class CattleAgeCalculator {
  /// Predefined age ranges for consistent categorization
  static const List<AgeRange> ageRanges = [
    AgeRange('0-6 months', 0, 6),
    AgeRange('6 months-2 years', 7, 24),
    <PERSON><PERSON><PERSON><PERSON>('2-8 years', 25, 96),
    <PERSON><PERSON><PERSON><PERSON>('8+ years', 97, 999),
  ];
  /// Calculate the reference date for age calculation
  /// 
  /// Priority:
  /// 1. Date of birth (if available)
  /// 2. Purchase date + default age (if purchase date available)
  /// 3. null (if neither available)
  static DateTime? _getAgeReferenceDate(CattleIsar cattle) {
    // Use date of birth if available
    if (cattle.dateOfBirth != null) {
      return cattle.dateOfBirth!;
    }
    
    // If no DOB, estimate birth date by subtracting default age from purchase date
    if (cattle.purchaseDate != null) {
      return cattle.purchaseDate!.subtract(
        const Duration(days: CattleAgeConstants.defaultAgeAtPurchaseInDays)
      );
    }
    
    return null;
  }
  
  /// Calculate age in months for filtering purposes
  /// 
  /// Returns the age in months, or 0 if age cannot be determined
  static int calculateAgeInMonths(CattleIsar cattle) {
    final referenceDate = _getAgeReferenceDate(cattle);
    if (referenceDate == null) {
      return 0; // Unknown age defaults to 0 months for filtering
    }
    
    final now = DateTime.now();
    final ageInDays = now.difference(referenceDate).inDays;
    return (ageInDays / CattleAgeConstants.averageDaysPerMonth).round();
  }
  
  /// Calculate age as a human-readable display string
  /// 
  /// Returns formatted age string like "2y 3m", "5m", "New", or "Unknown age"
  static String calculateAgeDisplay(CattleIsar cattle) {
    final referenceDate = _getAgeReferenceDate(cattle);
    if (referenceDate == null) {
      return 'Unknown age';
    }
    
    final now = DateTime.now();
    final ageInDays = now.difference(referenceDate).inDays;
    
    // Handle negative ages (future dates)
    if (ageInDays < 0) {
      return 'Future date';
    }
    
    final years = (ageInDays / 365).floor();
    final months = ((ageInDays % 365) / 30).floor();
    
    if (years > 0) {
      return '${years}y ${months}m';
    } else if (months > 0) {
      return '${months}m';
    } else {
      return 'New';
    }
  }
  
  /// Get age category for the cattle
  ///
  /// Returns the age group category based on predefined ranges
  static String getAgeCategory(CattleIsar cattle) {
    final ageInMonths = calculateAgeInMonths(cattle);

    for (final range in ageRanges) {
      if (ageInMonths >= range.minMonths && ageInMonths <= range.maxMonths) {
        return range.label;
      }
    }

    return 'Unknown';
  }
  
  /// Check if cattle matches a specific age group filter
  ///
  /// [ageGroupFilter] should be one of: 'All' or any age range label
  static bool matchesAgeGroup(CattleIsar cattle, String ageGroupFilter) {
    if (ageGroupFilter == 'All') return true;

    return getAgeCategory(cattle) == ageGroupFilter;
  }

  /// Get all available age group labels for filtering
  ///
  /// Returns a list of all age range labels plus 'All'
  static List<String> getAvailableAgeGroups() {
    return ['All', ...ageRanges.map((range) => range.label)];
  }
}
