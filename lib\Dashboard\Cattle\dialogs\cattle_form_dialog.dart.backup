import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/cattle_isar.dart';
import '../../Farm Setup/models/breed_category_isar.dart';
import '../../Farm Setup/models/animal_type_isar.dart';
import 'package:get_it/get_it.dart';
import '../../Farm Setup/services/farm_setup_handler.dart';
import '../../../utils/message_utils.dart';
import '../../../constants/app_dialog_buttons.dart';
import '../../../constants/app_layout.dart';
import 'dart:math' as math;

// --- Constants ---
class _AppStrings {
  static const String addCattleTitle = 'Add Cattle';
  static const String editCattleTitle = 'Edit Cattle';
  static const String animalTypeLabel = 'Animal Type';
  static const String genderLabel = 'Gender';

  static const String selectAnimalTypeHint = 'Please select an animal type';
  static const String selectBreedHint = 'Please select a breed';
  static const String animalTypeRequired = 'Animal Type is required';
  static const String breedRequired = 'Breed is required';
  static const String genderRequired = 'Gender is required';
  static const String sourceRequired = 'Source is required';
  static const String dobRequired =
      'Date of Birth is required for cattle born at farm';
  static const String motherRequired =
      'Mother Tag ID is required for cattle born at farm';
  static const String purchaseDateRequired =
      'Purchase Date is required for purchased cattle';
  static const String purchasePriceRequired =
      'Purchase Price is required for purchased cattle';

  static const String sourceBorn = 'Born at Farm';
  static const String sourcePurchased = 'Purchased';
  static const String genderMale = 'Male';
  static const String genderFemale = 'Female';

  static const String tagIdRequired = 'Please enter a tag ID';
  static const String invalidTagIdFormat =
      'Tag ID must start with animal type first letter followed by numbers';
  static const String tagIdAlreadyInUse =
      'This tag ID is already in use by another animal';

  // Breed loading messages
  static const String noBreedsAvailable =
      'No breeds available for this animal type';
  static const String errorLoadingBreeds =
      'Error loading breeds. Please try again.';
}

class _AppPadding {
  static const double medium = 16.0;
}

class CattleFormDialog extends StatefulWidget {
  final CattleIsar? cattle;
  final List<AnimalTypeIsar> animalTypes;
  final List<CattleIsar> existingCattle;
  final Function(CattleIsar) onSave;
  final String businessId;

  const CattleFormDialog({
    Key? key,
    this.cattle,
    required this.animalTypes,
    required this.existingCattle,
    required this.onSave,
    required this.businessId,
  }) : super(key: key);

  @override
  State<CattleFormDialog> createState() => _CattleFormDialogState();
}

class _CattleFormDialogState extends State<CattleFormDialog> {
  final _formKey = GlobalKey<FormState>();

  // --- Controllers ---
  late TextEditingController _nameController;
  late TextEditingController _tagIdController;
  late TextEditingController _weightController;
  late TextEditingController _colorController;
  late TextEditingController _notesController;
  late TextEditingController _purchasePriceController;

  // --- State Variables ---
  String? _selectedGender;
  String? _selectedBreedId;
  String? _selectedAnimalTypeId;
  String? _selectedSource;
  String? _selectedMotherTagId;
  DateTime? _dateOfBirth;
  DateTime? _purchaseDate;
  bool _autoGenerateTagId = true;
// Store original tag ID for generation logic

  List<BreedCategoryIsar> _filteredBreeds = [];
  bool _isLoadingBreeds = false;
  String? _breedDropdownError; // To show loading/error state in dropdown area

  @override
  void initState() {
    super.initState();

    // Initialize controllers
    _nameController = TextEditingController();
    _tagIdController = TextEditingController();
    _weightController = TextEditingController();
    _colorController = TextEditingController();
    _notesController = TextEditingController();
    _purchasePriceController = TextEditingController();

    if (widget.cattle != null) {
      // --- Editing existing cattle ---
      final cattle = widget.cattle!;
      _nameController.text = cattle.name ?? '';
      _tagIdController.text = cattle.tagId ?? '';
// Store original
      _selectedGender = cattle.gender;

      // Ensure IDs are strings and valid
      String? initialAnimalTypeId = cattle.animalTypeId?.toString();
      bool animalTypeValid = widget.animalTypes
          .any((type) => type.businessId == initialAnimalTypeId);
      _selectedAnimalTypeId = animalTypeValid ? initialAnimalTypeId : null;

      // Initialize breed handling for editing
      String? initialBreedId = cattle.breedId?.toString();
      _selectedBreedId =
          initialBreedId; // Will be validated by _updateFilteredBreeds

      _dateOfBirth = cattle.dateOfBirth;
      _selectedSource = cattle.source;
      _purchaseDate = cattle.purchaseDate;
      _purchasePriceController.text = cattle.purchasePrice?.toString() ?? '';
      _weightController.text = cattle.weight?.toString() ?? '';
      _colorController.text = cattle.color ?? '';
      _notesController.text = cattle.notes ?? '';
      _selectedMotherTagId = cattle.motherTagId;

      _autoGenerateTagId = false; // Start with auto-generate off when editing

      // Load breeds for the initial animal type
      if (_selectedAnimalTypeId != null) {
        _updateFilteredBreeds(_selectedAnimalTypeId, initialBreedId);
      }
    } else {
      // --- Creating new cattle ---
      // Defaults are null/empty
      _autoGenerateTagId = true; // Start with auto-generate on for new cattle
    }
  }

  // --- Event Handlers ---
  void _onAnimalTypeChanged(String? value) {
    if (value == null) return;

    setState(() {
      _selectedAnimalTypeId = value;
      // Reset breed - will be loaded by _updateFilteredBreeds
      _selectedBreedId = null;

      if (_autoGenerateTagId) {
        // When changing animal type and auto-generate is on
        _tagIdController.text = '';
      }
    });

    // Load breeds for the new animal type
    _updateFilteredBreeds(value);
  }

  Future<void> _selectDate(BuildContext context, bool isDateOfBirth) async {
    final DateTime currentDate = DateTime.now();
    final DateTime initialDate = isDateOfBirth
        ? _dateOfBirth ?? currentDate
        : _purchaseDate ?? currentDate;

    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: DateTime(2000),
      lastDate: currentDate,
    );

    if (pickedDate != null) {
      setState(() {
        if (isDateOfBirth) {
          _dateOfBirth = pickedDate;
        } else {
          _purchaseDate = pickedDate;
        }
      });
    }
  }

  void _handleSave() {
    debugPrint('DEBUG: _handleSave called');

    if (!_formKey.currentState!.validate()) {
      debugPrint('DEBUG: Form validation failed');
      return;
    }

    try {
      // Validate required fields
      if (_selectedAnimalTypeId == null) {
        debugPrint('DEBUG: Animal type validation failed');
        throw Exception(_AppStrings.animalTypeRequired);
      }
      if (_selectedBreedId == null) {
        debugPrint('DEBUG: Breed validation failed');
        throw Exception(_AppStrings.breedRequired);
      }
      if (_selectedGender == null) {
        debugPrint('DEBUG: Gender validation failed');
        throw Exception(_AppStrings.genderRequired);
      }
      if (_selectedSource == null) {
        debugPrint('DEBUG: Source validation failed');
        throw Exception(_AppStrings.sourceRequired);
      }

      // Source-specific validation
      if (_selectedSource == _AppStrings.sourceBorn) {
        if (_dateOfBirth == null) {
          debugPrint('DEBUG: Date of birth validation failed');
          throw Exception(_AppStrings.dobRequired);
        }
        if (_selectedMotherTagId == null || _selectedMotherTagId!.isEmpty) {
          debugPrint('DEBUG: Mother tag ID validation failed');
          throw Exception(_AppStrings.motherRequired);
        }
      }

      if (_selectedSource == _AppStrings.sourcePurchased) {
        if (_purchaseDate == null) {
          debugPrint('DEBUG: Purchase date validation failed');
          throw Exception(_AppStrings.purchaseDateRequired);
        }
        if (_purchasePriceController.text.trim().isEmpty) {
          debugPrint('DEBUG: Purchase price validation failed');
          throw Exception(_AppStrings.purchasePriceRequired);
        }
      }

      // Generate new tag ID if auto-generate is on and field is empty
      if (_autoGenerateTagId && _tagIdController.text.trim().isEmpty) {
        _tagIdController.text = _generateTagId();
        debugPrint('DEBUG: Generated tag ID: ${_tagIdController.text}');
      }

      // Create the cattle object and call the save callback
      final cattle = _createCattleFromForm();

      // Log what we're about to save for debugging
      debugPrint(
          'DEBUG: Saving cattle: ${cattle.businessId}, name: ${cattle.name}, isEdit: ${widget.cattle != null}');

      // Make sure businessId is set for new cattle
      if (widget.cattle == null && (cattle.businessId == null || cattle.businessId!.isEmpty)) {
        debugPrint('DEBUG: Setting businessId for new cattle to ${widget.businessId}');
        cattle.businessId = widget.businessId;
      }

      // Call the save callback but don't navigate away
      widget.onSave(cattle);

      // Only close the dialog, not the entire navigation stack
      if (Navigator.canPop(context)) {
        debugPrint('DEBUG: Popping dialog only');
        Navigator.of(context).pop();
      }
    } catch (e) {
      debugPrint('ERROR: Failed to save cattle: $e');
      CattleMessageUtils.showError(context,
          'Error: ${e.toString().replaceAll('Exception: ', '')}');
    }
  }

  CattleIsar _createCattleFromForm() {
    // Create a new cattle object or update the existing one
    final isNewCattle = widget.cattle == null;

    // Create a new CattleIsar instance
    final cattle = CattleIsar();

    // If updating an existing record, copy the necessary data
    if (!isNewCattle && widget.cattle != null) {
      // Copy existing data
      cattle.id = widget.cattle!.id;
      cattle.businessId = widget.cattle!.businessId;
      cattle.createdAt = widget.cattle!.createdAt;
      cattle.status = widget.cattle!.status ?? 'Active';

      debugPrint(
          'DEBUG: Preserving IDs for update - id: ${widget.cattle!.id}, businessId: ${widget.cattle!.businessId}');
    }

    // Set the tag ID and other common fields
    cattle.tagId = _tagIdController.text.trim();
    cattle.name = _nameController.text.trim();
    cattle.gender = _selectedGender;
    cattle.animalTypeId = _selectedAnimalTypeId;
    cattle.breedId = _selectedBreedId;
    cattle.color = _colorController.text.trim();
    cattle.source = _selectedSource;

    // Set weight if provided
    final weightText = _weightController.text.trim();
    if (weightText.isNotEmpty) {
      cattle.weight = double.tryParse(weightText);
    }

    // Set source-specific fields
    if (_selectedSource == 'Born at Farm') {
      cattle.dateOfBirth = _dateOfBirth;
      cattle.motherTagId = _selectedMotherTagId;
    } else if (_selectedSource == 'Purchased') {
      cattle.purchaseDate = _purchaseDate;

      final purchasePrice = _purchasePriceController.text.trim();
      if (purchasePrice.isNotEmpty) {
        cattle.purchasePrice = double.tryParse(purchasePrice);
      }
    }

    cattle.notes = _notesController.text.trim();

    // Set business ID if new
    if (isNewCattle) {
      cattle.businessId = widget.businessId;
      cattle.createdAt = DateTime.now();
      cattle.status = 'Active'; // Set initial status for new cattle

      // Initialize necessary empty objects to avoid null errors
      cattle.breedingStatus = BreedingStatus();
      cattle.healthInfo = HealthInfo();
      cattle.productionInfo = ProductionInfo();

      debugPrint('DEBUG: Creating new cattle with businessId: ${cattle.businessId}');
    }

    // Always update the updated timestamp
    cattle.updatedAt = DateTime.now();

    // Add debug output to inspect the created cattle object
    debugPrint('DEBUG: Cattle created/updated:');
    debugPrint('  businessId: ${cattle.businessId}');
    debugPrint('  name: ${cattle.name}');
    debugPrint('  tagId: ${cattle.tagId}');
    debugPrint('  animalTypeId: ${cattle.animalTypeId}');
    debugPrint('  breedId: ${cattle.breedId}');
    debugPrint('  gender: ${cattle.gender}');
    debugPrint('  source: ${cattle.source}');

    return cattle;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _tagIdController.dispose();
    _weightController.dispose();
    _colorController.dispose();
    _notesController.dispose();
    _purchasePriceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return UniversalLayout.formDialog(
      context: context,
      title: widget.cattle == null
          ? _AppStrings.addCattleTitle
          : _AppStrings.editCattleTitle,
      headerIcon: widget.cattle == null ? Icons.add_circle : Icons.edit,
      formContent: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
                      // Animal Type Dropdown
                      DropdownButtonFormField<String>(
                        value: _selectedAnimalTypeId,
                        decoration: InputDecoration(
                          labelText: _AppStrings.animalTypeLabel,
                          border: const OutlineInputBorder(),
                          contentPadding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 16),
                          constraints: const BoxConstraints(
                            minHeight: 60,
                          ),
                          prefixIcon: Icon(
                            Icons.pets,
                            color: Colors.brown.shade400,
                          ),
                        ),
                        items: widget.animalTypes
                            .map<DropdownMenuItem<String>>((type) {
                          return DropdownMenuItem(
                            value: type.businessId,
                            child: Text(type.name ?? 'Unknown Type'),
                          );
                        }).toList(),
                        onChanged: _onAnimalTypeChanged,
                        validator: (value) => value == null || value.isEmpty
                            ? _AppStrings.selectAnimalTypeHint
                            : null,
                      ),
                      const SizedBox(height: _AppPadding.medium),

                      // Breed Dropdown - only show when animal type is selected
                      if (_selectedAnimalTypeId != null)
                        _isLoadingBreeds
                            ? const Center(
                                child: Padding(
                                  padding: EdgeInsets.symmetric(vertical: 8),
                                  child: CircularProgressIndicator(),
                                ),
                              )
                            : _breedDropdownError != null
                                ? Container(
                                    padding: const EdgeInsets.all(10),
                                    decoration: BoxDecoration(
                                        color: Colors.red.shade50,
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border.all(
                                            color: Colors.red.shade200)),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        const Row(
                                          children: [
                                            Icon(Icons.error_outline,
                                                color: Colors.red),
                                            SizedBox(width: 8),
                                            Text(
                                              'No breeds available',
                                              style: TextStyle(
                                                  color: Colors.red,
                                                  fontWeight: FontWeight.bold,
                                                  fontSize: 16),
                                            ),
                                          ],
                                        ),
                                        const SizedBox(height: 8),
                                        Text(
                                          'There are no breeds defined for this animal type yet. Please select a different animal type or add a new breed.',
                                          style: TextStyle(
                                              color: Colors.red.shade800),
                                        ),
                                        const SizedBox(height: 12),
                                        UniversalDialogButtons.clear(
                                          onPressed: () {
                                            // Show dialog/navigation to add a new breed
                                            ScaffoldMessenger.of(context)
                                                .showSnackBar(
                                              const SnackBar(
                                                content: Text(
                                                    'To add a new breed, please go to Farm Setup > Breeds'),
                                              ),
                                            );
                                          },
                                          text: 'Add Breed for This Animal Type',
                                        ),
                                      ],
                                    ),
                                  )
                                : DropdownButtonFormField<String>(
                                    value: _selectedBreedId,
                                    decoration: const InputDecoration(
                                      labelText: 'Breed',
                                      border: OutlineInputBorder(),
                                      contentPadding: EdgeInsets.symmetric(
                                          horizontal: 12, vertical: 16),
                                      constraints: BoxConstraints(
                                        minHeight: 60,
                                      ),
                                      prefixIcon: Icon(
                                        Icons.category,
                                        color: Colors.deepPurple,
                                      ),
                                    ),
                                    items: _filteredBreeds
                                        .map<DropdownMenuItem<String>>((breed) {
                                      return DropdownMenuItem(
                                        value: breed.businessId,
                                        child:
                                            Text(breed.name ?? 'Unknown Breed'),
                                      );
                                    }).toList(),
                                    onChanged: (value) {
                                      setState(() {
                                        _selectedBreedId = value;
                                      });
                                    },
                                    validator: (value) =>
                                        value == null || value.isEmpty
                                            ? _AppStrings.selectBreedHint
                                            : null,
                                  ),
                      if (_selectedAnimalTypeId != null)
                        const SizedBox(height: _AppPadding.medium),

                      // Name Field
                      TextFormField(
                        controller: _nameController,
                        decoration: const InputDecoration(
                          labelText: 'Name',
                          border: OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(
                              horizontal: 12, vertical: 16),
                          constraints: BoxConstraints(
                            minHeight: 60,
                          ),
                          prefixIcon: Icon(
                            Icons.label,
                            color: Colors.blue,
                          ),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter a name';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Tag ID Field with Auto-generate Toggle
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: _tagIdController,
                              enabled: !_autoGenerateTagId,
                              decoration: InputDecoration(
                                labelText: 'Tag ID',
                                border: const OutlineInputBorder(),
                                helperText: !_autoGenerateTagId
                                    ? 'Format: ${_getAnimalTypePrefix()}N (N = number)'
                                    : null,
                                contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 16),
                                constraints: const BoxConstraints(
                                  minHeight: 60,
                                ),
                                prefixIcon: const Icon(
                                  Icons.tag,
                                  color: Colors.orange,
                                ),
                              ),
                              validator: (value) => validateTagId(value),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Text('Auto',
                                  style: TextStyle(fontSize: 14)),
                              Switch(
                                value: _autoGenerateTagId,
                                activeColor: Colors.white,
                                activeTrackColor: const Color(0xFF2E7D32),
                                inactiveTrackColor: Colors.grey.shade300,
                                inactiveThumbColor: Colors.grey.shade50,
                                onChanged: (value) {
                                  setState(() {
                                    _autoGenerateTagId = value;
                                    if (value) {
                                      _tagIdController.text = '';
                                    }
                                  });
                                },
                              ),
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // Gender Field
                      DropdownButtonFormField<String>(
                        value: _selectedGender,
                        decoration: const InputDecoration(
                          labelText: _AppStrings.genderLabel,
                          border: OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(
                              horizontal: 12, vertical: 16),
                          constraints: BoxConstraints(
                            minHeight: 60,
                          ),
                          prefixIcon: Icon(
                            Icons.person,
                            color: Colors.purple,
                          ),
                        ),
                        items:
                            [_AppStrings.genderMale, _AppStrings.genderFemale]
                                .map<DropdownMenuItem<String>>(
                                  (gender) => DropdownMenuItem(
                                    value: gender,
                                    child: Text(gender),
                                  ),
                                )
                                .toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedGender = value;
                          });
                        },
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please select a gender';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Source Field
                      DropdownButtonFormField<String>(
                        value: _selectedSource,
                        decoration: const InputDecoration(
                          labelText: 'Source',
                          border: OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(
                              horizontal: 12, vertical: 16),
                          constraints: BoxConstraints(
                            minHeight: 60,
                          ),
                          prefixIcon: Icon(
                            Icons.source,
                            color: Colors.teal,
                          ),
                        ),
                        items: const [
                          DropdownMenuItem(
                              value: 'Purchased', child: Text('Purchased')),
                          DropdownMenuItem(
                              value: 'Born at Farm',
                              child: Text('Born at Farm')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            if (_selectedSource == 'Born at Farm' &&
                                value != 'Born at Farm') {
                              _selectedMotherTagId = null;
                            }
                            _selectedSource = value;
                            if (value != 'Purchased') {
                              _purchaseDate = null;
                              _purchasePriceController.clear();
                            }
                            if (value != 'Born at Farm') {
                              _dateOfBirth = null;
                            }
                          });
                        },
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please select a source';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Source specific fields
                      if (_selectedSource == 'Born at Farm') ...[
                        // Date of Birth
                        TextFormField(
                          readOnly: true,
                          onTap: () async {
                            await _selectDate(context, true);
                          },
                          controller: TextEditingController(
                            text: _dateOfBirth != null
                                ? DateFormat('yyyy-MM-dd').format(_dateOfBirth!)
                                : '',
                          ),
                          decoration: const InputDecoration(
                            labelText: 'Date of Birth *',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 12, vertical: 16),
                            constraints: BoxConstraints(
                              minHeight: 60,
                            ),
                            prefixIcon: Icon(
                              Icons.calendar_today,
                              color: Colors.blue,
                            ),
                          ),
                          validator: (value) {
                            if (_selectedSource == 'Born at Farm' &&
                                _dateOfBirth == null) {
                              return 'Date of Birth is required';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),

                        // Mother Tag ID
                        if (_selectedAnimalTypeId != null)
                          DropdownButtonFormField<String>(
                            value: _selectedMotherTagId,
                            decoration: const InputDecoration(
                              labelText: 'Mother Tag ID *',
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 16),
                              constraints: BoxConstraints(
                                minHeight: 60,
                              ),
                              prefixIcon: Icon(
                                Icons.family_restroom,
                                color: Colors.purple,
                              ),
                            ),
                            items: widget.existingCattle
                                .where((cattle) =>
                                    cattle.gender == 'Female' &&
                                    cattle.animalTypeId ==
                                        _selectedAnimalTypeId)
                                .map<DropdownMenuItem<String>>((cattle) {
                              return DropdownMenuItem(
                                value: cattle.tagId,
                                child: Text(
                                    '${cattle.name ?? "Unknown"} (${cattle.tagId ?? "No Tag"})'),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedMotherTagId = value;
                              });
                            },
                            validator: (value) {
                              if (_selectedSource == 'Born at Farm' &&
                                  (value == null || value.isEmpty)) {
                                return 'Mother Tag ID is required';
                              }
                              return null;
                            },
                          ),
                      ] else if (_selectedSource == 'Purchased') ...[
                        // Purchase Date
                        TextFormField(
                          readOnly: true,
                          onTap: () async {
                            await _selectDate(context, false);
                          },
                          controller: TextEditingController(
                            text: _purchaseDate != null
                                ? DateFormat('yyyy-MM-dd')
                                    .format(_purchaseDate!)
                                : '',
                          ),
                          decoration: const InputDecoration(
                            labelText: 'Purchase Date *',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 12, vertical: 16),
                            constraints: BoxConstraints(
                              minHeight: 60,
                            ),
                            prefixIcon: Icon(
                              Icons.calendar_today,
                              color: Colors.blue,
                            ),
                          ),
                          validator: (value) {
                            if (_selectedSource == 'Purchased' &&
                                _purchaseDate == null) {
                              return 'Purchase Date is required';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),

                        // Purchase Price
                        TextFormField(
                          controller: _purchasePriceController,
                          keyboardType: TextInputType.number,
                          decoration: const InputDecoration(
                            labelText: 'Purchase Price *',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 12, vertical: 16),
                            constraints: BoxConstraints(
                              minHeight: 60,
                            ),
                            prefixIcon: Icon(
                              Icons.attach_money,
                              color: Colors.green,
                            ),
                          ),
                          validator: (value) {
                            if (_selectedSource == 'Purchased' &&
                                (value == null || value.trim().isEmpty)) {
                              return 'Purchase Price is required';
                            }
                            if (value != null &&
                                value.isNotEmpty &&
                                double.tryParse(value) == null) {
                              return 'Please enter a valid number';
                            }
                            return null;
                          },
                        ),
                      ],

                      const SizedBox(height: 24),
                      const Divider(height: 1, thickness: 1),
                      const SizedBox(height: 24),

                      // Optional Information section
                      const Text(
                        'Optional Information',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF2E7D32),
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Weight Field
                      TextFormField(
                        controller: _weightController,
                        keyboardType: const TextInputType.numberWithOptions(
                            decimal: true),
                        decoration: const InputDecoration(
                          labelText: 'Weight (kg)',
                          border: OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(
                              horizontal: 12, vertical: 16),
                          constraints: BoxConstraints(
                            minHeight: 60,
                          ),
                          prefixIcon: Icon(
                            Icons.monitor_weight,
                            color: Colors.amber,
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Color Field
                      TextFormField(
                        controller: _colorController,
                        decoration: const InputDecoration(
                          labelText: 'Color',
                          border: OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(
                              horizontal: 12, vertical: 16),
                          constraints: BoxConstraints(
                            minHeight: 60,
                          ),
                          prefixIcon: Icon(
                            Icons.color_lens,
                            color: Colors.indigo,
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Notes Field
                      TextFormField(
                        controller: _notesController,
                        maxLines: 3,
                        decoration: const InputDecoration(
                          labelText: 'Notes',
                          border: OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(
                              horizontal: 12, vertical: 12),
                          prefixIcon: Icon(
                            Icons.notes,
                            color: Colors.grey,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
        ),
      ),
      actionButtons: widget.cattle == null
          ? UniversalDialogButtons.cancelAddRow(
              onCancel: () => Navigator.of(context).pop(),
              onAdd: _handleSave,
              cancelText: 'Cancel',
              addText: 'Add',
              isAdding: false,
            )
          : UniversalDialogButtons.cancelUpdateRow(
              onCancel: () => Navigator.of(context).pop(),
              onUpdate: _handleSave,
              cancelText: 'Cancel',
              updateText: 'Update',
              isUpdating: false,
            ),
    );
  }

  // --- Breed Loading Logic ---
  // This is the single source of truth for breed loading based on animal type
  Future<void> _updateFilteredBreeds(String? animalTypeId,
      [String? initialBreedId]) async {
    if (animalTypeId == null) {
      if (mounted) {
        setState(() {
          _filteredBreeds = [];
          _selectedBreedId = null;
          _isLoadingBreeds = false;
          _breedDropdownError = null;
        });
      }
      return;
    }

    if (mounted) {
      setState(() {
        _isLoadingBreeds = true;
        _breedDropdownError = null; // Clear previous errors
        _filteredBreeds = []; // Clear previous breeds
      });
    }

    try {
      // Use handler to get all breeds
      final farmSetupHandler = GetIt.instance<FarmSetupHandler>();
      final allBreeds = await farmSetupHandler.getAllBreedCategories();
      final filtered = allBreeds
          .where((breed) => breed.animalTypeId == animalTypeId)
          .toList();

      if (mounted) {
        // Check if initialBreedId is valid within the filtered list
        String? validInitialBreedId = initialBreedId;
        if (initialBreedId != null &&
            !filtered.any((b) => b.businessId == initialBreedId)) {
          debugPrint(
              "Initial breed ID $initialBreedId not found in filtered list for type $animalTypeId. Resetting.");
          validInitialBreedId = null; // Reset if not valid
        }

        setState(() {
          _filteredBreeds = filtered;
          _isLoadingBreeds = false;

          _selectedBreedId ??= validInitialBreedId;

          if (filtered.isEmpty) {
            _breedDropdownError = _AppStrings.noBreedsAvailable;
          }
        });
      }
    } catch (e) {
      debugPrint('Error loading breeds for type $animalTypeId: $e');
      if (mounted) {
        setState(() {
          _isLoadingBreeds = false;
          _filteredBreeds = [];
          _selectedBreedId = null;
          _breedDropdownError = _AppStrings.errorLoadingBreeds;
        });
      }
    }
  }

  // --- Tag ID Logic ---
  bool _isTagIdInUse(String tagId) {
    // Compares against existing cattle, excluding the one being edited
    return widget.existingCattle.any((c) =>
        c.tagId == tagId &&
        (widget.cattle == null || c.businessId != widget.cattle!.businessId));
  }

  String _generateTagId() {
    if (_selectedAnimalTypeId == null) {
      return ''; // Should not happen if validation passes
    }

    final animalType = widget.animalTypes.firstWhere(
      (type) => type.businessId == _selectedAnimalTypeId,
      orElse: () => AnimalTypeIsar(),
    );
    final prefix = animalType.name?.substring(0, 1).toUpperCase() ?? 'X';

    // Find max number among existing tags with this prefix
    int maxNumber = 0;
    final regExp = RegExp('^$prefix(\\d+)\$');

    for (final cattle in widget.existingCattle) {
      final tagId = cattle.tagId;
      if (tagId != null && regExp.hasMatch(tagId)) {
        final match = regExp.firstMatch(tagId);
        if (match != null && match.groupCount >= 1) {
          final number = int.tryParse(match.group(1) ?? '0') ?? 0;
          maxNumber = math.max(maxNumber, number);
        }
      }
    }

    // Default approach - simply increment the maximum tag number found
    return '$prefix${maxNumber + 1}';
  }

  bool _isValidTagIdFormat(String tagId) {
    if (_selectedAnimalTypeId == null) return false;

    final animalType = widget.animalTypes.firstWhere(
      (type) => type.businessId == _selectedAnimalTypeId,
      orElse: () => AnimalTypeIsar(),
    );
    final prefix = animalType.name?.substring(0, 1).toUpperCase() ?? 'X';

    // Format should be: prefix followed by numbers
    final RegExp tagIdRegex = RegExp('^$prefix\\d+\$');
    return tagIdRegex.hasMatch(tagId);
  }

  String _getAnimalTypePrefix() {
    if (_selectedAnimalTypeId == null) return 'X';

    final animalType = widget.animalTypes.firstWhere(
        (type) => type.businessId == _selectedAnimalTypeId,
        orElse: () => AnimalTypeIsar());

    return animalType.name?.substring(0, 1).toUpperCase() ?? 'X';
  }

  // Validates the tag ID format and uniqueness
  String? validateTagId(String? value) {
    if (!_autoGenerateTagId) {
      // Only validate if not auto-generating
      if (value == null || value.trim().isEmpty) {
        return _AppStrings.tagIdRequired;
      }

      // Check format
      if (!_isValidTagIdFormat(value)) {
        return _AppStrings.invalidTagIdFormat;
      }

      // Check for duplicates (only when editing or custom tag ID)
      if (_isTagIdInUse(value)) {
        return _AppStrings.tagIdAlreadyInUse;
      }
    }
    return null;
  }
}


