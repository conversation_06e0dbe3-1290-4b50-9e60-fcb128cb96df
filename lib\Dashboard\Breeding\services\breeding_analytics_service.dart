import '../models/breeding_record_isar.dart';
import '../models/pregnancy_record_isar.dart';
import '../models/breeding_event_isar.dart';
import '../models/delivery_record_isar.dart';
import '../../Cattle/models/cattle_isar.dart';

/// Data class to hold all breeding analytics results
class BreedingAnalyticsResult {
  // KPI Metrics
  final int totalBreedingRecords;
  final int totalPregnancies;
  final int totalDeliveries;
  final int activePregnancies;
  final int completedBreedings;
  final int failedBreedings;
  final double conceptionRate;
  final double calvingRate;

  // Distributions
  final Map<String, int> breedingMethodDistribution;
  final Map<String, int> breedingStatusDistribution;
  final Map<String, int> pregnancyStatusDistribution;
  final Map<String, int> deliveryTypeDistribution;

  // Performance metrics
  final double averageCalvingInterval;
  final double averageGestationPeriod;
  final int totalCalvesBorn;
  final double averageCalvesPerDelivery;

  // Financial metrics
  final double totalBreedingCosts;
  final double averageBreedingCost;

  // Insights
  final int overduePregnancies;
  final int upcomingCalvings;
  final List<String> topPerformingBulls;

  const BreedingAnalyticsResult({
    required this.totalBreedingRecords,
    required this.totalPregnancies,
    required this.totalDeliveries,
    required this.activePregnancies,
    required this.completedBreedings,
    required this.failedBreedings,
    required this.conceptionRate,
    required this.calvingRate,
    required this.breedingMethodDistribution,
    required this.breedingStatusDistribution,
    required this.pregnancyStatusDistribution,
    required this.deliveryTypeDistribution,
    required this.averageCalvingInterval,
    required this.averageGestationPeriod,
    required this.totalCalvesBorn,
    required this.averageCalvesPerDelivery,
    required this.totalBreedingCosts,
    required this.averageBreedingCost,
    required this.overduePregnancies,
    required this.upcomingCalvings,
    required this.topPerformingBulls,
  });

  /// Empty result for when there's no data
  static const empty = BreedingAnalyticsResult(
    totalBreedingRecords: 0,
    totalPregnancies: 0,
    totalDeliveries: 0,
    activePregnancies: 0,
    completedBreedings: 0,
    failedBreedings: 0,
    conceptionRate: 0.0,
    calvingRate: 0.0,
    breedingMethodDistribution: {},
    breedingStatusDistribution: {},
    pregnancyStatusDistribution: {},
    deliveryTypeDistribution: {},
    averageCalvingInterval: 0.0,
    averageGestationPeriod: 0.0,
    totalCalvesBorn: 0,
    averageCalvesPerDelivery: 0.0,
    totalBreedingCosts: 0.0,
    averageBreedingCost: 0.0,
    overduePregnancies: 0,
    upcomingCalvings: 0,
    topPerformingBulls: [],
  );
}

/// Pure analytics service for breeding calculations - no state, just calculations
/// Following the cattle module pattern with single-pass data processing
class BreedingAnalyticsService {

  /// Main calculation method - single entry point with O(n) efficiency
  static BreedingAnalyticsResult calculate(
    List<BreedingRecordIsar> breedingRecords,
    List<PregnancyRecordIsar> pregnancyRecords,
    List<DeliveryRecordIsar> deliveryRecords,
    List<BreedingEventIsar> breedingEvents,
    List<CattleIsar> cattle,
  ) {
    if (breedingRecords.isEmpty && pregnancyRecords.isEmpty && deliveryRecords.isEmpty) {
      return BreedingAnalyticsResult.empty;
    }

    // Single pass through the data for maximum efficiency
    final accumulator = _BreedingAnalyticsAccumulator();

    // Process all breeding data types
    for (final record in breedingRecords) {
      accumulator.processBreedingRecord(record);
    }

    for (final pregnancy in pregnancyRecords) {
      accumulator.processPregnancyRecord(pregnancy);
    }

    for (final delivery in deliveryRecords) {
      accumulator.processDeliveryRecord(delivery);
    }

    // Calculate derived metrics
    accumulator.calculateDerivedMetrics(cattle);

    return accumulator.toResult();
  }

  /// Calculate conception rate for a specific cattle
  static double calculateCattleConceptionRate(
    String cattleId,
    List<BreedingRecordIsar> breedingRecords,
    List<PregnancyRecordIsar> pregnancyRecords,
  ) {
    final cattleBreedings = breedingRecords.where((r) => r.cattleId == cattleId).length;
    final cattlePregnancies = pregnancyRecords.where((p) => p.cattleId == cattleId).length;

    if (cattleBreedings == 0) return 0.0;
    return (cattlePregnancies / cattleBreedings * 100).clamp(0.0, 100.0);
  }

  /// Calculate average calving interval for a specific cattle
  static double calculateCattleCalvingInterval(
    String cattleId,
    List<DeliveryRecordIsar> deliveryRecords,
  ) {
    final cattleDeliveries = deliveryRecords
        .where((d) => d.cattleId == cattleId && d.deliveryDate != null)
        .toList();

    if (cattleDeliveries.length < 2) return 0.0;

    // Sort by delivery date
    cattleDeliveries.sort((a, b) => a.deliveryDate!.compareTo(b.deliveryDate!));

    double totalDays = 0.0;
    int intervals = 0;

    for (int i = 1; i < cattleDeliveries.length; i++) {
      final interval = cattleDeliveries[i].deliveryDate!
          .difference(cattleDeliveries[i - 1].deliveryDate!)
          .inDays;
      totalDays += interval;
      intervals++;
    }

    return intervals > 0 ? totalDays / intervals : 0.0;
  }

  /// Identify top performing bulls based on conception rates
  /// Refactored to reuse helper method and eliminate duplication
  static List<String> identifyTopPerformingBulls(
    List<BreedingRecordIsar> breedingRecords,
    List<PregnancyRecordIsar> pregnancyRecords,
    {int limit = 5}
  ) {
    final bullAttempts = <String, int>{};
    final bullSuccesses = <String, int>{};

    // Count breeding attempts per bull
    for (final breeding in breedingRecords) {
      final bull = breeding.bullIdOrType ?? 'Unknown';
      bullAttempts[bull] = (bullAttempts[bull] ?? 0) + 1;
    }

    // Count successful pregnancies per bull
    for (final pregnancy in pregnancyRecords) {
      final relatedBreeding = breedingRecords
          .where((b) => b.businessId == pregnancy.breedingRecordId)
          .firstOrNull;

      if (relatedBreeding != null) {
        final bull = relatedBreeding.bullIdOrType ?? 'Unknown';
        bullSuccesses[bull] = (bullSuccesses[bull] ?? 0) + 1;
      }
    }

    // Reuse the helper method for calculation
    return _calculateTopPerformingBullsFromMaps(bullAttempts, bullSuccesses, limit);
  }
}

/// Efficient single-pass accumulator for all breeding analytics calculations
class _BreedingAnalyticsAccumulator {
  // Constants for calculations
  static const int _standardGestationDays = 283; // Average cattle gestation period
  static const int _overdueThresholdDays = 10; // Days past expected calving to consider overdue
  static const int _upcomingCalvingDays = 30; // Days ahead to consider upcoming calvings

  // Basic counts
  int totalBreedingRecords = 0;
  int totalPregnancies = 0;
  int totalDeliveries = 0;
  int activePregnancies = 0;
  int completedBreedings = 0;
  int failedBreedings = 0;

  // Financial metrics
  double totalBreedingCosts = 0.0;
  int recordsWithCost = 0;

  // Distributions
  final Map<String, int> breedingMethodDistribution = {};
  final Map<String, int> breedingStatusDistribution = {};
  final Map<String, int> pregnancyStatusDistribution = {};
  final Map<String, int> deliveryTypeDistribution = {};

  // Performance tracking
  final List<double> gestationPeriods = [];
  final List<double> calvingIntervals = [];
  int totalCalvesBorn = 0;
  final List<int> calvesPerDelivery = [];

  // Insights tracking
  int overduePregnancies = 0;
  int upcomingCalvings = 0;
  final Map<String, int> bullAttempts = {};
  final Map<String, int> bullSuccesses = {};

  /// Process a single breeding record
  void processBreedingRecord(BreedingRecordIsar record) {
    totalBreedingRecords++;

    // Status processing
    final status = record.status?.toLowerCase() ?? 'unknown';
    breedingStatusDistribution[status] = (breedingStatusDistribution[status] ?? 0) + 1;

    if (status == 'completed' || status == 'confirmed') {
      completedBreedings++;
    } else if (status == 'failed') {
      failedBreedings++;
    }

    // Method processing
    final method = record.method ?? 'Unknown';
    breedingMethodDistribution[method] = (breedingMethodDistribution[method] ?? 0) + 1;

    // Financial processing
    final cost = record.cost ?? 0.0;
    if (cost > 0) {
      totalBreedingCosts += cost;
      recordsWithCost++;
    }

    // Bull performance tracking
    final bull = record.bullIdOrType ?? 'Unknown';
    bullAttempts[bull] = (bullAttempts[bull] ?? 0) + 1;
  }

  /// Process a single pregnancy record
  void processPregnancyRecord(PregnancyRecordIsar record) {
    totalPregnancies++;

    // Status processing
    final status = record.status?.toLowerCase() ?? 'unknown';
    pregnancyStatusDistribution[status] = (pregnancyStatusDistribution[status] ?? 0) + 1;

    if (status == 'active') {
      activePregnancies++;

      // Check for overdue pregnancies
      if (record.expectedCalvingDate != null) {
        final daysOverdue = DateTime.now().difference(record.expectedCalvingDate!).inDays;
        if (daysOverdue > _overdueThresholdDays) {
          overduePregnancies++;
        } else if (daysOverdue > -_upcomingCalvingDays && daysOverdue <= 0) {
          upcomingCalvings++;
        }
      }
    }

    // Calculate gestation period if completed
    if (record.startDate != null && record.actualCalvingDate != null) {
      final gestationDays = record.actualCalvingDate!.difference(record.startDate!).inDays;
      gestationPeriods.add(gestationDays.toDouble());
    }

    // Track bull success
    if (status == 'completed') {
      // This would require linking back to breeding record to get bull info
      // For now, we'll handle this in the main calculate method
    }
  }

  /// Process a single delivery record
  void processDeliveryRecord(DeliveryRecordIsar record) {
    totalDeliveries++;

    // Delivery type processing
    final deliveryType = record.deliveryType ?? 'Normal';
    deliveryTypeDistribution[deliveryType] = (deliveryTypeDistribution[deliveryType] ?? 0) + 1;

    // Calf count processing
    final calfCount = record.calfCount;
    totalCalvesBorn += calfCount;
    calvesPerDelivery.add(calfCount);
  }

  /// Calculate derived metrics
  void calculateDerivedMetrics(List<CattleIsar> cattle) {
    // Calculate calving intervals for each cattle
    final cattleDeliveries = <String, List<DateTime>>{};
    
    // This would require access to delivery records grouped by cattle
    // For now, we'll use placeholder logic
  }

  /// Convert accumulated data to immutable result
  BreedingAnalyticsResult toResult() {
    final conceptionRate = totalBreedingRecords > 0 
        ? (totalPregnancies / totalBreedingRecords * 100).clamp(0.0, 100.0)
        : 0.0;

    final calvingRate = totalPregnancies > 0 
        ? (totalDeliveries / totalPregnancies * 100).clamp(0.0, 100.0)
        : 0.0;

    final averageGestationPeriod = gestationPeriods.isNotEmpty
        ? gestationPeriods.reduce((a, b) => a + b) / gestationPeriods.length
        : _standardGestationDays.toDouble();

    final averageCalvingInterval = calvingIntervals.isNotEmpty
        ? calvingIntervals.reduce((a, b) => a + b) / calvingIntervals.length
        : 0.0;

    final averageCalvesPerDelivery = calvesPerDelivery.isNotEmpty
        ? calvesPerDelivery.reduce((a, b) => a + b) / calvesPerDelivery.length
        : 0.0;

    final averageBreedingCost = recordsWithCost > 0 
        ? totalBreedingCosts / recordsWithCost 
        : 0.0;

    // Calculate top performing bulls
    final topPerformingBulls = _calculateTopPerformingBulls();

    return BreedingAnalyticsResult(
      totalBreedingRecords: totalBreedingRecords,
      totalPregnancies: totalPregnancies,
      totalDeliveries: totalDeliveries,
      activePregnancies: activePregnancies,
      completedBreedings: completedBreedings,
      failedBreedings: failedBreedings,
      conceptionRate: conceptionRate,
      calvingRate: calvingRate,
      breedingMethodDistribution: Map.unmodifiable(breedingMethodDistribution),
      breedingStatusDistribution: Map.unmodifiable(breedingStatusDistribution),
      pregnancyStatusDistribution: Map.unmodifiable(pregnancyStatusDistribution),
      deliveryTypeDistribution: Map.unmodifiable(deliveryTypeDistribution),
      averageCalvingInterval: averageCalvingInterval,
      averageGestationPeriod: averageGestationPeriod,
      totalCalvesBorn: totalCalvesBorn,
      averageCalvesPerDelivery: averageCalvesPerDelivery,
      totalBreedingCosts: totalBreedingCosts,
      averageBreedingCost: averageBreedingCost,
      overduePregnancies: overduePregnancies,
      upcomingCalvings: upcomingCalvings,
      topPerformingBulls: topPerformingBulls,
    );
  }

  /// Calculate top performing bulls based on success rates
  /// Reuses the static helper method to eliminate duplication
  List<String> _calculateTopPerformingBulls() {
    return _calculateTopPerformingBullsFromMaps(bullAttempts, bullSuccesses, 5);
  }

  /// Extract top performing bulls calculation - reusable helper
  static List<String> _calculateTopPerformingBullsFromMaps(
    Map<String, int> attempts,
    Map<String, int> successes,
    int limit,
  ) {
    final bullRates = <String, double>{};

    for (final bull in attempts.keys) {
      final attemptCount = attempts[bull] ?? 0;
      final successCount = successes[bull] ?? 0;

      if (attemptCount >= 2) { // Minimum 2 attempts to be considered
        bullRates[bull] = attemptCount > 0 ? successCount / attemptCount : 0.0;
      }
    }

    final sortedBulls = bullRates.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return sortedBulls.take(limit).map((entry) => entry.key).toList();
  }
}
