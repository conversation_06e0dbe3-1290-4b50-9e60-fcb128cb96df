import 'package:isar/isar.dart';

part 'vaccination_record_isar.g.dart';

@collection
class VaccinationIsar {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  String? recordId;

  @Index()
  String? cattleId;

  @Index()
  DateTime? date;

  String? vaccineName;

  String? manufacturer;

  String? batchNumber;

  String? status;

  double? cost;

  String? notes;

  DateTime? nextDueDate;

  DateTime? createdAt;

  DateTime? updatedAt;

  // Backward compatibility getters and setters
  String? get businessId => recordId;
  set businessId(String? value) => recordId = value;

  String? get cattleBusinessId => cattleId;
  set cattleBusinessId(String? value) => cattleId = value;

  String? get name => vaccineName;
  set name(String? value) => vaccineName = value;

  VaccinationIsar();

  /// Generate a deterministic record ID for vaccination records to ensure consistency
  /// across app reinstallations. Based on cattle ID, date, and vaccine name.
  static String generateRecordId(
      String cattleId, DateTime date, String vaccineName) {
    // Format the date in a consistent way (YYYYMMDD)
    final dateStr =
        "${date.year}${date.month.toString().padLeft(2, '0')}${date.day.toString().padLeft(2, '0')}";

    // Normalize vaccine name (lowercase, remove spaces)
    final normalizedVaccine = vaccineName.toLowerCase().replaceAll(' ', '_');

    // Create a unique ID combining cattle ID, date and vaccine name
    final uniqueKey = "$cattleId-$dateStr-$normalizedVaccine";

    // Return with a prefix to distinguish vaccination records
    return "vax_$uniqueKey";
  }

  factory VaccinationIsar.create({
    String? recordId,
    required String cattleId,
    required DateTime date,
    required String vaccineName,
    required String manufacturer,
    required String batchNumber,
    String status = 'Completed',
    double? cost,
    String? notes,
    DateTime? nextDueDate,
  }) {
    return VaccinationIsar()
      ..recordId = recordId ?? generateRecordId(cattleId, date, vaccineName)
      ..cattleId = cattleId
      ..date = date
      ..vaccineName = vaccineName
      ..manufacturer = manufacturer
      ..batchNumber = batchNumber
      ..status = status
      ..cost = cost
      ..notes = notes
      ..nextDueDate = nextDueDate
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();
  }

  // Factory method for backward compatibility with Cattle model
  factory VaccinationIsar.fromCattleModel({
    required String cattleBusinessId,
    required String name,
    required String batchNumber,
    required String manufacturer,
    required double cost,
    required DateTime date,
    String notes = '',
  }) {
    return VaccinationIsar()
      ..recordId = generateRecordId(cattleBusinessId, date, name)
      ..cattleId = cattleBusinessId
      ..date = date
      ..vaccineName = name
      ..manufacturer = manufacturer
      ..batchNumber = batchNumber
      ..cost = cost
      ..notes = notes
      ..status = 'Completed'
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();
  }

  factory VaccinationIsar.fromMap(Map<String, dynamic> map) {
    return VaccinationIsar()
      ..recordId = map['id'] as String? ??
          map['recordId'] as String? ??
          map['businessId'] as String?
      ..cattleId =
          map['cattleId'] as String? ?? map['cattleBusinessId'] as String?
      ..date = map['date'] != null || map['administeredDate'] != null
          ? DateTime.parse((map['date'] ?? map['administeredDate']).toString())
          : null
      ..vaccineName = map['vaccineName'] as String? ?? map['name'] as String?
      ..manufacturer = map['manufacturer'] as String?
      ..batchNumber = map['batchNumber'] as String?
      ..status = map['status'] as String?
      ..cost = map['cost'] != null ? double.parse(map['cost'].toString()) : null
      ..notes = map['notes'] as String?
      ..nextDueDate = map['nextDueDate'] != null
          ? DateTime.parse(map['nextDueDate'].toString())
          : null
      ..createdAt = map['createdAt'] != null
          ? DateTime.parse(map['createdAt'].toString())
          : DateTime.now()
      ..updatedAt = map['updatedAt'] != null
          ? DateTime.parse(map['updatedAt'].toString())
          : DateTime.now();
  }

  Map<String, dynamic> toMap() {
    return {
      'id': recordId,
      'cattleId': cattleId,
      'date': date?.toIso8601String(),
      'vaccineName': vaccineName,
      'manufacturer': manufacturer,
      'batchNumber': batchNumber,
      'status': status,
      'cost': cost,
      'notes': notes,
      'nextDueDate': nextDueDate?.toIso8601String(),
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  // For backward compatibility with Cattle model
  Map<String, dynamic> toCattleMap() {
    return {
      'id': recordId,
      'cattleBusinessId': cattleId,
      'name': vaccineName,
      'batchNumber': batchNumber,
      'manufacturer': manufacturer,
      'cost': cost,
      'notes': notes,
      'date': date?.toIso8601String(),
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  Map<String, dynamic> toJson() => toMap();

  VaccinationIsar copyWith({
    String? recordId,
    String? cattleId,
    DateTime? date,
    String? vaccineName,
    String? manufacturer,
    String? batchNumber,
    String? status,
    double? cost,
    String? notes,
    DateTime? nextDueDate,
  }) {
    return VaccinationIsar()
      ..id = id
      ..recordId = recordId ?? this.recordId
      ..cattleId = cattleId ?? this.cattleId
      ..date = date ?? this.date
      ..vaccineName = vaccineName ?? this.vaccineName
      ..manufacturer = manufacturer ?? this.manufacturer
      ..batchNumber = batchNumber ?? this.batchNumber
      ..status = status ?? this.status
      ..cost = cost ?? this.cost
      ..notes = notes ?? this.notes
      ..nextDueDate = nextDueDate ?? this.nextDueDate
      ..createdAt = createdAt
      ..updatedAt = DateTime.now();
  }
}
