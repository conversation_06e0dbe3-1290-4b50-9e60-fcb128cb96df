import 'package:flutter/material.dart';
import '../../constants/app_constants.dart';

/// Universal Info Card Widget
/// 
/// A reusable card component for displaying key information with:
/// - Icon and title
/// - Primary value display
/// - Optional subtitle, badge, and insight text
/// - Consistent styling and responsive behavior
/// - Module-specific theming support

class UniversalInfoCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;
  final String? subtitle;
  final String? badge;
  final String? insight;
  final VoidCallback? onTap;
  final EdgeInsets? padding;
  final double? elevation;

  const UniversalInfoCard({
    Key? key,
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
    this.subtitle,
    this.badge,
    this.insight,
    this.onTap,
    this.padding,
    this.elevation,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isLargeScreen = MediaQuery.of(context).size.width > 600;
    final cardPadding = padding ?? EdgeInsets.all(isLargeScreen ? kPaddingMedium : kPaddingSmall);
    final iconSize = isLargeScreen ? kIconSizeMedium : kIconSizeSmall;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(kBorderRadius),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 6,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
          padding: cardPadding,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Icon and title row
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(icon, color: color, size: iconSize),
                  const SizedBox(width: kSpacingSmall),
                  Flexible(
                    child: Text(
                      title,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: color,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              SizedBox(height: isLargeScreen ? kSpacingSmall : 6),

              // Value
              Text(
                value,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w700,
                  color: color,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),

              if (subtitle != null) ...[
                const SizedBox(height: 4),
                Text(
                  subtitle!,
                  style: TextStyle(
                    color: color,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],

              if (badge != null) ...[
                const SizedBox(height: 8),
                _buildBadge(badge!, color),
              ],

              if (insight != null) ...[
                const SizedBox(height: 8),
                _buildBadge(insight!, color),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBadge(String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: kSpacingSmall, vertical: 3),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: color.withValues(alpha: 0.4),
          width: 1,
        ),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 10,
          fontWeight: FontWeight.w600,
        ),
        textAlign: TextAlign.center,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  /// Factory constructors for common use cases

  /// KPI card for key performance indicators
  factory UniversalInfoCard.kpi({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    String? subtitle,
    VoidCallback? onTap,
  }) {
    return UniversalInfoCard(
      title: title,
      value: value,
      icon: icon,
      color: color,
      subtitle: subtitle,
      onTap: onTap,
    );
  }

  /// Metric card with badge for additional context
  factory UniversalInfoCard.metric({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    String? subtitle,
    String? badge,
    VoidCallback? onTap,
  }) {
    return UniversalInfoCard(
      title: title,
      value: value,
      icon: icon,
      color: color,
      subtitle: subtitle,
      badge: badge,
      onTap: onTap,
    );
  }

  /// Insight card with additional insight text
  factory UniversalInfoCard.insight({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    String? subtitle,
    String? insight,
    VoidCallback? onTap,
  }) {
    return UniversalInfoCard(
      title: title,
      value: value,
      icon: icon,
      color: color,
      subtitle: subtitle,
      insight: insight,
      onTap: onTap,
    );
  }
}
