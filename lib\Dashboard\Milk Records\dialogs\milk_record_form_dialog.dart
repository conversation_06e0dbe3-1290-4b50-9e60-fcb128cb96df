import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/milk_record_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../../utils/date_utils.dart';

/// Dialog for adding/editing milk records
/// Follows the universal form dialog pattern with validation and cattle selection
class MilkRecordFormDialog extends StatefulWidget {
  final MilkRecordIsar? milkRecord;
  final List<CattleIsar> cattle;
  final Function(MilkRecordIsar) onSave;

  const MilkRecordFormDialog({
    super.key,
    this.milkRecord,
    required this.cattle,
    required this.onSave,
  });

  @override
  State<MilkRecordFormDialog> createState() => _MilkRecordFormDialogState();
}

class _MilkRecordFormDialogState extends State<MilkRecordFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final _morningController = TextEditingController();
  final _afternoonController = TextEditingController();
  final _eveningController = TextEditingController();
  final _fatContentController = TextEditingController();
  final _notesController = TextEditingController();

  CattleIsar? _selectedCattle;
  DateTime _selectedDate = DateTime.now();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    if (widget.milkRecord != null) {
      final record = widget.milkRecord!;
      _morningController.text = record.morning?.toString() ?? '';
      _afternoonController.text = record.afternoon?.toString() ?? '';
      _eveningController.text = record.evening?.toString() ?? '';
      _fatContentController.text = record.fatContent?.toString() ?? '';
      _notesController.text = record.notes ?? '';
      _selectedDate = record.date ?? DateTime.now();
      
      // Find selected cattle
      if (record.cattleBusinessId != null) {
        _selectedCattle = widget.cattle.firstWhere(
          (cattle) => cattle.businessId == record.cattleBusinessId,
          orElse: () => widget.cattle.first,
        );
      }
    }
  }

  @override
  void dispose() {
    _morningController.dispose();
    _afternoonController.dispose();
    _eveningController.dispose();
    _fatContentController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.milkRecord == null ? 'Add Milk Record' : 'Edit Milk Record'),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.8,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Cattle Selection
                DropdownButtonFormField<CattleIsar>(
                  value: _selectedCattle,
                  decoration: const InputDecoration(
                    labelText: 'Select Cattle',
                    border: OutlineInputBorder(),
                  ),
                  items: widget.cattle.map((cattle) {
                    return DropdownMenuItem(
                      value: cattle,
                      child: Text('${cattle.name} (${cattle.tagId})'),
                    );
                  }).toList(),
                  onChanged: (cattle) {
                    setState(() {
                      _selectedCattle = cattle;
                    });
                  },
                  validator: (value) {
                    if (value == null) {
                      return 'Please select a cattle';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Date Selection
                InkWell(
                  onTap: _selectDate,
                  child: InputDecorator(
                    decoration: const InputDecoration(
                      labelText: 'Date',
                      border: OutlineInputBorder(),
                      suffixIcon: Icon(Icons.calendar_today),
                    ),
                    child: Text(
                      AppDateUtils.formatDate(_selectedDate),
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Milk Production Fields
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _morningController,
                        decoration: const InputDecoration(
                          labelText: 'Morning (L)',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                        ],
                        validator: (value) {
                          if (value?.isNotEmpty == true) {
                            final amount = double.tryParse(value!);
                            if (amount == null || amount < 0) {
                              return 'Invalid amount';
                            }
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: TextFormField(
                        controller: _afternoonController,
                        decoration: const InputDecoration(
                          labelText: 'Afternoon (L)',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                        ],
                        validator: (value) {
                          if (value?.isNotEmpty == true) {
                            final amount = double.tryParse(value!);
                            if (amount == null || amount < 0) {
                              return 'Invalid amount';
                            }
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _eveningController,
                        decoration: const InputDecoration(
                          labelText: 'Evening (L)',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                        ],
                        validator: (value) {
                          if (value?.isNotEmpty == true) {
                            final amount = double.tryParse(value!);
                            if (amount == null || amount < 0) {
                              return 'Invalid amount';
                            }
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: TextFormField(
                        controller: _fatContentController,
                        decoration: const InputDecoration(
                          labelText: 'Fat Content (%)',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                        ],
                        validator: (value) {
                          if (value?.isNotEmpty == true) {
                            final fat = double.tryParse(value!);
                            if (fat == null || fat < 0 || fat > 100) {
                              return 'Invalid percentage';
                            }
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Notes
                TextFormField(
                  controller: _notesController,
                  decoration: const InputDecoration(
                    labelText: 'Notes (Optional)',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _saveMilkRecord,
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(widget.milkRecord == null ? 'Add' : 'Save'),
        ),
      ],
    );
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (date != null) {
      setState(() {
        _selectedDate = date;
      });
    }
  }

  Future<void> _saveMilkRecord() async {
    if (!_formKey.currentState!.validate()) return;

    // Validate that at least one milk amount is provided
    final morning = double.tryParse(_morningController.text) ?? 0;
    final afternoon = double.tryParse(_afternoonController.text) ?? 0;
    final evening = double.tryParse(_eveningController.text) ?? 0;

    if (morning == 0 && afternoon == 0 && evening == 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter at least one milk amount'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final record = widget.milkRecord ?? MilkRecordIsar();
      
      record.cattleBusinessId = _selectedCattle!.businessId;
      record.cattleTagId = _selectedCattle!.tagId;
      record.date = _selectedDate;
      record.morning = morning > 0 ? morning : null;
      record.afternoon = afternoon > 0 ? afternoon : null;
      record.evening = evening > 0 ? evening : null;
      record.fatContent = double.tryParse(_fatContentController.text);
      record.notes = _notesController.text.isNotEmpty ? _notesController.text : null;
      record.updatedAt = DateTime.now();
      
      if (widget.milkRecord == null) {
        record.createdAt = DateTime.now();
      }

      widget.onSave(record);
      Navigator.of(context).pop();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error saving milk record: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
