# Cattle Manager App - Migration Script
# This script migrates the clean structure from D:\Cattle Manager\lib to D:\Cattle Manager App\lib

Write-Host "🚀 Starting Cattle Manager App Migration..." -ForegroundColor Green

# Set paths
$SourcePath = "D:\Cattle Manager\lib"
$TargetPath = "D:\Cattle Manager App\lib"
$BackupPath = "D:\Cattle Manager App\lib_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"

# Step 1: Backup existing lib directory
Write-Host "📦 Step 1: Backing up existing lib directory..." -ForegroundColor Yellow
if (Test-Path $TargetPath) {
    Move-Item $TargetPath $BackupPath
    Write-Host "✅ Backup created at: $BackupPath" -ForegroundColor Green
}

# Step 2: Create new lib directory
Write-Host "📁 Step 2: Creating new lib directory..." -ForegroundColor Yellow
New-Item -ItemType Directory -Path $TargetPath -Force
Write-Host "✅ New lib directory created" -ForegroundColor Green

# Step 3: Copy core infrastructure files
Write-Host "🏗️ Step 3: Copying core infrastructure..." -ForegroundColor Yellow

# Copy main files
Copy-Item "$SourcePath\main.dart" "$TargetPath\" -Force
Copy-Item "$SourcePath\firebase_options.dart" "$TargetPath\" -Force -ErrorAction SilentlyContinue
Write-Host "✅ Main files copied" -ForegroundColor Green

# Copy core directories
$CoreDirectories = @("constants", "theme", "utils", "routes", "widgets")
foreach ($dir in $CoreDirectories) {
    if (Test-Path "$SourcePath\$dir") {
        Copy-Item "$SourcePath\$dir" "$TargetPath\" -Recurse -Force
        Write-Host "✅ $dir directory copied" -ForegroundColor Green
    } else {
        Write-Host "⚠️ $dir directory not found" -ForegroundColor Yellow
    }
}

# Step 4: Copy services (selective - skip corrupted ones)
Write-Host "🔧 Step 4: Copying services..." -ForegroundColor Yellow
if (Test-Path "$SourcePath\services") {
    Copy-Item "$SourcePath\services" "$TargetPath\" -Recurse -Force
    Write-Host "✅ Services copied" -ForegroundColor Green
}
if (Test-Path "$SourcePath\core") {
    Copy-Item "$SourcePath\core" "$TargetPath\" -Recurse -Force
    Write-Host "✅ Core services copied" -ForegroundColor Green
}

# Step 5: Copy screens
Write-Host "📱 Step 5: Copying screens..." -ForegroundColor Yellow
if (Test-Path "$SourcePath\screens") {
    Copy-Item "$SourcePath\screens" "$TargetPath\" -Recurse -Force
    Write-Host "✅ Screens copied" -ForegroundColor Green
}

# Step 6: Create Dashboard structure
Write-Host "🏠 Step 6: Creating Dashboard structure..." -ForegroundColor Yellow
New-Item -ItemType Directory -Path "$TargetPath\Dashboard" -Force

# Copy dashboard core files
if (Test-Path "$SourcePath\Dashboard\dashboard_screen.dart") {
    Copy-Item "$SourcePath\Dashboard\dashboard_screen.dart" "$TargetPath\Dashboard\" -Force
    Write-Host "✅ Dashboard screen copied" -ForegroundColor Green
}

# Copy dashboard widgets
if (Test-Path "$SourcePath\Dashboard\widgets") {
    Copy-Item "$SourcePath\Dashboard\widgets" "$TargetPath\Dashboard\" -Recurse -Force
    Write-Host "✅ Dashboard widgets copied" -ForegroundColor Green
}

# Step 7: Copy clean modules (priority order)
Write-Host "📦 Step 7: Copying clean modules..." -ForegroundColor Yellow

# Copy Cattle module (cleanest - 0 errors)
if (Test-Path "$SourcePath\Dashboard\Cattle") {
    Copy-Item "$SourcePath\Dashboard\Cattle" "$TargetPath\Dashboard\" -Recurse -Force
    Write-Host "✅ Cattle module copied (0 errors)" -ForegroundColor Green
}

# Copy other modules (we'll add them gradually)
$ModulesToCopy = @("Events", "Health", "Help", "Notifications", "Profile", "Settings", "User Account", "Weight")
foreach ($module in $ModulesToCopy) {
    if (Test-Path "$SourcePath\Dashboard\$module") {
        Copy-Item "$SourcePath\Dashboard\$module" "$TargetPath\Dashboard\" -Recurse -Force
        Write-Host "✅ $module module copied" -ForegroundColor Green
    } else {
        Write-Host "⚠️ $module module not found" -ForegroundColor Yellow
    }
}

# Step 8: Clean up corrupted files
Write-Host "🧹 Step 8: Cleaning up corrupted files..." -ForegroundColor Yellow

# Remove .g.dart files (will be regenerated)
Get-ChildItem -Path $TargetPath -Recurse -Filter "*.g.dart" | Remove-Item -Force
Write-Host "✅ Removed .g.dart files (will be regenerated)" -ForegroundColor Green

# Step 9: Create placeholder for problematic modules
Write-Host "📝 Step 9: Creating placeholders for problematic modules..." -ForegroundColor Yellow

$ProblematicModules = @("Breeding", "Farm Setup", "Milk Records", "Reports", "Transactions")
foreach ($module in $ProblematicModules) {
    $ModulePath = "$TargetPath\Dashboard\$module"
    if (-not (Test-Path $ModulePath)) {
        New-Item -ItemType Directory -Path $ModulePath -Force
        
        # Create basic structure
        $SubDirs = @("models", "services", "screens", "tabs", "dialogs", "details", "widgets")
        foreach ($subdir in $SubDirs) {
            New-Item -ItemType Directory -Path "$ModulePath\$subdir" -Force
        }
        
        # Create placeholder README
        @"
# $module Module - Placeholder

This module needs to be implemented or fixed.
Current status: Placeholder created during migration.

## TODO:
- [ ] Implement models
- [ ] Implement services  
- [ ] Implement screens
- [ ] Implement widgets
- [ ] Fix any corrupted files
"@ | Out-File "$ModulePath\README.md" -Encoding UTF8
        
        Write-Host "✅ $module placeholder created" -ForegroundColor Green
    }
}

# Step 10: Summary
Write-Host "`n🎉 Migration Complete!" -ForegroundColor Green
Write-Host "📊 Migration Summary:" -ForegroundColor Cyan
Write-Host "  ✅ Core infrastructure copied" -ForegroundColor Green
Write-Host "  ✅ Clean modules copied" -ForegroundColor Green
Write-Host "  ✅ Cattle module ready (0 errors)" -ForegroundColor Green
Write-Host "  📝 Problematic modules have placeholders" -ForegroundColor Yellow
Write-Host "  🧹 Corrupted .g.dart files removed" -ForegroundColor Green

Write-Host "`n📋 Next Steps:" -ForegroundColor Cyan
Write-Host "  1. cd 'D:\Cattle Manager App'" -ForegroundColor White
Write-Host "  2. flutter pub get" -ForegroundColor White
Write-Host "  3. Test basic functionality" -ForegroundColor White
Write-Host "  4. Gradually add problematic modules" -ForegroundColor White
Write-Host "  5. Run flutter packages pub run build_runner build (when Isar is ready)" -ForegroundColor White

Write-Host "`n🚀 Ready to start development!" -ForegroundColor Green
