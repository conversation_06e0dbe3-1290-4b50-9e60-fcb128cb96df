import 'package:flutter/material.dart';
import 'app_colors.dart';

/// A utility class that provides standardized AppBar configurations for the app.
///
/// Design Philosophy:
/// - One unified AppBar design for the entire app (except main dashboard)
/// - Main dashboard has its own special AppBar design
/// - All module screens use the same consistent AppBar with primary green color
class AppBarConfig {





  // ============================================================================
  // UTILITY APP BAR VARIANTS (Based on unified design)
  // ============================================================================

  /// Creates the unified AppBar with a drawer toggle button
  ///
  /// [title] - The title to display
  /// [context] - The build context
  /// [actions] - Optional list of action widgets to display on the right
  static AppBar withDrawer({
    required String title,
    required BuildContext context,
    List<Widget>? actions,
    double elevation = 0,
    PreferredSizeWidget? bottom,
  }) {
    return AppBar(
      title: Text(title),
      backgroundColor: AppColors.primary, // Unified primary color
      foregroundColor: Colors.white,
      elevation: elevation,
      leading: Builder(
        builder: (context) => IconButton(
          icon: const Icon(Icons.menu_outlined),
          onPressed: () {
            Scaffold.of(context).openDrawer();
          },
        ),
      ),
      actions: actions,
      bottom: bottom,
    );
  }

  /// Creates the unified AppBar with a back button
  ///
  /// [title] - The title to display
  /// [context] - The build context
  /// [actions] - Optional list of action widgets to display on the right
  /// [onBack] - Optional callback when back button is pressed (defaults to Navigator.pop)
  static AppBar withBack({
    required String title,
    required BuildContext context,
    List<Widget>? actions,
    VoidCallback? onBack,
    double elevation = 0,
    PreferredSizeWidget? bottom,
  }) {
    return AppBar(
      title: Text(title),
      backgroundColor: AppColors.primary, // Unified primary color
      foregroundColor: Colors.white,
      elevation: elevation,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back),
        onPressed: onBack ?? () => Navigator.pop(context),
      ),
      actions: actions,
      bottom: bottom,
    );
  }

  // ============================================================================
  // UTILITY WIDGETS FOR APP BARS
  // ============================================================================

  /// Creates a common notifications button for the AppBar
  static IconButton notificationsButton({
    required VoidCallback onPressed,
    String tooltip = 'Notifications',
  }) {
    return IconButton(
      icon: const Icon(Icons.notifications_outlined),
      onPressed: onPressed,
      tooltip: tooltip,
    );
  }

  /// Creates a save button for the AppBar
  static IconButton saveButton({
    required VoidCallback onPressed,
    String tooltip = 'Save',
  }) {
    return IconButton(
      icon: const Icon(Icons.save),
      onPressed: onPressed,
      tooltip: tooltip,
    );
  }

  /// Creates a loading indicator in place of an action button
  static Widget loadingIndicator({
    double size = 24.0,
    double padding = 12.0,
  }) {
    return Container(
      width: 48,
      height: 48,
      padding: EdgeInsets.all(padding),
      child: const CircularProgressIndicator(
        strokeWidth: 2,
        color: Colors.white,
      ),
    );
  }
}
