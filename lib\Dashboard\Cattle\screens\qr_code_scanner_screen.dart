import 'package:flutter/material.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:uuid/uuid.dart';
import '../models/cattle_isar.dart';
import '../services/qr_code_service.dart';
import '../../Health/models/health_record_isar.dart';
import '../../Breeding/models/breeding_record_isar.dart';
import '../../Milk Records/models/milk_record_isar.dart';
import '../services/cattle_repository.dart';
import '../../Health/services/health_repository.dart';
import '../../Breeding/services/breeding_repository.dart';
import '../../Milk Records/services/milk_repository.dart';
import '../../Events/services/events_repository.dart';
import '../../../utils/message_utils.dart';
import 'package:get_it/get_it.dart';

class QRCodeScannerScreen extends StatefulWidget {
  const QRCodeScannerScreen({Key? key}) : super(key: key);

  @override
  State<QRCodeScannerScreen> createState() => _QRCodeScannerScreenState();
}

class _QRCodeScannerScreenState extends State<QRCodeScannerScreen> {
  final MobileScannerController controller = MobileScannerController();
  bool _isProcessing = false;

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  Future<void> _onDetect(BarcodeCapture barcode) async {
    if (barcode.barcodes.isEmpty || _isProcessing) return;

    setState(() => _isProcessing = true);

    try {
      // Get the first barcode value
      final barcodeValue = barcode.barcodes.first.rawValue;

      if (barcodeValue == null) {
        throw Exception('Failed to read QR code value');
      }

      final data = await QRCodeService.parseQRData(barcodeValue);
      final tagId = data?['tagId'];

      if (tagId != null) {
        // Find cattle by tagId using direct Isar query
        final isar = GetIt.instance<Isar>();
        final CattleIsar? cattle = await isar.cattleIsars
            .filter()
            .tagIdEqualTo(tagId)
            .findFirst();

        if (!mounted) return;
        final navigatorContext = context;

        if (cattle != null && mounted) {
          // Update local database with scanned data if it's newer
          final lastUpdated = DateTime.parse(data!['lastUpdated']);
          final cattleUpdatedAt =
              cattle.updatedAt ?? DateTime.fromMillisecondsSinceEpoch(0);
          if (lastUpdated.isAfter(cattleUpdatedAt)) {
            await _updateCattleData(cattle, data);
          }

          controller.stop();
          if (navigatorContext.mounted) {
            Navigator.of(navigatorContext).pop(cattle);
          }
        } else if (mounted && navigatorContext.mounted) {
          CattleMessageUtils.showError(navigatorContext,
              'Cattle not found in database. Please add the cattle first.');
          if (mounted) setState(() => _isProcessing = false);
        }
      } else {
        throw Exception('Invalid QR code format: missing tagId');
      }
    } catch (e) {
      if (!mounted) return;
      final navigatorContext = context;
      if (navigatorContext.mounted) {
        CattleMessageUtils.showError(navigatorContext,
            'Error reading QR code: ${e.toString()}');
        if (mounted) setState(() => _isProcessing = false);
      }
    }
  }

  Future<void> _updateCattleData(
      CattleIsar cattle, Map<String, dynamic> data) async {
    // Update health records
    if (data['healthRecords'] != null) {
      final healthRepository = GetIt.instance<HealthRepository>();
      for (var recordData in data['healthRecords']) {
        final record = HealthRecordIsar()
          ..recordId = recordData['recordId'] ?? const Uuid().v4()
          ..cattleId = cattle.id.toString()
          ..recordType = recordData['recordType'] ?? ''
          ..details = recordData['details'] ?? ''
          ..diagnosis = recordData['diagnosis'] ?? ''
          ..treatment = recordData['treatment'] ?? ''
          ..medicine = recordData['medicine'] ?? ''
          ..dose = recordData['dose']?.toDouble() ?? 0.0
          ..dosageUnit = recordData['dosageUnit'] ?? ''
          ..date = DateTime.parse(recordData['date'])
          ..followUpRequired = recordData['followUpRequired'] ?? false
          ..followUpDate = recordData['followUpDate'] != null
              ? DateTime.parse(recordData['followUpDate'])
              : null
          ..isFollowUp = recordData['isFollowUp'] ?? false
          ..isResolved = recordData['isResolved'] ?? false
          ..previousRecordId = recordData['previousRecordId']
          ..cost = recordData['cost']?.toDouble()
          ..notes = recordData['notes'];
        await healthRepository.saveHealthRecord(record);
      }
    }

    // Update breeding records
    if (data['breedingRecords'] != null) {
      final breedingRepository = GetIt.instance<BreedingRepository>();
      for (var recordData in data['breedingRecords']) {
        final record = BreedingRecordIsar()
          ..businessId = recordData['recordId'] ?? const Uuid().v4()
          ..cattleId = cattle.id.toString()
          ..date = DateTime.parse(recordData['date'])
          ..bullIdOrType = recordData['bullIdOrType']
          ..method = recordData['method']
          ..status = recordData['status']
          ..expectedDate = recordData['expectedDate'] != null
              ? DateTime.parse(recordData['expectedDate'])
              : null
          ..cost = recordData['cost']?.toDouble()
          ..notes = recordData['notes']
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now();
        await breedingRepository.saveBreedingRecord(record);
      }
    }

    // Update milk records
    if (data['milkRecords'] != null) {
      final milkRepository = GetIt.instance<MilkRepository>();
      for (var recordData in data['milkRecords']) {
        final record = MilkRecordIsar()
          ..recordId = recordData['recordId'] ?? const Uuid().v4()
          ..cattleId = cattle.id.toString()
          ..morning = recordData['morning']?.toDouble() ?? 0.0
          ..afternoon = recordData['afternoon']?.toDouble() ?? 0.0
          ..evening = recordData['evening']?.toDouble() ?? 0.0
          ..date = DateTime.parse(recordData['date'])
          ..notes = recordData['notes']
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now();
        await milkRepository.saveMilkRecord(record);
      }
    }

    // Update events
    if (data['events'] != null) {
      final eventsRepository = GetIt.instance<EventsRepository>();
      for (var event in data['events']) {
        await eventsRepository.saveEvent(event);
      }
    }

    // Update cattle basic information
    final updatedCattle = CattleIsar()
      ..id = cattle.id
      ..tagId = cattle.tagId
      ..name = data['name'] ?? cattle.name
      ..breedId = data['breedId'] ?? cattle.breedId
      ..animalTypeId = data['animalTypeId'] ?? cattle.animalTypeId
      ..dateOfBirth = data['dateOfBirth'] != null
          ? DateTime.parse(data['dateOfBirth'])
          : cattle.dateOfBirth
      ..gender = data['gender'] ?? cattle.gender
      ..status = data['status'] ?? cattle.status
      ..purchaseDate = data['purchaseDate'] != null
          ? DateTime.parse(data['purchaseDate'])
          : cattle.purchaseDate
      ..purchasePrice = data['purchasePrice'] ?? cattle.purchasePrice
      ..weight = data['weight'] ?? cattle.weight
      ..notes = data['notes'] ?? cattle.notes
      ..motherTagId = data['motherTagId'] ?? cattle.motherTagId
      ..updatedAt = DateTime.parse(data['lastUpdated']);

    // Use saveCattle for both add and update (upsert)
    await GetIt.instance<CattleRepository>().saveCattle(updatedCattle);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Scan Cattle QR Code'),
        actions: [
          IconButton(
            icon: ValueListenableBuilder(
              valueListenable: controller.torchState,
              builder: (context, state, child) {
                return Icon(
                  state == TorchState.on ? Icons.flash_off : Icons.flash_on,
                );
              },
            ),
            onPressed: () => controller.toggleTorch(),
          ),
          IconButton(
            icon: ValueListenableBuilder(
              valueListenable: controller.cameraFacingState,
              builder: (context, state, child) {
                return Icon(
                  state == CameraFacing.front
                      ? Icons.camera_rear
                      : Icons.camera_front,
                );
              },
            ),
            onPressed: () => controller.switchCamera(),
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            flex: 5,
            child: MobileScanner(
              controller: controller,
              onDetect: _onDetect,
              overlay: Container(
                decoration: BoxDecoration(
                  color: Colors.black.withAlpha(128), // 0.5 * 255 = 128
                ),
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    final scanSize = constraints.maxWidth * 0.8;
                    final scanPosition = (constraints.maxWidth - scanSize) / 2;
                    return Stack(
                      children: [
                        Container(
                          width: constraints.maxWidth,
                          height: constraints.maxHeight,
                          color: Colors.transparent,
                        ),
                        Positioned(
                          left: scanPosition,
                          top: (constraints.maxHeight - scanSize) / 2,
                          child: Container(
                            width: scanSize,
                            height: scanSize,
                            decoration: BoxDecoration(
                              color: Colors.transparent,
                              border: Border.all(
                                color: Theme.of(context).primaryColor,
                                width: 3,
                              ),
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Align QR code within the frame to scan',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  if (_isProcessing) ...[
                    const SizedBox(height: 8),
                    const CircularProgressIndicator(),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
