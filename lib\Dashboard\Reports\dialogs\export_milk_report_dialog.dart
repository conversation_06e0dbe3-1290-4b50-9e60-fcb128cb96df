import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'package:share_plus/share_plus.dart';
import '../models/milk_report_data_isar.dart';
import '../../../utils/message_utils.dart';

class ExportMilkReportDialog extends StatefulWidget {
  final MilkReportDataIsar reportData;

  const ExportMilkReportDialog({
    Key? key,
    required this.reportData,
  }) : super(key: key);

  @override
  ExportMilkReportDialogState createState() => ExportMilkReportDialogState();
}

class ExportMilkReportDialogState extends State<ExportMilkReportDialog> {
  bool includeSummary = true;
  bool includeChart = true;
  bool includeDetails = true;
  String exportFormat = 'PDF';

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Export Report'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CheckboxListTile(
            title: const Text('Include Summary'),
            value: includeSummary,
            onChanged: (value) {
              setState(() => includeSummary = value ?? true);
            },
          ),
          CheckboxListTile(
            title: const Text('Include Chart'),
            value: includeChart,
            onChanged: (value) {
              setState(() => includeChart = value ?? true);
            },
          ),
          CheckboxListTile(
            title: const Text('Include Details'),
            value: includeDetails,
            onChanged: (value) {
              setState(() {
                includeDetails = value ?? false;
              });
            },
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            decoration: const InputDecoration(
              labelText: 'Export Format',
            ),
            value: exportFormat,
            items: const [
              DropdownMenuItem(value: 'PDF', child: Text('PDF')),
              DropdownMenuItem(value: 'Excel', child: Text('Excel')),
            ],
            onChanged: (value) {
              setState(() => exportFormat = value ?? 'PDF');
            },
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () => _handleExport(context),
                child: const Text('Export'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _handleExport(BuildContext context) async {
    final navigator = Navigator.of(context);

    try {
      await _exportReport(context);
      if (!mounted) return;
      navigator.pop();
    } catch (e) {
      if (!mounted) return;
      if (context.mounted) {
        MessageUtils.showError(context, 'Failed to export report: $e');
      }
    }
  }

  Future<void> _exportReport(BuildContext context) async {
    final dateFormat = DateFormat('yyyy-MM-dd');

    if (exportFormat == 'PDF') {
      final pdf = pw.Document();

      pdf.addPage(
        pw.Page(
          build: (context) {
            final children = <pw.Widget>[];

            // Title
            children.add(
              pw.Header(
                level: 0,
                child: pw.Text('Milk Production Report',
                    style: pw.TextStyle(
                        fontSize: 24, fontWeight: pw.FontWeight.bold)),
              ),
            );

            // Date Range
            if (widget.reportData.startDate != null ||
                widget.reportData.endDate != null) {
              children.add(
                pw.Paragraph(
                  text:
                      'Period: ${widget.reportData.startDate != null ? dateFormat.format(widget.reportData.startDate!) : 'Start'} to ${widget.reportData.endDate != null ? dateFormat.format(widget.reportData.endDate!) : 'End'}',
                ),
              );
            }

            // Summary Section
            if (includeSummary) {
              children.add(pw.SizedBox(height: 20));
              children.add(
                pw.Container(
                  padding: const pw.EdgeInsets.all(10),
                  decoration: pw.BoxDecoration(
                    border: pw.Border.all(),
                    borderRadius:
                        const pw.BorderRadius.all(pw.Radius.circular(10)),
                  ),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text('Summary',
                          style: pw.TextStyle(
                              fontSize: 18, fontWeight: pw.FontWeight.bold)),
                      pw.SizedBox(height: 10),
                      pw.Text('Total Records: ${widget.reportData.totalRecords ?? 0}'),
                      pw.Text('Total Milk: ${widget.reportData.totalMilk ?? 0} L'),
                      pw.Text('Average Daily: ${widget.reportData.averageMilkPerDay ?? 0} L'),
                      if (widget.reportData.topProducerName != null)
                        pw.Text('Top Producer: ${widget.reportData.topProducerName} (${widget.reportData.topProducerAmount ?? 0} L)'),
                    ],
                  ),
                ),
              );
            }

            // Details Section
            if (includeDetails) {
              children.add(pw.SizedBox(height: 20));
              
              // Since we don't have the actual filteredRecords property in MilkReportDataIsar,
              // we'll just display a placeholder message
              children.add(
                pw.Container(
                  padding: const pw.EdgeInsets.all(10),
                  decoration: pw.BoxDecoration(
                    border: pw.Border.all(),
                    borderRadius: const pw.BorderRadius.all(pw.Radius.circular(10)),
                  ),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text('Details',
                          style: pw.TextStyle(
                              fontSize: 18, fontWeight: pw.FontWeight.bold)),
                      pw.SizedBox(height: 10),
                      pw.Text('The detailed milk records are not available for export at this time.'),
                    ],
                  ),
                ),
              );
            }

            return pw.Column(children: children);
          },
        ),
      );

      final tempDir = await getTemporaryDirectory();
      final file = File(
          '${tempDir.path}/milk_production_report_${DateTime.now().millisecondsSinceEpoch}.pdf');
      await file.writeAsBytes(await pdf.save());

      if (!mounted) return;

      await Share.shareXFiles(
        [XFile(file.path)],
        subject: 'Milk Production Report',
      );
    } else {
      // Generate a CSV file with available data
      final csvData = <List<String>>[
        ['Report Type', 'Total Records', 'Total Milk (L)', 'Average Milk Per Day (L)', 'Top Producer'],
        [
          widget.reportData.reportType ?? 'Milk Report',
          (widget.reportData.totalRecords ?? 0).toString(),
          (widget.reportData.totalMilk ?? 0).toString(),
          (widget.reportData.averageMilkPerDay ?? 0).toString(),
          '${widget.reportData.topProducerName ?? 'N/A'} (${widget.reportData.topProducerAmount ?? 0} L)',
        ],
      ];

      final csvString = csvData.map((row) => row.join(',')).join('\n');
      final tempDir = await getTemporaryDirectory();
      final file = File(
          '${tempDir.path}/milk_production_report_${DateTime.now().millisecondsSinceEpoch}.csv');
      await file.writeAsString(csvString);

      if (!mounted) return;

      await Share.shareXFiles(
        [XFile(file.path)],
        subject: 'Milk Production Report',
      );
    }
  }
}
