import 'package:flutter/material.dart';
import '../services/cattle_analytics_service.dart';
import '../../../constants/app_colors.dart';

/// Service for generating cattle management insights and recommendations
/// Extracted from UI layer for better testability and reusability
/// Uses dependency injection pattern for architectural consistency
class CattleInsightsService {
  // Business Rule Thresholds - Centralized for easy maintenance and clarity
  // These constants define the business logic for cattle management insights

  /// Herd size below this threshold is considered "small" requiring individual focus
  static const int _smallHerdThreshold = 10;

  /// Herd size above this threshold is considered "large" requiring systematic management
  static const int _largeHerdThreshold = 50;

  /// Male ratio above this threshold indicates potential breeding inefficiency
  static const double _highMaleRatioThreshold = 0.7; // 70% males

  /// Male ratio below this threshold may indicate breeding challenges
  static const double _lowMaleRatioThreshold = 0.1; // 10% males

  /// Any unknown data above this threshold triggers data quality insights
  static const double _unknownDataThreshold = 0.0; // Any unknown data

  /// Generate cattle management insights based on analytics data
  List<CattleInsight> generateInsights(CattleAnalyticsResult analytics) {
    final insights = <CattleInsight>[];

    // Herd Size Insights
    insights.addAll(_analyzeHerdSize(analytics));

    // Gender Distribution Insights
    final genderInsight = _analyzeGenderDistribution(analytics);
    if (genderInsight != null) {
      insights.add(genderInsight);
    }

    // Type Distribution Insights
    final typeInsight = _analyzeTypeDistribution(analytics);
    if (typeInsight != null) {
      insights.add(typeInsight);
    }

    return insights;
  }

  /// Generate management recommendations
  List<ManagementRecommendation> generateManagementRecommendations() {
    return [
      ManagementRecommendation(
        title: 'Record Keeping Best Practices',
        description: 'Maintain comprehensive records to optimize your cattle management and ensure regulatory compliance.',
        icon: Icons.assignment,
        color: Colors.blue,
      ),
      ManagementRecommendation(
        title: 'Regular Health Monitoring',
        description: 'Establish a routine health monitoring schedule to catch issues early and maintain herd health.',
        icon: Icons.health_and_safety,
        color: Colors.green,
      ),
      ManagementRecommendation(
        title: 'Identification System',
        description: 'Ensure all cattle have proper identification tags for easy tracking and management.',
        icon: Icons.qr_code,
        color: AppColors.cattleHeader,
      ),
      ManagementRecommendation(
        title: 'Data Backup',
        description: 'Regularly backup your cattle data to prevent loss of important information.',
        icon: Icons.backup,
        color: Colors.green,
      ),
    ];
  }

  /// Analyze herd size and provide insights
  List<CattleInsight> _analyzeHerdSize(CattleAnalyticsResult analytics) {
    final insights = <CattleInsight>[];
    final totalCattle = analytics.totalCattle;

    if (totalCattle == 0) {
      insights.add(CattleInsight(
        title: 'Start Your Cattle Management Journey',
        description: 'You haven\'t added any cattle records yet. Start by adding your first cattle to begin tracking and managing your herd.',
        icon: Icons.pets,
        color: AppColors.cattleHeader,
        priority: InsightPriority.high,
        recommendations: [
          'Add cattle records with complete information',
          'Include tag IDs for easy identification',
          'Record gender and animal type for better analytics',
        ],
      ));
    } else if (totalCattle <= _smallHerdThreshold) {
      insights.add(CattleInsight(
        title: 'Small Herd Management',
        description: 'You have a small herd of $totalCattle cattle. Focus on individual animal care and detailed record keeping.',
        icon: Icons.pets,
        color: Colors.green,
        priority: InsightPriority.low,
        recommendations: [
          'Maintain detailed individual records',
          'Focus on quality over quantity',
          'Monitor each animal closely',
          'Plan for gradual herd expansion',
        ],
      ));
    } else if (totalCattle > _largeHerdThreshold) {
      insights.add(CattleInsight(
        title: 'Large Herd Management',
        description: 'You have a large herd of $totalCattle cattle. Consider implementing systematic management practices.',
        icon: Icons.groups,
        color: Colors.orange,
        priority: InsightPriority.high,
        recommendations: [
          'Implement batch management systems',
          'Use automated tracking where possible',
          'Regular health screening schedules',
          'Consider herd segmentation strategies',
        ],
      ));
    }

    return insights;
  }

  /// Analyze gender distribution and provide insights
  CattleInsight? _analyzeGenderDistribution(CattleAnalyticsResult analytics) {
    final totalCattle = analytics.totalCattle;
    if (totalCattle < 2) return null;

    final maleCount = analytics.maleCount;
    final maleRatio = maleCount / totalCattle;

    if (maleRatio > _highMaleRatioThreshold) {
      return CattleInsight(
        title: 'High Male Ratio Detected',
        description: 'Your herd has a high proportion of males (${(maleRatio * 100).toStringAsFixed(1)}%). This may impact breeding efficiency.',
        icon: Icons.male,
        color: Colors.blue,
        priority: InsightPriority.medium,
        recommendations: [
          'Consider increasing female cattle for breeding',
          'Evaluate market strategy for male cattle',
          'Plan breeding program optimization',
        ],
      );
    } else if (maleRatio < _lowMaleRatioThreshold && maleCount == 0) {
      return CattleInsight(
        title: 'No Male Cattle Detected',
        description: 'Your herd has no male cattle. Consider breeding requirements and genetic diversity.',
        icon: Icons.male,
        color: Colors.red,
        priority: InsightPriority.high,
        recommendations: [
          'Consider acquiring breeding bulls',
          'Evaluate artificial insemination options',
          'Plan for genetic diversity maintenance',
        ],
      );
    }

    return null;
  }

  /// Analyze data completeness and provide insights about unknown/missing information
  /// Note: Currently analyzes status distribution for unknown animal types
  CattleInsight? _analyzeTypeDistribution(CattleAnalyticsResult analytics) {
    final statusData = analytics.statusDistribution; // More accurate variable name
    final totalCattle = analytics.totalCattle;

    if (statusData.isEmpty || totalCattle == 0) return null;

    // Check for unknown animal type status - indicates incomplete data entry
    final hasUnknownType = statusData.containsKey('Unknown') && statusData['Unknown']! > _unknownDataThreshold;

    if (hasUnknownType) {
      final unknownCount = statusData['Unknown']!;
      final unknownPercentage = (unknownCount / totalCattle) * 100;

      return CattleInsight(
        title: 'Incomplete Type Information',
        description: '$unknownCount cattle (${unknownPercentage.toStringAsFixed(1)}%) have unknown animal types. Complete this information for better management.',
        icon: Icons.help_outline,
        color: Colors.orange,
        priority: InsightPriority.medium,
        recommendations: [
          'Update animal type information for all cattle',
          'Use standardized type classifications',
          'Ensure consistent data entry practices',
        ],
      );
    }

    return null;
  }
}

/// Data classes for cattle insights
class CattleInsight {
  final String title;
  final String description;
  final IconData icon;
  final Color color;
  final InsightPriority priority;
  final List<String> recommendations;

  CattleInsight({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
    required this.priority,
    this.recommendations = const [],
  });
}

class ManagementRecommendation {
  final String title;
  final String description;
  final IconData icon;
  final Color color;

  ManagementRecommendation({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
  });
}

enum InsightPriority {
  low(Colors.green, 'Low'),
  medium(Colors.orange, 'Medium'),
  high(Colors.red, 'High');

  const InsightPriority(this.color, this.label);
  final Color color;
  final String label;
}
