import 'package:flutter/material.dart';
import '../../../constants/app_tabs.dart';
import '../controllers/transaction_detail_controller.dart';
import '../controllers/transaction_controller.dart';
import '../../widgets/index.dart';

class TransactionDetailsAnalyticsTab extends StatefulWidget {
  final TransactionDetailController controller;

  const TransactionDetailsAnalyticsTab({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  State<TransactionDetailsAnalyticsTab> createState() => _TransactionDetailsAnalyticsTabState();
}

class _TransactionDetailsAnalyticsTabState extends State<TransactionDetailsAnalyticsTab>
    with AutomaticKeepAliveClientMixin {

  late final FilterController _filterController;

  @override
  void initState() {
    super.initState();
    _filterController = FilterController();
  }

  @override
  void dispose() {
    _filterController.dispose();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    return ListenableBuilder(
      listenable: widget.controller,
      builder: (context, child) {
        if (widget.controller.isLoading) {
          return UniversalLoadingIndicator.transactions();
        }

        if (widget.controller.error != null) {
          return UniversalErrorIndicator.transactions(
            message: widget.controller.error!,
            onRetry: () => widget.controller.refresh(),
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              UniversalDateFilterWidget(
                controller: _filterController,
                theme: FilterTheme.transaction,
                config: WidgetConfig.compactConfig,
              ),
              const SizedBox(height: 16),
              _buildFilterInfoCard(),
              const SizedBox(height: 16),
              _buildSummaryCard(),
              const SizedBox(height: 16),
              _buildTrendCard(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFilterInfoCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: UniversalEmptyStateTheme.transactions),
                const SizedBox(width: 8),
                const Text(
                  'Filter Information',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (widget.controller.categoryFilter != null)
              _buildInfoRow(
                'Category Filter',
                widget.controller.categoryFilter!,
                Icons.category,
              ),
            if (widget.controller.typeFilter != null)
              _buildInfoRow(
                'Type Filter',
                widget.controller.typeFilter!,
                Icons.type_specimen,
              ),
            _buildInfoRow(
              'Total Records',
              widget.controller.filteredRecords.length.toString(),
              Icons.receipt_long,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, size: 16, color: Colors.grey[600]),
          const SizedBox(width: 8),
          Text(
            '$label: ',
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              color: Colors.grey,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard() {
    final analyticsSummary = widget.controller.analyticsSummary;

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.analytics, color: UniversalEmptyStateTheme.transactions),
                const SizedBox(width: 8),
                const Text(
                  'Financial Summary',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'Total Income',
                    '\$${analyticsSummary.totalIncome.toStringAsFixed(2)}',
                    Icons.trending_up,
                    Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'Total Expenses',
                    '\$${analyticsSummary.totalExpenses.toStringAsFixed(2)}',
                    Icons.trending_down,
                    Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'Net Balance',
                    '\$${analyticsSummary.netBalance.toStringAsFixed(2)}',
                    Icons.account_balance_wallet,
                    analyticsSummary.netBalance >= 0 ? Colors.green : Colors.red,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'Avg Transaction',
                    '\$${_calculateAverageTransaction(analyticsSummary).toStringAsFixed(2)}',
                    Icons.calculate,
                    UniversalEmptyStateTheme.transactions,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTrendCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.show_chart, color: Colors.purple),
                SizedBox(width: 8),
                Text(
                  'Transaction Trends',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: widget.controller.filteredRecords.isEmpty
                  ? const Center(
                      child: Text(
                        'No transaction data available for trends',
                        style: TextStyle(color: Colors.grey),
                      ),
                    )
                  : const Center(
                      child: Text(
                        'Transaction trend chart will be displayed here',
                        style: TextStyle(color: Colors.grey),
                      ),
                    ),
            ),
          ],
        ),
      ),
    );
  }

  double _calculateAverageTransaction(TransactionAnalyticsResult summary) {
    if (summary.totalTransactions == 0) return 0;
    return (summary.totalIncome + summary.totalExpenses) / summary.totalTransactions;
  }
}
