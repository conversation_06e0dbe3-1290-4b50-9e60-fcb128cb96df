import 'package:flutter/material.dart';
import '../controllers/weight_controller.dart';

/// Weight analytics tab - pure UI component that reads from controller
class WeightAnalyticsTab extends StatelessWidget {
  final WeightController controller;

  const WeightAnalyticsTab({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Summary Cards
          _buildSummaryCards(),
          const SizedBox(height: 24),

          // Charts Section
          _buildChartsSection(),
          const SizedBox(height: 24),

          // Performance Metrics
          _buildPerformanceMetrics(),
        ],
      ),
    );
  }

  Widget _buildSummaryCards() {
    return Row(
      children: [
        Expanded(
          child: Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: <PERSON>umn(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('Total Records', style: TextStyle(fontSize: 14, color: Colors.grey)),
                  const SizedBox(height: 8),
                  Text(
                    '${controller.allRecords.length}',
                    style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('Cattle Tracked', style: TextStyle(fontSize: 14, color: Colors.grey)),
                  const SizedBox(height: 8),
                  Text(
                    '${controller.allCattle.length}',
                    style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildChartsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Weight Trends',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Container(
              height: 200,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Center(
                child: Text(
                  'Chart Coming Soon',
                  style: TextStyle(color: Colors.grey),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceMetrics() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Performance Insights',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            if (controller.insights.performance.topPerformerName.isNotEmpty)
              ListTile(
                leading: const Icon(Icons.star, color: Colors.amber),
                title: const Text('Top Performer'),
                subtitle: Text(controller.insights.performance.topPerformerName),
                trailing: Text(
                  '${controller.insights.performance.topPerformerGainRate.toStringAsFixed(2)} kg/day',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ListTile(
              leading: const Icon(Icons.trending_up, color: Colors.green),
              title: const Text('Average Daily Gain'),
              trailing: Text(
                '${controller.insights.performance.averageDailyGain.toStringAsFixed(2)} kg/day',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
            ListTile(
              leading: const Icon(Icons.assessment, color: Colors.blue),
              title: const Text('Growth Trend'),
              subtitle: Text(controller.insights.performance.growthTrendDescription),
              trailing: Text(
                controller.insights.performance.growthTrend,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: _getTrendColor(controller.insights.performance.growthTrend),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getTrendColor(String trend) {
    switch (trend.toLowerCase()) {
      case 'excellent':
        return Colors.green;
      case 'good':
        return Colors.lightGreen;
      case 'stable':
        return Colors.orange;
      case 'concerning':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
