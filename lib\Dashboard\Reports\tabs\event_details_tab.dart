import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/event_report_data_isar.dart';

class EventDetailsTab extends StatefulWidget {
  final EventReportDataIsar reportData;

  const EventDetailsTab({
    Key? key,
    required this.reportData,
  }) : super(key: key);

  @override
  EventDetailsTabState createState() => EventDetailsTabState();
}

class EventDetailsTabState extends State<EventDetailsTab> {
  String? _sortColumn;
  bool _sortAscending = true;
  final currencyFormat = NumberFormat.currency(locale: 'en_US', symbol: '\$');

  @override
  Widget build(BuildContext context) {
    final events = List.from(widget.reportData.filteredEvents);

    // Apply sorting
    if (_sortColumn != null) {
      events.sort((a, b) {
        int comparison;
        switch (_sortColumn) {
          case 'date':
            comparison = a.date.compareTo(b.date);
            break;
          case 'cattle':
            comparison = a.cattleId.compareTo(b.cattleId);
            break;
          case 'type':
            comparison = a.type.compareTo(b.type);
            break;
          case 'description':
            comparison = a.description.compareTo(b.description);
            break;
          case 'status':
            comparison = a.status.compareTo(b.status);
            break;
          case 'cost':
            comparison = a.cost.compareTo(b.cost);
            break;
          default:
            comparison = 0;
        }
        return _sortAscending ? comparison : -comparison;
      });
    }

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SingleChildScrollView(
        child: DataTable(
          sortColumnIndex: _getSortColumnIndex(),
          sortAscending: _sortAscending,
          columns: [
            DataColumn(
              label: const Text('Date'),
              onSort: (_, __) => _onSort('date'),
            ),
            DataColumn(
              label: const Text('Cattle'),
              onSort: (_, __) => _onSort('cattle'),
            ),
            DataColumn(
              label: const Text('Type'),
              onSort: (_, __) => _onSort('type'),
            ),
            DataColumn(
              label: const Text('Description'),
              onSort: (_, __) => _onSort('description'),
            ),
            DataColumn(
              label: const Text('Status'),
              onSort: (_, __) => _onSort('status'),
            ),
            DataColumn(
              label: const Text('Cost'),
              numeric: true,
              onSort: (_, __) => _onSort('cost'),
            ),
          ],
          rows: events.map((event) {
            return DataRow(
              cells: [
                DataCell(Text(DateFormat('yyyy-MM-dd').format(event.date))),
                DataCell(Text(event.cattleId)),
                DataCell(
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getEventTypeColor(event.type),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      event.type,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
                DataCell(
                  ConstrainedBox(
                    constraints: const BoxConstraints(maxWidth: 300),
                    child: Text(
                      event.description,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                DataCell(
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getStatusColor(event.status),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      event.status,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
                DataCell(
                  Text(
                    event.cost > 0 ? currencyFormat.format(event.cost) : '-',
                    style: TextStyle(
                      fontWeight: event.cost > 0 ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                ),
              ],
            );
          }).toList(),
        ),
      ),
    );
  }

  void _onSort(String column) {
    setState(() {
      if (_sortColumn == column) {
        _sortAscending = !_sortAscending;
      } else {
        _sortColumn = column;
        _sortAscending = true;
      }
    });
  }

  int? _getSortColumnIndex() {
    switch (_sortColumn) {
      case 'date':
        return 0;
      case 'cattle':
        return 1;
      case 'type':
        return 2;
      case 'description':
        return 3;
      case 'status':
        return 4;
      case 'cost':
        return 5;
      default:
        return null;
    }
  }

  Color _getEventTypeColor(String type) {
    switch (type.toLowerCase()) {
      case 'health':
        return const Color(0xFF2E7D32);
      case 'breeding':
        return const Color(0xFF1976D2);
      case 'vaccination':
        return const Color(0xFFFFA000);
      default:
        return const Color(0xFF757575);
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return const Color(0xFF2E7D32);
      case 'pending':
        return const Color(0xFFFFA000);
      case 'cancelled':
        return const Color(0xFFD32F2F);
      default:
        return const Color(0xFF757575);
    }
  }
}
