import 'package:isar/isar.dart';

part 'user_preferences_isar.g.dart';

/// User preferences storage
@collection
class UserPreferencesIsar {
  /// Auto-incremented ID
  Id id = Isar.autoIncrement;
  
  /// Dashboard layout preference
  String? dashboardLayout;
  
  /// Whether to show the welcome screen
  bool? showWelcomeScreen = true;
  
  /// Whether to enable tutorials
  bool? enableTutorials = true;
  
  /// Whether to enable reminders
  bool? enableReminders = true;
  
  /// Default reminder time (HH:MM)
  String? reminderTime;
  
  /// Default view mode (list, grid, etc.)
  String? defaultViewMode;
  
  /// Number of items to show per page
  int? itemsPerPage = 20;
  
  /// Time of creation
  DateTime? createdAt;
  
  /// Time of last update
  DateTime? updatedAt;
  
  /// Constructor
  UserPreferencesIsar();
  
  /// Convert to Map/JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'dashboardLayout': dashboardLayout,
      'showWelcomeScreen': showWelcomeScreen,
      'enableTutorials': enableTutorials,
      'enableReminders': enableReminders,
      'reminderTime': reminderTime,
      'defaultViewMode': defaultViewMode,
      'itemsPerPage': itemsPerPage,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }
  
  /// Create from Map/JSON
  factory UserPreferencesIsar.fromJson(Map<String, dynamic> json) {
    return UserPreferencesIsar()
      ..dashboardLayout = json['dashboardLayout'] as String?
      ..showWelcomeScreen = json['showWelcomeScreen'] as bool?
      ..enableTutorials = json['enableTutorials'] as bool?
      ..enableReminders = json['enableReminders'] as bool?
      ..reminderTime = json['reminderTime'] as String?
      ..defaultViewMode = json['defaultViewMode'] as String?
      ..itemsPerPage = json['itemsPerPage'] as int?
      ..createdAt = json['createdAt'] != null ? DateTime.parse(json['createdAt'] as String) : null
      ..updatedAt = json['updatedAt'] != null ? DateTime.parse(json['updatedAt'] as String) : null;
  }
} 