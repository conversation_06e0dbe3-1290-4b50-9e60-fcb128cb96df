import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/cattle_report_data_isar.dart';
import '../models/chart_data_isar.dart';

class CattleSummaryTab extends StatelessWidget {
  final CattleReportDataIsar reportData;

  const CattleSummaryTab({
    Key? key,
    required this.reportData,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final summaryData = reportData.getSummaryData();
    final chartData = reportData.getChartData();

    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSummaryCard(summaryData),
            const SizedBox(height: 24),
            if (chartData.isNotEmpty) _buildCategoryChart(chartData),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCard(Map<String, dynamic> summaryData) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Cattle Summary',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildSummaryRow('Total Cattle', summaryData['Total Cattle'].toString()),
            _buildSummaryRow('Active', summaryData['Active'].toString()),
            _buildSummaryRow('Inactive', summaryData['Inactive'].toString()),
            _buildSummaryRow('Average Age (months)', summaryData['Average Age (months)'].toString()),
            _buildSummaryRow('Total Weight (kg)', summaryData['Total Weight (kg)'].toString()),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.blue,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryChart(List<ChartDataIsar> chartData) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Cattle by Category',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 300,
              child: PieChart(
                PieChartData(
                  sections: chartData.map((data) {
                    return PieChartSectionData(
                      value: data.value ?? 0,
                      title: '${data.label ?? ""}\n${(data.value ?? 0).toInt()}',
                      color: Color(data.colorValue ?? Colors.grey.value), // ignore: deprecated_member_use (value is correct for storing ARGB int)
                      radius: 100,
                      titleStyle: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    );
                  }).toList(),
                  sectionsSpace: 2,
                  centerSpaceRadius: 40,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
