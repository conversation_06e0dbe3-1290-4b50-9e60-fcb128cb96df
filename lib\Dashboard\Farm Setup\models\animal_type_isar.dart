import 'package:flutter/material.dart';
import 'package:isar/isar.dart';
import 'package:uuid/uuid.dart';
import '../../../constants/app_icons.dart';

part 'animal_type_isar.g.dart';

/// Represents a type of animal in the system (cattle, sheep, goat, etc.)
@collection
class AnimalTypeIsar {
  /// Isar database ID (auto-incremented)
  Id id = Isar.autoIncrement;

  /// Business ID (UUID) for the animal type - indexed for quick lookup
  @Index(unique: true)
  String? businessId;

  /// Name of the animal type
  @Index(caseSensitive: false)
  String? name;

  /// Icon code point to represent this animal type in the UI
  int? iconCodePoint;

  /// Icon font family
  String? iconFontFamily;

  /// Color value for the animal type
  int? colorValue;

  /// Default gestation period in days
  int? defaultGestationDays;

  /// Default heat cycle period in days
  int? defaultHeatCycleDays;

  /// Default empty period in days (optional)
  int? defaultEmptyPeriodDays;

  /// Default breeding age in months
  int? defaultBreedingAge;

  /// Date when the record was created
  DateTime? createdAt;

  /// Date when the record was last updated
  DateTime? updatedAt;

  /// Default constructor for Isar
  AnimalTypeIsar();

  /// Static constant IDs for predefined animal types to avoid ID regeneration on reinstall
  /// Format: type_[name] - simple, readable, and easy to use
  static const Map<String, String> staticIds = {
    'cow': 'type_cow',
    'buffalo': 'type_buffalo',
    'goat': 'type_goat',
    'sheep': 'type_sheep',
    'horse': 'type_horse',
    // Add new types below with the same format
  };

  /// Direct access to specific type IDs for easier reference
  static const String cowId = 'type_cow';
  static const String buffaloId = 'type_buffalo';
  static const String goatId = 'type_goat';
  static const String sheepId = 'type_sheep';
  static const String horseId = 'type_horse';

  /// Get all animal type IDs
  /// Returns a list of all animal type IDs
  static List<String> getAllTypeIds() {
    return staticIds.values.toList();
  }

  /// Get an animal type ID by name
  /// Returns the animal type ID if found, null otherwise
  static String? getTypeId(String typeName) {
    final normalizedName = typeName.toLowerCase();
    return staticIds[normalizedName];
  }

  /// Check if an animal type exists
  /// Returns true if the animal type exists, false otherwise
  static bool typeExists(String typeName) {
    final normalizedName = typeName.toLowerCase();
    return staticIds.containsKey(normalizedName);
  }

  /// Named constructor for creating an AnimalType with values
  factory AnimalTypeIsar.create({
    required String name,
    required IconData icon,
    Color color = Colors.green,
    required int defaultGestationDays,
    required int defaultHeatCycleDays,
    int? defaultEmptyPeriodDays,
    int? defaultBreedingAge,
    String? businessId,
  }) {
    final String normalizedName = name.toLowerCase();
    // Use provided businessId, or a static ID if the animal type is predefined, otherwise generate a new UUID
    final String id = businessId ?? staticIds[normalizedName] ?? const Uuid().v4();

    return AnimalTypeIsar()
      ..businessId = id
      ..name = name
      ..iconCodePoint = icon.codePoint
      ..iconFontFamily = icon.fontFamily
      ..colorValue = color.value // ignore: deprecated_member_use (value is correct for storing ARGB int)
      ..defaultGestationDays = defaultGestationDays
      ..defaultHeatCycleDays = defaultHeatCycleDays
      ..defaultEmptyPeriodDays = defaultEmptyPeriodDays
      ..defaultBreedingAge = defaultBreedingAge
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();
  }

  /// Get the icon for this animal type
  @ignore
  IconData get icon {
    // First check if we have a valid stored icon code point
    if (iconCodePoint != null) {
      // If this is a valid explicitly selected custom icon, use it directly
      return IconData(
        iconCodePoint!,
        fontFamily: iconFontFamily ?? 'MaterialIcons',
      );
    }

    // If no valid stored icon, try to get appropriate icon by name
    if (name != null) {
      return AppIcons.getAnimalIcon(name);
    }

    // Ultimate fallback
    return AppIcons.defaultIcon;
  }

  /// Set the icon data
  set icon(IconData value) {
    iconCodePoint = value.codePoint;
    iconFontFamily = value.fontFamily;
  }

  /// Get color for this animal type
  @ignore
  Color get color {
    // First use stored color value if it exists
    if (colorValue != null) {
      // print('AnimalType $name using stored color: ${Color(colorValue!)}'); // Removed print
      return Color(colorValue!);
    }

    // Ultimate fallback
    // print('AnimalType $name using default color'); // Removed print
    return const Color(0xFF2E7D32);
  }

  /// Set the color
  set color(Color value) {
    // print('Setting color for AnimalType $name: $value'); // Removed print
    colorValue = value.value; // ignore: deprecated_member_use (value is correct for storing ARGB int)
  }

  factory AnimalTypeIsar.fromMap(Map<String, dynamic> map) {
    final animalType = AnimalTypeIsar()
      ..businessId = map['id'] as String?
      ..name = map['name'] as String?
      ..defaultGestationDays = map['defaultGestationDays'] as int?
      ..defaultHeatCycleDays = map['defaultHeatCycleDays'] as int?
      ..defaultEmptyPeriodDays = map['defaultEmptyPeriodDays'] as int?
      ..defaultBreedingAge = map['defaultBreedingAge'] as int?;

    // Parse icon data
    if (map['iconCodePoint'] != null) {
      animalType.iconCodePoint = map['iconCodePoint'] as int;
      animalType.iconFontFamily =
          map['iconFontFamily'] as String? ?? 'MaterialIcons';
    }

    // Parse color
    if (map['color'] != null) {
      animalType.colorValue = int.parse(map['color'].toString());
    }

    // Parse dates
    if (map['createdAt'] != null) {
      animalType.createdAt = DateTime.parse(map['createdAt'] as String);
    }

    if (map['updatedAt'] != null) {
      animalType.updatedAt = DateTime.parse(map['updatedAt'] as String);
    }

    return animalType;
  }

  Map<String, dynamic> toMap() {
    return {
      'id': businessId,
      'name': name,
      'iconCodePoint': iconCodePoint,
      'iconFontFamily': iconFontFamily,
      'color': colorValue,
      'defaultGestationDays': defaultGestationDays,
      'defaultHeatCycleDays': defaultHeatCycleDays,
      'defaultEmptyPeriodDays': defaultEmptyPeriodDays,
      'defaultBreedingAge': defaultBreedingAge,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  AnimalTypeIsar copyWith({
    String? businessId,
    String? name,
    IconData? icon,
    Color? color,
    int? defaultGestationDays,
    int? defaultHeatCycleDays,
    int? defaultEmptyPeriodDays,
    int? defaultBreedingAge,
  }) {
    final result = AnimalTypeIsar()
      ..id = id
      ..businessId = businessId ?? this.businessId
      ..name = name ?? this.name
      ..defaultGestationDays = defaultGestationDays ?? this.defaultGestationDays
      ..defaultHeatCycleDays = defaultHeatCycleDays ?? this.defaultHeatCycleDays
      ..defaultEmptyPeriodDays =
          defaultEmptyPeriodDays ?? this.defaultEmptyPeriodDays
      ..defaultBreedingAge = defaultBreedingAge ?? this.defaultBreedingAge
      ..createdAt = createdAt
      ..updatedAt = DateTime.now();

    if (icon != null) {
      result.icon = icon;
    } else if (iconCodePoint != null) {
      result.iconCodePoint = iconCodePoint;
      result.iconFontFamily = iconFontFamily;
    }

    if (color != null) {
      result.color = color;
    } else if (colorValue != null) {
      result.colorValue = colorValue;
    }

    return result;
  }

  // Default icon for animal types
  @ignore
  static IconData get defaultIcon => AppIcons.defaultIcon;

  /// Simple JSON serialization without using generated code
  Map<String, dynamic> toJson() => toMap();

  /// Simple JSON deserialization without using generated code
  factory AnimalTypeIsar.fromJson(Map<String, dynamic> json) =>
      AnimalTypeIsar.fromMap(json);
}