/*
import 'package:flutter/material.dart';
import '../models/milk_record_isar.dart';
import '../../Cattle/models/cattle_isar.dart';

import 'milk_details_screen.dart';
import 'package:intl/intl.dart';
import '../../../constants/app_tabs.dart';

class MilkDetailsRecordsTab extends StatefulWidget {
  final MilkRecordIsar milkRecord;
  final CattleIsar? cattle;
  final List<MilkRecordIsar> relatedRecords;

  const MilkDetailsRecordsTab({
    Key? key,
    required this.milkRecord,
    this.cattle,
    required this.relatedRecords,
  }) : super(key: key);

  @override
  State<MilkDetailsRecordsTab> createState() => _MilkDetailsRecordsTabState();
}

class _MilkDetailsRecordsTabState extends State<MilkDetailsRecordsTab>
    with AutomaticKeepAliveClientMixin {

  String _searchQuery = '';
  String _selectedSession = 'All';

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    final filteredRecords = _getFilteredRecords();

    return Column(
      children: [
        // Search and Filter Section
        _buildSearchAndFilters(),
        
        // Records List
        Expanded(
          child: filteredRecords.isEmpty
              ? UniversalEmptyState.milk(
                  title: _searchQuery.isNotEmpty || _selectedSession != 'All'
                      ? 'No Matching Records'
                      : 'No Milk Records',
                  message: _searchQuery.isNotEmpty || _selectedSession != 'All'
                      ? 'Try adjusting your search or filter criteria'
                      : 'Milk production records for ${widget.cattle?.name ?? 'this cattle'} will appear here',
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16.0),
                  itemCount: filteredRecords.length,
                  itemBuilder: (context, index) {
                    final record = filteredRecords[index];
                    final isCurrentRecord = record.businessId == widget.milkRecord.businessId;
                    return _buildRecordCard(record, isCurrentRecord);
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildSearchAndFilters() {
    final sessions = ['All', ...widget.relatedRecords
        .map((r) => r.session ?? 'Unknown')
        .toSet()
        .toList()..sort()];

    return Card(
      margin: const EdgeInsets.all(16.0),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Search Bar
            TextField(
              decoration: const InputDecoration(
                hintText: 'Search milk records...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() => _searchQuery = value);
              },
            ),
            const SizedBox(height: 16),
            
            // Session Filter
            DropdownButtonFormField<String>(
              value: _selectedSession,
              decoration: const InputDecoration(
                labelText: 'Session',
                border: OutlineInputBorder(),
              ),
              items: sessions.map((session) => DropdownMenuItem(
                value: session,
                child: Text(session),
              )).toList(),
              onChanged: (value) {
                setState(() => _selectedSession = value ?? 'All');
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecordCard(MilkRecordIsar record, bool isCurrentRecord) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: isCurrentRecord ? 4 : 1,
      color: isCurrentRecord ? UniversalEmptyStateTheme.milk.withValues(alpha: 0.1) : null,
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: UniversalEmptyStateTheme.milk,
          child: const Icon(
            Icons.water_drop,
            color: Colors.white,
            size: 20,
          ),
        ),
        title: Row(
          children: [
            Expanded(
              child: Text(
                '${record.quantity?.toStringAsFixed(1) ?? '0.0'}L',
                style: TextStyle(
                  fontWeight: isCurrentRecord ? FontWeight.bold : FontWeight.normal,
                  fontSize: 16,
                ),
              ),
            ),
            if (isCurrentRecord)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: UniversalEmptyStateTheme.milk,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'Current',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Date: ${_formatDate(record.date)}'),
            if (record.session != null)
              Text('Session: ${record.session}'),
            if (record.notes != null && record.notes!.isNotEmpty)
              Text(
                'Notes: ${record.notes}',
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
          ],
        ),
        trailing: Text(
          '${record.quantity?.toStringAsFixed(1) ?? '0.0'}L',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
            color: UniversalEmptyStateTheme.milk,
          ),
        ),
        onTap: isCurrentRecord ? null : () {
          // Navigate to this record's detail screen
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => MilkDetailScreen(
                milkRecord: record,
                title: 'Milk Record',
              ),
            ),
          );
        },
      ),
    );
  }

  List<MilkRecordIsar> _getFilteredRecords() {
    List<MilkRecordIsar> filtered = List.from(widget.relatedRecords);

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((record) {
        final searchLower = _searchQuery.toLowerCase();
        return (record.session?.toLowerCase().contains(searchLower) ?? false) ||
               (record.notes?.toLowerCase().contains(searchLower) ?? false) ||
               (record.quantity?.toString().contains(searchLower) ?? false);
      }).toList();
    }

    // Apply session filter
    if (_selectedSession != 'All') {
      filtered = filtered.where((record) => record.session == _selectedSession).toList();
    }

    // Sort by date (newest first)
    filtered.sort((a, b) => (b.date ?? DateTime.now()).compareTo(a.date ?? DateTime.now()));

    return filtered;
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'Unknown';
    return DateFormat('MMM dd, yyyy').format(date);
  }
}
*/
