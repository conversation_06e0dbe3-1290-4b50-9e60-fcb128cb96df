import 'package:isar/isar.dart';

part 'pregnancy_record_isar.g.dart';

/// Represents a pregnancy record in the Isar database
@collection
class PregnancyRecordIsar {
  /// Isar database ID
  Id id = Isar.autoIncrement;

  /// Business ID (UUID) for the pregnancy record - indexed for quick lookup
  @Index(unique: true)
  String? businessId;

  /// ID of the cattle that is pregnant
  @Index(type: IndexType.value)
  String? cattleId;

  /// Date when the pregnancy started
  DateTime? startDate;

  /// Date when the pregnancy ended
  DateTime? endDate;

  /// Current status of the pregnancy (Active, Completed, Aborted, etc.)
  @Index()
  String? status;

  /// Associated breeding record business ID
  @Index()
  String? breedingRecordId;

  /// Expected calving date
  DateTime? expectedCalvingDate;

  /// Actual calving date (once completed)
  DateTime? actualCalvingDate;

  /// Additional notes about the pregnancy
  String? notes;

  /// Creation timestamp
  DateTime? createdAt;

  /// Last update timestamp
  DateTime? updatedAt;

  /// Default constructor for Isar
  PregnancyRecordIsar();

  /// Generate a formatted ID for pregnancy records in the format 'cattleId-Pregnancy-sequenceNumber'
  static String generateFormattedId(String cattleId, int sequenceNumber) {
    return '$cattleId-Pregnancy-$sequenceNumber';
  }

  /// Factory constructor for creating a new pregnancy record
  factory PregnancyRecordIsar.create({
    required String cattleId,
    required DateTime startDate,
    required String status,
    String? breedingRecordId,
    DateTime? expectedCalvingDate,
    DateTime? actualCalvingDate,
    DateTime? endDate,
    String? notes,
    String? businessId,
  }) {
    return PregnancyRecordIsar()
      ..businessId = businessId ??
          generateFormattedId(cattleId,
              1) // Default to sequence 1, should be overridden by caller
      ..cattleId = cattleId
      ..startDate = startDate
      ..endDate = endDate
      ..status = status
      ..breedingRecordId = breedingRecordId
      ..expectedCalvingDate = expectedCalvingDate
      ..actualCalvingDate = actualCalvingDate
      ..notes = notes
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();
  }

  /// Convert to a map for serialization
  Map<String, dynamic> toMap() {
    return {
      'id': businessId,
      'cattleId': cattleId,
      'startDate': startDate?.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'status': status,
      'breedingRecordId': breedingRecordId,
      'expectedCalvingDate': expectedCalvingDate?.toIso8601String(),
      'actualCalvingDate': actualCalvingDate?.toIso8601String(),
      'notes': notes,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  /// Alias for toMap
  Map<String, dynamic> toJson() => toMap();

  /// Create from a map
  factory PregnancyRecordIsar.fromMap(Map<String, dynamic> map) {
    return PregnancyRecordIsar()
      ..businessId = map['id'] as String?
      ..cattleId = map['cattleId'] as String?
      ..startDate = map['startDate'] != null
          ? map['startDate'] is String
              ? DateTime.parse(map['startDate'])
              : map['startDate'] as DateTime
          : null
      ..endDate = map['endDate'] != null
          ? map['endDate'] is String
              ? DateTime.parse(map['endDate'])
              : map['endDate'] as DateTime
          : null
      ..status = map['status'] as String?
      ..breedingRecordId = map['breedingRecordId'] as String?
      ..expectedCalvingDate = map['expectedCalvingDate'] != null
          ? map['expectedCalvingDate'] is String
              ? DateTime.parse(map['expectedCalvingDate'])
              : map['expectedCalvingDate'] as DateTime
          : null
      ..actualCalvingDate = map['actualCalvingDate'] != null
          ? map['actualCalvingDate'] is String
              ? DateTime.parse(map['actualCalvingDate'])
              : map['actualCalvingDate'] as DateTime
          : null
      ..notes = map['notes'] as String?
      ..createdAt = map['createdAt'] != null
          ? map['createdAt'] is String
              ? DateTime.parse(map['createdAt'])
              : map['createdAt'] as DateTime
          : DateTime.now()
      ..updatedAt = map['updatedAt'] != null
          ? map['updatedAt'] is String
              ? DateTime.parse(map['updatedAt'])
              : map['updatedAt'] as DateTime
          : DateTime.now();
  }

  /// Create from JSON
  factory PregnancyRecordIsar.fromJson(Map<String, dynamic> json) =>
      PregnancyRecordIsar.fromMap(json);

  /// Create a copy with updated fields
  PregnancyRecordIsar copyWith({
    String? businessId,
    String? cattleId,
    DateTime? startDate,
    DateTime? endDate,
    String? status,
    String? breedingRecordId,
    DateTime? expectedCalvingDate,
    DateTime? actualCalvingDate,
    String? notes,
  }) {
    return PregnancyRecordIsar()
      ..id = id
      ..businessId = businessId ?? this.businessId
      ..cattleId = cattleId ?? this.cattleId
      ..startDate = startDate ?? this.startDate
      ..endDate = endDate ?? this.endDate
      ..status = status ?? this.status
      ..breedingRecordId = breedingRecordId ?? this.breedingRecordId
      ..expectedCalvingDate = expectedCalvingDate ?? this.expectedCalvingDate
      ..actualCalvingDate = actualCalvingDate ?? this.actualCalvingDate
      ..notes = notes ?? this.notes
      ..createdAt = createdAt
      ..updatedAt = DateTime.now();
  }

  /// Operator to access properties using map-like syntax
  dynamic operator [](String key) {
    switch (key) {
      case 'id':
        return businessId;
      case 'cattleId':
        return cattleId;
      case 'startDate':
        return startDate;
      case 'endDate':
        return endDate;
      case 'status':
        return status;
      case 'breedingRecordId':
        return breedingRecordId;
      case 'expectedCalvingDate':
        return expectedCalvingDate;
      case 'actualCalvingDate':
        return actualCalvingDate;
      case 'notes':
        return notes;
      case 'createdAt':
        return createdAt;
      case 'updatedAt':
        return updatedAt;
      default:
        return null;
    }
  }
}
