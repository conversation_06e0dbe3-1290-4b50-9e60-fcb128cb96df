import 'package:flutter/foundation.dart';
import '../../../constants/app_tabs.dart';
import '../../Cattle/controllers/cattle_controller.dart'; // For ControllerState enum

/// Screen State Mapper Mixin
/// 
/// Provides a shared method to map controller states to screen states,
/// eliminating code duplication across all module screens.
/// 
/// Usage:
/// ```dart
/// class _ScreenContentState extends State<_ScreenContent>
///     with TickerProviderStateMixin, UniversalScreenState, ScreenStateMapper {
///   
///   @override
///   Widget build(BuildContext context) {
///     return UniversalStateBuilder(
///       state: getScreenStateFromController(controller),
///       // ... rest of the widget
///     );
///   }
/// }
/// ```
mixin ScreenStateMapper<T extends StatefulWidget> on State<T> {
  /// Maps controller state to screen state for UniversalStateBuilder
  /// This method is used consistently across all module screens
  ScreenState getScreenStateFromController(ChangeNotifier controller) {
    // Use dynamic typing to access the state property
    final dynamic dynamicController = controller;
    
    try {
      final ControllerState controllerState = dynamicController.state as ControllerState;
      
      switch (controllerState) {
        case ControllerState.initial:
          return ScreenState.initial;
        case ControllerState.loading:
          return ScreenState.loading;
        case ControllerState.loaded:
          return ScreenState.loaded;
        case ControllerState.error:
          return ScreenState.error;
        case ControllerState.empty:
          return ScreenState.empty;
      }
    } catch (e) {
      // Fallback for controllers that don't have a state property
      // (like WeightController which uses isLoading/error pattern)
      debugPrint('Warning: Controller does not have a state property: $e');
      
      // Try to access common properties for fallback
      try {
        final dynamic fallbackController = controller;
        final bool? isLoading = fallbackController.isLoading as bool?;
        final String? error = fallbackController.error as String?;
        
        if (isLoading == true) return ScreenState.loading;
        if (error != null) return ScreenState.error;
        return ScreenState.loaded;
      } catch (fallbackError) {
        debugPrint('Warning: Could not determine controller state: $fallbackError');
        return ScreenState.loaded; // Safe fallback
      }
    }
  }
}
