/*
import 'package:flutter/material.dart';
import '../models/currency_settings_isar.dart';
import '../services/farm_setup_handler.dart';
import '../../../constants/currencies.dart';
import '../../../constants/app_colors.dart';

// Define spacing constants for consistency
const double kSpacing = 8.0;
const double kSpacingMedium = 16.0;
const double kSpacingLarge = 24.0;

class CurrencySetupScreen extends StatefulWidget {
  const CurrencySetupScreen({super.key});

  @override
  State<CurrencySetupScreen> createState() => _CurrencySetupScreenState();
}

class _CurrencySetupScreenState extends State<CurrencySetupScreen> {
  final _formKey = GlobalKey<FormState>();
  final FarmSetupHandler _farmSetupHandler = FarmSetupHandler.instance;
  late CurrencySettingsIsar _settings;
  bool _isLoading = true;

  // Map for O(1) symbol lookup
  late Map<String, String> _symbolMap;

  @override
  void initState() {
    super.initState();
    // Initialize symbol lookup map
    _symbolMap = {
      for (var currency in kAvailableCurrencies)
        currency['code']!: currency['symbol']!
    };
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      debugPrint('Loading currency settings...');
      _settings = await _farmSetupHandler.getCurrencySettings();
      debugPrint(
          'Loaded currency settings - Farm ID: ${_settings.farmId}, Code: ${_settings.currencyCode}, Symbol: ${_settings.currencySymbol}, ID: ${_settings.id}');
      setState(() => _isLoading = false);
    } catch (e) {
      debugPrint('Error loading currency settings: $e');
      // Create default settings if none exist
      _settings = CurrencySettingsIsar()
        ..currencyCode = 'USD'
        ..currencySymbol = '\$'
        ..symbolBeforeAmount = true
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now();
      debugPrint('Created default settings: ${_settings.currencyCode}');
      setState(() => _isLoading = false);
    }
  }

  Future<void> _saveSettings() async {
    if (_formKey.currentState!.validate()) {
      try {
        final selectedFarmId = await _farmSetupHandler.getSelectedFarmId();
        debugPrint('Saving currency settings - Farm ID: $selectedFarmId');
        debugPrint(
            'Before save - Currency: ${_settings.currencyCode}, Symbol: ${_settings.currencySymbol}');

        // Create a completely new settings object to avoid any reference issues
        final updatedSettings = CurrencySettingsIsar()
          ..id = _settings.id // Keep the same ID if it exists
          ..farmId = selectedFarmId
          ..currencyCode = _settings.currencyCode
          ..currencySymbol = _settings.currencySymbol
          ..symbolBeforeAmount = _settings.symbolBeforeAmount
          ..createdAt = _settings.createdAt ?? DateTime.now()
          ..updatedAt = DateTime.now();

        // Save the new settings object
        await _farmSetupHandler.saveCurrencySettings(updatedSettings);

        // Update our local settings to match what was saved
        setState(() {
          _settings = updatedSettings;
        });

        debugPrint(
            'After save - Currency: ${updatedSettings.currencyCode}, Symbol: ${updatedSettings.currencySymbol}');

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Currency settings saved successfully'),
              backgroundColor: AppColors.success,
            ),
          );
        }
      } catch (e) {
        debugPrint('Error saving currency settings: $e');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error saving settings: $e'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    }
  }

  String _getPreviewText(bool beforeAmount) {
    return beforeAmount
        ? '${_settings.currencySymbol}100'
        : '100${_settings.currencySymbol}';
  }

  @override
  Widget build(BuildContext context) {
    // Get theme data for consistent styling
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final colorScheme = theme.colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Currency Setup',
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: AppColors.primary,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(color: AppColors.primary))
          : SingleChildScrollView(
              child: Container(
                color: colorScheme.surfaceContainerHighest.withAlpha(77),
                child: Padding(
                  padding: const EdgeInsets.all(kSpacingMedium),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        // Currency Selection Card
                        Card(
                          elevation: 1,
                          margin: EdgeInsets.zero,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(kSpacingMedium),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Card title with icon
                                Row(
                                  children: [
                                    const Icon(Icons.language,
                                        color: AppColors.primary, size: 20),
                                    const SizedBox(width: kSpacing),
                                    Text(
                                      'Select Currency',
                                      style: textTheme.titleLarge?.copyWith(
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: kSpacingMedium),
                                DropdownButtonFormField<String>(
                                  value: _settings.currencyCode,
                                  isExpanded: true,
                                  decoration: InputDecoration(
                                    labelText: 'Currency',
                                    prefixIcon: Icon(Icons.money,
                                        color:
                                            AppColors.primary.withAlpha(179)),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                      borderSide: BorderSide(
                                          color: Colors.grey.shade400),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                      borderSide: const BorderSide(
                                          color: AppColors.primary, width: 2.0),
                                    ),
                                    contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 16,
                                    ),
                                  ),
                                  items: kAvailableCurrencies.map((currency) {
                                    return DropdownMenuItem<String>(
                                      value: currency['code'],
                                      child: Text(
                                        '${currency['code']} - ${currency['name']} (${currency['symbol']})',
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    );
                                  }).toList(),
                                  onChanged: (value) {
                                    if (value != null) {
                                      debugPrint('Currency changed to: $value');
                                      setState(() {
                                        _settings.currencyCode = value;
                                        // Use O(1) lookup from the map
                                        _settings.currencySymbol =
                                            _symbolMap[value]!;
                                        _settings.updatedAt = DateTime.now();
                                      });
                                      debugPrint(
                                          'Updated settings - Code: ${_settings.currencyCode}, Symbol: ${_settings.currencySymbol}');
                                    }
                                  },
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: kSpacingMedium),

                        // Symbol Position Card
                        Card(
                          elevation: 1,
                          margin: EdgeInsets.zero,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(kSpacingMedium),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Card title with icon
                                Row(
                                  children: [
                                    const Icon(Icons.compare_arrows,
                                        color: AppColors.primary, size: 20),
                                    const SizedBox(width: kSpacing),
                                    Text(
                                      'Symbol Position',
                                      style: textTheme.titleLarge?.copyWith(
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: kSpacingMedium),
                                Row(
                                  children: [
                                    Expanded(
                                      child: RadioListTile<bool>(
                                        title: const Text('Before Amount'),
                                        value: true,
                                        groupValue:
                                            _settings.symbolBeforeAmount,
                                        activeColor: AppColors.primary,
                                        onChanged: (value) {
                                          if (value != null) {
                                            setState(() {
                                              _settings.symbolBeforeAmount =
                                                  value;
                                              _settings.updatedAt =
                                                  DateTime.now();
                                            });
                                          }
                                        },
                                      ),
                                    ),
                                    Expanded(
                                      child: RadioListTile<bool>(
                                        title: const Text('After Amount'),
                                        value: false,
                                        groupValue:
                                            _settings.symbolBeforeAmount,
                                        activeColor: AppColors.primary,
                                        onChanged: (value) {
                                          if (value != null) {
                                            setState(() {
                                              _settings.symbolBeforeAmount =
                                                  value;
                                              _settings.updatedAt =
                                                  DateTime.now();
                                            });
                                          }
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: kSpacingMedium),

                                // Enhanced Preview Container
                                Container(
                                  width: double.infinity,
                                  padding: const EdgeInsets.all(kSpacingMedium),
                                  decoration: BoxDecoration(
                                    color: AppColors.primary.withAlpha(13),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                        color: AppColors.primary.withAlpha(51)),
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          const Icon(Icons.visibility_outlined,
                                              size: 18,
                                              color: AppColors.primary),
                                          const SizedBox(width: kSpacing),
                                          Text(
                                            'Preview',
                                            style: textTheme.titleMedium,
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: kSpacing),
                                      Center(
                                        child: Text(
                                          _getPreviewText(
                                              _settings.symbolBeforeAmount),
                                          style: textTheme.headlineMedium
                                              ?.copyWith(
                                            fontWeight: FontWeight.bold,
                                            color: AppColors.primary,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: kSpacingLarge),

                        // Enhanced Save Button
                        ElevatedButton.icon(
                          icon: const Icon(Icons.save_alt_outlined,
                              color: Colors.white),
                          label: const Text('Save Settings'),
                          onPressed: _saveSettings,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primary,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                                vertical: kSpacingMedium),
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12)),
                            minimumSize: const Size(double.infinity, 50),
                            textStyle: textTheme.labelLarge
                                ?.copyWith(fontWeight: FontWeight.bold),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
    );
  }
}
*/
