// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_settings_isar.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetNotificationSettingsIsarCollection on Isar {
  IsarCollection<NotificationSettingsIsar> get notificationSettingsIsars =>
      this.collection();
}

const NotificationSettingsIsarSchema = CollectionSchema(
  name: r'NotificationSettingsIsar',
  id: 7435365505411647902,
  properties: {
    r'autoDeleteReadAfterDays': PropertySchema(
      id: 0,
      name: r'autoDeleteReadAfterDays',
      type: IsarType.long,
    ),
    r'breedingNotificationsEnabled': PropertySchema(
      id: 1,
      name: r'breedingNotificationsEnabled',
      type: IsarType.bool,
    ),
    r'emailNotificationsEnabled': PropertySchema(
      id: 2,
      name: r'emailNotificationsEnabled',
      type: IsarType.bool,
    ),
    r'eventsNotificationsEnabled': PropertySchema(
      id: 3,
      name: r'eventsNotificationsEnabled',
      type: IsarType.bool,
    ),
    r'healthNotificationsEnabled': PropertySchema(
      id: 4,
      name: r'healthNotificationsEnabled',
      type: IsarType.bool,
    ),
    r'inAppNotificationsEnabled': PropertySchema(
      id: 5,
      name: r'inAppNotificationsEnabled',
      type: IsarType.bool,
    ),
    r'maxNotificationsToKeep': PropertySchema(
      id: 6,
      name: r'maxNotificationsToKeep',
      type: IsarType.long,
    ),
    r'milkNotificationsEnabled': PropertySchema(
      id: 7,
      name: r'milkNotificationsEnabled',
      type: IsarType.bool,
    ),
    r'notificationsEnabled': PropertySchema(
      id: 8,
      name: r'notificationsEnabled',
      type: IsarType.bool,
    ),
    r'pushNotificationsEnabled': PropertySchema(
      id: 9,
      name: r'pushNotificationsEnabled',
      type: IsarType.bool,
    ),
    r'smsNotificationsEnabled': PropertySchema(
      id: 10,
      name: r'smsNotificationsEnabled',
      type: IsarType.bool,
    ),
    r'updatedAt': PropertySchema(
      id: 11,
      name: r'updatedAt',
      type: IsarType.dateTime,
    )
  },
  estimateSize: _notificationSettingsIsarEstimateSize,
  serialize: _notificationSettingsIsarSerialize,
  deserialize: _notificationSettingsIsarDeserialize,
  deserializeProp: _notificationSettingsIsarDeserializeProp,
  idName: r'id',
  indexes: {},
  links: {},
  embeddedSchemas: {},
  getId: _notificationSettingsIsarGetId,
  getLinks: _notificationSettingsIsarGetLinks,
  attach: _notificationSettingsIsarAttach,
  version: '3.1.0+1',
);

int _notificationSettingsIsarEstimateSize(
  NotificationSettingsIsar object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  return bytesCount;
}

void _notificationSettingsIsarSerialize(
  NotificationSettingsIsar object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeLong(offsets[0], object.autoDeleteReadAfterDays);
  writer.writeBool(offsets[1], object.breedingNotificationsEnabled);
  writer.writeBool(offsets[2], object.emailNotificationsEnabled);
  writer.writeBool(offsets[3], object.eventsNotificationsEnabled);
  writer.writeBool(offsets[4], object.healthNotificationsEnabled);
  writer.writeBool(offsets[5], object.inAppNotificationsEnabled);
  writer.writeLong(offsets[6], object.maxNotificationsToKeep);
  writer.writeBool(offsets[7], object.milkNotificationsEnabled);
  writer.writeBool(offsets[8], object.notificationsEnabled);
  writer.writeBool(offsets[9], object.pushNotificationsEnabled);
  writer.writeBool(offsets[10], object.smsNotificationsEnabled);
  writer.writeDateTime(offsets[11], object.updatedAt);
}

NotificationSettingsIsar _notificationSettingsIsarDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = NotificationSettingsIsar(
    autoDeleteReadAfterDays: reader.readLongOrNull(offsets[0]) ?? 30,
    breedingNotificationsEnabled: reader.readBoolOrNull(offsets[1]) ?? true,
    emailNotificationsEnabled: reader.readBoolOrNull(offsets[2]) ?? false,
    eventsNotificationsEnabled: reader.readBoolOrNull(offsets[3]) ?? true,
    healthNotificationsEnabled: reader.readBoolOrNull(offsets[4]) ?? true,
    inAppNotificationsEnabled: reader.readBoolOrNull(offsets[5]) ?? true,
    maxNotificationsToKeep: reader.readLongOrNull(offsets[6]) ?? 100,
    milkNotificationsEnabled: reader.readBoolOrNull(offsets[7]) ?? true,
    notificationsEnabled: reader.readBoolOrNull(offsets[8]) ?? true,
    pushNotificationsEnabled: reader.readBoolOrNull(offsets[9]) ?? false,
    smsNotificationsEnabled: reader.readBoolOrNull(offsets[10]) ?? false,
    updatedAt: reader.readDateTimeOrNull(offsets[11]),
  );
  object.id = id;
  return object;
}

P _notificationSettingsIsarDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readLongOrNull(offset) ?? 30) as P;
    case 1:
      return (reader.readBoolOrNull(offset) ?? true) as P;
    case 2:
      return (reader.readBoolOrNull(offset) ?? false) as P;
    case 3:
      return (reader.readBoolOrNull(offset) ?? true) as P;
    case 4:
      return (reader.readBoolOrNull(offset) ?? true) as P;
    case 5:
      return (reader.readBoolOrNull(offset) ?? true) as P;
    case 6:
      return (reader.readLongOrNull(offset) ?? 100) as P;
    case 7:
      return (reader.readBoolOrNull(offset) ?? true) as P;
    case 8:
      return (reader.readBoolOrNull(offset) ?? true) as P;
    case 9:
      return (reader.readBoolOrNull(offset) ?? false) as P;
    case 10:
      return (reader.readBoolOrNull(offset) ?? false) as P;
    case 11:
      return (reader.readDateTimeOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _notificationSettingsIsarGetId(NotificationSettingsIsar object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _notificationSettingsIsarGetLinks(
    NotificationSettingsIsar object) {
  return [];
}

void _notificationSettingsIsarAttach(
    IsarCollection<dynamic> col, Id id, NotificationSettingsIsar object) {
  object.id = id;
}

extension NotificationSettingsIsarQueryWhereSort on QueryBuilder<
    NotificationSettingsIsar, NotificationSettingsIsar, QWhere> {
  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterWhere>
      anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension NotificationSettingsIsarQueryWhere on QueryBuilder<
    NotificationSettingsIsar, NotificationSettingsIsar, QWhereClause> {
  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterWhereClause> idEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterWhereClause> idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterWhereClause> idGreaterThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterWhereClause> idLessThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterWhereClause> idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension NotificationSettingsIsarQueryFilter on QueryBuilder<
    NotificationSettingsIsar, NotificationSettingsIsar, QFilterCondition> {
  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> autoDeleteReadAfterDaysEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'autoDeleteReadAfterDays',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> autoDeleteReadAfterDaysGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'autoDeleteReadAfterDays',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> autoDeleteReadAfterDaysLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'autoDeleteReadAfterDays',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> autoDeleteReadAfterDaysBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'autoDeleteReadAfterDays',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> breedingNotificationsEnabledEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'breedingNotificationsEnabled',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> emailNotificationsEnabledEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'emailNotificationsEnabled',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> eventsNotificationsEnabledEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'eventsNotificationsEnabled',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> healthNotificationsEnabledEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'healthNotificationsEnabled',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> idEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> inAppNotificationsEnabledEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'inAppNotificationsEnabled',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> maxNotificationsToKeepEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'maxNotificationsToKeep',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> maxNotificationsToKeepGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'maxNotificationsToKeep',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> maxNotificationsToKeepLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'maxNotificationsToKeep',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> maxNotificationsToKeepBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'maxNotificationsToKeep',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> milkNotificationsEnabledEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'milkNotificationsEnabled',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> notificationsEnabledEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'notificationsEnabled',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> pushNotificationsEnabledEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'pushNotificationsEnabled',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> smsNotificationsEnabledEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'smsNotificationsEnabled',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> updatedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> updatedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> updatedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> updatedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> updatedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar,
      QAfterFilterCondition> updatedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'updatedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension NotificationSettingsIsarQueryObject on QueryBuilder<
    NotificationSettingsIsar, NotificationSettingsIsar, QFilterCondition> {}

extension NotificationSettingsIsarQueryLinks on QueryBuilder<
    NotificationSettingsIsar, NotificationSettingsIsar, QFilterCondition> {}

extension NotificationSettingsIsarQuerySortBy on QueryBuilder<
    NotificationSettingsIsar, NotificationSettingsIsar, QSortBy> {
  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByAutoDeleteReadAfterDays() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoDeleteReadAfterDays', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByAutoDeleteReadAfterDaysDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoDeleteReadAfterDays', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByBreedingNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'breedingNotificationsEnabled', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByBreedingNotificationsEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'breedingNotificationsEnabled', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByEmailNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'emailNotificationsEnabled', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByEmailNotificationsEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'emailNotificationsEnabled', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByEventsNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'eventsNotificationsEnabled', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByEventsNotificationsEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'eventsNotificationsEnabled', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByHealthNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'healthNotificationsEnabled', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByHealthNotificationsEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'healthNotificationsEnabled', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByInAppNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'inAppNotificationsEnabled', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByInAppNotificationsEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'inAppNotificationsEnabled', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByMaxNotificationsToKeep() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'maxNotificationsToKeep', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByMaxNotificationsToKeepDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'maxNotificationsToKeep', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByMilkNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'milkNotificationsEnabled', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByMilkNotificationsEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'milkNotificationsEnabled', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notificationsEnabled', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByNotificationsEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notificationsEnabled', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByPushNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pushNotificationsEnabled', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByPushNotificationsEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pushNotificationsEnabled', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortBySmsNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'smsNotificationsEnabled', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortBySmsNotificationsEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'smsNotificationsEnabled', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      sortByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }
}

extension NotificationSettingsIsarQuerySortThenBy on QueryBuilder<
    NotificationSettingsIsar, NotificationSettingsIsar, QSortThenBy> {
  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByAutoDeleteReadAfterDays() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoDeleteReadAfterDays', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByAutoDeleteReadAfterDaysDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoDeleteReadAfterDays', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByBreedingNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'breedingNotificationsEnabled', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByBreedingNotificationsEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'breedingNotificationsEnabled', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByEmailNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'emailNotificationsEnabled', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByEmailNotificationsEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'emailNotificationsEnabled', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByEventsNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'eventsNotificationsEnabled', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByEventsNotificationsEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'eventsNotificationsEnabled', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByHealthNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'healthNotificationsEnabled', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByHealthNotificationsEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'healthNotificationsEnabled', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByInAppNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'inAppNotificationsEnabled', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByInAppNotificationsEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'inAppNotificationsEnabled', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByMaxNotificationsToKeep() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'maxNotificationsToKeep', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByMaxNotificationsToKeepDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'maxNotificationsToKeep', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByMilkNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'milkNotificationsEnabled', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByMilkNotificationsEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'milkNotificationsEnabled', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notificationsEnabled', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByNotificationsEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notificationsEnabled', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByPushNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pushNotificationsEnabled', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByPushNotificationsEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pushNotificationsEnabled', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenBySmsNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'smsNotificationsEnabled', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenBySmsNotificationsEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'smsNotificationsEnabled', Sort.desc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QAfterSortBy>
      thenByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }
}

extension NotificationSettingsIsarQueryWhereDistinct on QueryBuilder<
    NotificationSettingsIsar, NotificationSettingsIsar, QDistinct> {
  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QDistinct>
      distinctByAutoDeleteReadAfterDays() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'autoDeleteReadAfterDays');
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QDistinct>
      distinctByBreedingNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'breedingNotificationsEnabled');
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QDistinct>
      distinctByEmailNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'emailNotificationsEnabled');
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QDistinct>
      distinctByEventsNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'eventsNotificationsEnabled');
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QDistinct>
      distinctByHealthNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'healthNotificationsEnabled');
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QDistinct>
      distinctByInAppNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'inAppNotificationsEnabled');
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QDistinct>
      distinctByMaxNotificationsToKeep() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'maxNotificationsToKeep');
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QDistinct>
      distinctByMilkNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'milkNotificationsEnabled');
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QDistinct>
      distinctByNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'notificationsEnabled');
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QDistinct>
      distinctByPushNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'pushNotificationsEnabled');
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QDistinct>
      distinctBySmsNotificationsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'smsNotificationsEnabled');
    });
  }

  QueryBuilder<NotificationSettingsIsar, NotificationSettingsIsar, QDistinct>
      distinctByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'updatedAt');
    });
  }
}

extension NotificationSettingsIsarQueryProperty on QueryBuilder<
    NotificationSettingsIsar, NotificationSettingsIsar, QQueryProperty> {
  QueryBuilder<NotificationSettingsIsar, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<NotificationSettingsIsar, int, QQueryOperations>
      autoDeleteReadAfterDaysProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'autoDeleteReadAfterDays');
    });
  }

  QueryBuilder<NotificationSettingsIsar, bool, QQueryOperations>
      breedingNotificationsEnabledProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'breedingNotificationsEnabled');
    });
  }

  QueryBuilder<NotificationSettingsIsar, bool, QQueryOperations>
      emailNotificationsEnabledProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'emailNotificationsEnabled');
    });
  }

  QueryBuilder<NotificationSettingsIsar, bool, QQueryOperations>
      eventsNotificationsEnabledProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'eventsNotificationsEnabled');
    });
  }

  QueryBuilder<NotificationSettingsIsar, bool, QQueryOperations>
      healthNotificationsEnabledProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'healthNotificationsEnabled');
    });
  }

  QueryBuilder<NotificationSettingsIsar, bool, QQueryOperations>
      inAppNotificationsEnabledProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'inAppNotificationsEnabled');
    });
  }

  QueryBuilder<NotificationSettingsIsar, int, QQueryOperations>
      maxNotificationsToKeepProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'maxNotificationsToKeep');
    });
  }

  QueryBuilder<NotificationSettingsIsar, bool, QQueryOperations>
      milkNotificationsEnabledProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'milkNotificationsEnabled');
    });
  }

  QueryBuilder<NotificationSettingsIsar, bool, QQueryOperations>
      notificationsEnabledProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'notificationsEnabled');
    });
  }

  QueryBuilder<NotificationSettingsIsar, bool, QQueryOperations>
      pushNotificationsEnabledProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'pushNotificationsEnabled');
    });
  }

  QueryBuilder<NotificationSettingsIsar, bool, QQueryOperations>
      smsNotificationsEnabledProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'smsNotificationsEnabled');
    });
  }

  QueryBuilder<NotificationSettingsIsar, DateTime?, QQueryOperations>
      updatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'updatedAt');
    });
  }
}
