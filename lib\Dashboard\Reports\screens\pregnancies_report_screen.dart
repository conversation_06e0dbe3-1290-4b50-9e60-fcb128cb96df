import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/pregnancy_report_data_isar.dart';
import '../tabs/pregnancy_summary_tab.dart';
import '../tabs/pregnancy_details_tab.dart';

class PregnanciesReportScreen extends StatefulWidget {
  const PregnanciesReportScreen({Key? key}) : super(key: key);

  @override
  PregnanciesReportScreenState createState() => PregnanciesReportScreenState();
}

class PregnanciesReportScreenState extends State<PregnanciesReportScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  PregnancyReportDataIsar? reportData;
  DateTime? startDate;
  DateTime? endDate;
  String searchQuery = '';
  String selectedStatus = 'All';
  bool isLoading = true;
  String? errorMessage;

  final List<String> statuses = ['All', 'Active', 'Completed', 'Aborted'];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    endDate = DateTime.now();
    startDate = endDate!.subtract(const Duration(days: 30));
    _initHandlers();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _initHandlers() {
    _loadData();
  }

  Future<void> _loadData() async {
    // Get pregnancy data based on filter
    setState(() {
      isLoading = true;
    });
    
    try {
      reportData = PregnancyReportDataIsar.empty();
    } catch (e) {
      debugPrint('Error loading pregnancy data: $e'); // Use debugPrint instead of print
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Pregnancy Report'),
        backgroundColor: const Color(0xFF2E7D32),
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Summary'),
            Tab(text: 'Details'),
          ],
        ),
      ),
      body: Column(
        children: [
          _buildFilters(),
          if (isLoading)
            const Expanded(
              child: Center(
                child: CircularProgressIndicator(),
              ),
            )
          else if (errorMessage != null)
            Expanded(
              child: Center(
                child: Text(
                  errorMessage!,
                  style: const TextStyle(color: Colors.red),
                ),
              ),
            )
          else
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  PregnancySummaryTab(reportData: reportData!),
                  PregnancyDetailsTab(reportData: reportData!),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildFilters() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'Status',
                    border: OutlineInputBorder(),
                  ),
                  value: selectedStatus,
                  items: const [
                    DropdownMenuItem<String>(
                      value: null,
                      child: Text('All Status'),
                    ),
                    DropdownMenuItem<String>(
                      value: 'Active',
                      child: Text('Active'),
                    ),
                    DropdownMenuItem<String>(
                      value: 'Completed',
                      child: Text('Completed'),
                    ),
                    DropdownMenuItem<String>(
                      value: 'Failed',
                      child: Text('Failed'),
                    ),
                  ],
                  onChanged: (value) {
                    setState(() {
                      selectedStatus = value!;
                      _loadData();
                    });
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'Search',
                    border: OutlineInputBorder(),
                  ),
                  controller: TextEditingController(
                    text: searchQuery,
                  ),
                  onChanged: (value) {
                    setState(() {
                      searchQuery = value;
                      _loadData();
                    });
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'Start Date',
                    border: OutlineInputBorder(),
                    suffixIcon: Icon(Icons.calendar_today),
                  ),
                  controller: TextEditingController(
                    text: DateFormat('yyyy-MM-dd').format(startDate!),
                  ),
                  readOnly: true,
                  onTap: () async {
                    final date = await showDatePicker(
                      context: context,
                      initialDate: startDate,
                      firstDate: DateTime(2000),
                      lastDate: DateTime.now(),
                    );
                    if (date != null) {
                      setState(() {
                        startDate = date;
                        _loadData();
                      });
                    }
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'End Date',
                    border: OutlineInputBorder(),
                    suffixIcon: Icon(Icons.calendar_today),
                  ),
                  controller: TextEditingController(
                    text: DateFormat('yyyy-MM-dd').format(endDate!),
                  ),
                  readOnly: true,
                  onTap: () async {
                    final date = await showDatePicker(
                      context: context,
                      initialDate: endDate,
                      firstDate: DateTime(2000),
                      lastDate: DateTime.now(),
                    );
                    if (date != null) {
                      setState(() {
                        endDate = date;
                        _loadData();
                      });
                    }
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
