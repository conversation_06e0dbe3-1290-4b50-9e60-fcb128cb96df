import 'package:flutter/material.dart';

/// Dialog themes configuration
class DialogThemes {
  static const EdgeInsets defaultPadding = EdgeInsets.all(16.0);
  static const double defaultWidth = 400.0;
  static const double defaultHeight = 600.0;
  
  static InputDecoration get textFieldDecoration => InputDecoration(
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
    ),
    contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
  );
}

/// Standard form dialog widget
class StandardFormDialog extends StatelessWidget {
  final String title;
  final Widget content;
  final List<Widget>? actions;
  final double? width;
  final double? height;
  final bool scrollable;

  const StandardFormDialog({
    super.key,
    required this.title,
    required this.content,
    this.actions,
    this.width,
    this.height,
    this.scrollable = true,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: width ?? DialogThemes.defaultWidth,
        height: height ?? DialogThemes.defaultHeight,
        padding: DialogThemes.defaultPadding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
            const Divider(),
            
            // Content
            Expanded(
              child: scrollable
                  ? SingleChildScrollView(child: content)
                  : content,
            ),
            
            // Actions
            if (actions != null) ...[
              const Divider(),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: actions!,
              ),
            ],
          ],
        ),
      ),
    );
  }
}
