/// Base class for all database-related exceptions
class DatabaseException implements Exception {
  final String message;
  final String details;

  DatabaseException(this.message, this.details);

  @override
  String toString() => 'DatabaseException: $message\nDetails: $details';
}

/// Exception thrown when validation fails
class ValidationException implements Exception {
  final String message;

  ValidationException(this.message);

  @override
  String toString() => 'ValidationException: $message';
}

/// Exception thrown when a record is not found
class RecordNotFoundException implements Exception {
  final String message;

  RecordNotFoundException(this.message);

  @override
  String toString() => 'RecordNotFoundException: $message';
} 