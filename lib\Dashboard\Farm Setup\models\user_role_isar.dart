import 'package:isar/isar.dart';
import 'package:uuid/uuid.dart';
import 'farm_user_isar.dart';

part 'user_role_isar.g.dart';

@collection
class UserRoleIsar {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  String? businessId;

  @Index(caseSensitive: false)
  String? name;

  // Store permissions as a List in Isar
  List<String> permissionsList = [];

  DateTime? createdAt;
  DateTime? updatedAt;

  // Backlink to users with this role
  @Backlink(to: 'role')
  final users = IsarLinks<FarmUserIsar>();

  UserRoleIsar();

  // Helper method to get/set permissions list
  @ignore
  List<String> get permissions => permissionsList;

  set permissions(List<String> value) {
    permissionsList = value;
  }

  // Helper for isar to store as string - to fix generated code
  String? get permissionsString => permissionsList.isNotEmpty ? permissionsList.join(',') : null;
  
  set permissionsString(String? value) {
    if (value != null && value.isNotEmpty) {
      permissionsList = value.split(',');
    } else {
      permissionsList = [];
    }
  }

  factory UserRoleIsar.create({
    required String name,
    required List<String> permissions,
  }) {
    final userRole = UserRoleIsar()
      ..businessId = const Uuid().v4()
      ..name = name
      ..permissionsList = permissions
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();
    
    return userRole;
  }

  Map<String, dynamic> toMap() {
    return {
      'id': businessId,
      'name': name,
      'permissions': permissionsList,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  factory UserRoleIsar.fromMap(Map<String, dynamic> map) {
    final userRole = UserRoleIsar()
      ..businessId = map['id'] as String?
      ..name = map['name'] as String?
      ..createdAt = map['createdAt'] != null ? DateTime.parse(map['createdAt'] as String) : DateTime.now()
      ..updatedAt = map['updatedAt'] != null ? DateTime.parse(map['updatedAt'] as String) : DateTime.now();
    
    if (map['permissions'] != null) {
      final permissionsList = List<String>.from(map['permissions'] as List);
      userRole.permissionsList = permissionsList;
    }
    
    return userRole;
  }

  UserRoleIsar copyWith({
    String? businessId,
    String? name,
    List<String>? permissions,
  }) {
    final userRole = UserRoleIsar()
      ..id = id
      ..businessId = businessId ?? this.businessId
      ..name = name ?? this.name
      ..permissionsList = permissions ?? permissionsList
      ..createdAt = createdAt
      ..updatedAt = DateTime.now();
    
    return userRole;
  }

  @ignore
  static List<String> get allPermissions => [
    'manage_cattle',
    'view_cattle',
    'manage_milk_records',
    'view_milk_records',
    'manage_health_records',
    'view_health_records',
    'manage_breeding_records',
    'view_breeding_records',
    'manage_transactions',
    'view_transactions',
    'manage_users',
    'view_reports',
    'manage_farm_settings',
  ];

  @ignore
  static List<UserRoleIsar> get defaultRoles => [
    UserRoleIsar.create(
      name: 'Admin',
      permissions: allPermissions,
    ),
    UserRoleIsar.create(
      name: 'Manager',
      permissions: [
        'view_cattle',
        'manage_cattle',
        'view_milk_records',
        'manage_milk_records',
        'view_health_records',
        'manage_health_records',
        'view_breeding_records',
        'manage_breeding_records',
        'view_transactions',
        'manage_transactions',
        'view_reports',
      ],
    ),
    UserRoleIsar.create(
      name: 'Worker',
      permissions: [
        'view_cattle',
        'view_milk_records',
        'manage_milk_records',
        'view_health_records',
        'view_breeding_records',
      ],
    ),
    UserRoleIsar.create(
      name: 'Veterinarian',
      permissions: [
        'view_cattle',
        'view_health_records',
        'manage_health_records',
        'view_breeding_records',
        'manage_breeding_records',
      ],
    ),
  ];
} 