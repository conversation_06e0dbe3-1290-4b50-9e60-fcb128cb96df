import 'dart:io';
import 'package:csv/csv.dart';
import '../../Milk Records/models/milk_record_isar.dart';
import 'package:intl/intl.dart';

class CsvExportService {
  static Future<File> exportMilkRecordsToCSV(List<MilkRecordIsar> records, String filePath) async {
    final csv = const ListToCsvConverter().convert(_prepareMilkRecordsData(records));
    final file = File(filePath);
    await file.writeAsString(csv);
    return file;
  }

  static List<List<dynamic>> _prepareMilkRecordsData(List<MilkRecordIsar> records) {
    final headers = ['Date', 'Cattle ID', 'Morning (L)', 'Evening (L)', 'Total (L)', 'Fat Content (%)', 'Revenue', 'Notes'];
    final rows = records.map((record) {
      return [
        record.date != null ? DateFormat('yyyy-MM-dd').format(record.date!) : '',
        record.tagId ?? '',
        record.morningAmount?.toString() ?? '',
        record.eveningAmount?.toString() ?? '',
        record.totalYield.toString(),
        record.fatContent?.toString() ?? '',
        (record.totalYield * (record.pricePerLiter ?? 0)).toString(),
        record.notes ?? '',
      ];
    }).toList();
    
    return [headers, ...rows];
  }

  static String generateUniqueFileName(String prefix) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return '${prefix}_$timestamp.csv';
  }
}