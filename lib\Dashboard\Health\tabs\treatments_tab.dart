/*
// TODO: Health Module - Treatments Tab
// This file needs to be refactored to use new repository pattern
// Currently commented out to fix compilation errors

import 'package:flutter/material.dart';
import '../../../services/database/database_helper.dart';
import '../dialogs/treatment_form_dialog.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../models/treatment_isar.dart';
import '../controllers/health_controller.dart';

import 'package:intl/intl.dart';
import '../../../constants/app_tabs.dart';

class TreatmentsTab extends StatelessWidget {
  final HealthController controller;

  const TreatmentsTab({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final treatmentRecords = controller.getRecordsByType('treatment');

    if (treatmentRecords.isEmpty) {
      return UniversalEmptyState.health(
        title: 'No Treatment Records',
        message: 'Treatment records will appear here when added',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: treatmentRecords.length,
      itemBuilder: (context, index) {
        final record = treatmentRecords[index];
        final cattle = controller.getCattle(record.cattleBusinessId);

        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: const CircleAvatar(
              backgroundColor: Colors.orange,
              child: Icon(
                Icons.healing,
                color: Colors.white,
              ),
            ),
            title: Text(cattle?.name ?? 'Unknown Cattle'),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Treatment: ${record.recordType ?? 'Unknown'}'),
                Text('Date: ${_formatDate(record.date)}'),
                if (record.status != null)
                  Text('Status: ${record.status}'),
              ],
            ),
            trailing: Icon(
              _getStatusIcon(record.status),
              color: _getStatusColor(record.status),
            ),
          ),
        );
      },
    );
  }

  IconData _getStatusIcon(String? status) {
    switch (status?.toLowerCase()) {
      case 'active':
        return Icons.pending;
      case 'completed':
        return Icons.check_circle;
      case 'cancelled':
        return Icons.cancel;
      default:
        return Icons.help_outline;
    }
  }

  Color _getStatusColor(String? status) {
    switch (status?.toLowerCase()) {
      case 'active':
        return Colors.orange;
      case 'completed':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'Unknown';
    return DateFormat('MMM dd, yyyy').format(date);
  }
}

class TreatmentsScreen extends StatefulWidget {
  const TreatmentsScreen({
    Key? key,
  }) : super(key: key);

  @override
  State<TreatmentsScreen> createState() => _TreatmentsScreenState();
}

class _TreatmentsScreenState extends State<TreatmentsScreen> {
  late final DatabaseHelper _dbHelper;

  List<TreatmentIsar> _treatments = [];
  Map<String, CattleIsar> _cattleMap = {};
  bool _isLoading = true;
  String _searchQuery = '';
  String _selectedAnimalType = 'All';
  List<dynamic> _animalTypes = [];
  final TextEditingController _searchController = TextEditingController();
  String _selectedDateRange = 'All Time';
  String _selectedCattleId = 'All';
  List<String> _tagIds = [];
  DateTime? selectedDate;
  DateTimeRange? selectedDateRange;

  final List<String> _dateRangeOptions = [
    'Today',
    '7 Days',
    '30 Days',
    '90 Days',
    'All Time',
  ];

  void _clearFilters() {
    setState(() {
      _searchQuery = '';
      _searchController.clear();
      _selectedAnimalType = 'All';
      _selectedCattleId = 'All';
      _selectedDateRange = 'All Time';
    });
  }

  Widget _buildSearchAndFilterSection() {
    return Container(
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Search Row
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search by cattle name or tag ID',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),
          // Filter Row
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 8),
            child: Row(
              children: [
                // Animal Type Filter
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: PopupMenuButton<String>(
                      position: PopupMenuPosition.under,
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'All',
                          child: Text('All Types'),
                        ),
                        ..._animalTypes.map((type) => PopupMenuItem(
                              value: type.name ?? 'Unknown',
                              child: Text(type.name ?? 'Unknown'),
                            )),
                      ],
                      onSelected: (value) {
                        setState(() {
                          _selectedAnimalType = value;
                        });
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: Text(
                                _selectedAnimalType,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const Icon(Icons.arrow_drop_down),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                // Cattle Filter
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: PopupMenuButton<String>(
                      position: PopupMenuPosition.under,
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'All',
                          child: Text('All Cattle'),
                        ),
                        ..._tagIds.map((tagId) {
                          final cattle = _cattleMap[tagId];
                          return PopupMenuItem(
                            value: tagId,
                            child: Text(cattle?.name ?? tagId),
                          );
                        }),
                      ],
                      onSelected: (value) {
                        setState(() {
                          _selectedCattleId = value;
                        });
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: Text(
                                _selectedCattleId == 'All'
                                    ? 'All Cattle'
                                    : _cattleMap[_selectedCattleId]?.name ??
                                        _selectedCattleId,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const Icon(Icons.arrow_drop_down),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                // Date Range Filter
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: PopupMenuButton<String>(
                      position: PopupMenuPosition.under,
                      itemBuilder: (context) => _dateRangeOptions
                          .map((range) => PopupMenuItem(
                                value: range,
                                child: Text(range),
                              ))
                          .toList(),
                      onSelected: (value) {
                        setState(() {
                          _selectedDateRange = value;
                        });
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: Text(
                                _selectedDateRange,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const Icon(Icons.arrow_drop_down),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Clear Filters button
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            child: Row(
              children: [
                TextButton.icon(
                  onPressed: _clearFilters,
                  icon: const Icon(Icons.clear),
                  label: const Text('Clear Filters'),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.redAccent,
                    backgroundColor: Colors.red.withAlpha(26),
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 8),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ],
            ),
          ),
          const Divider(height: 1),
        ],
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    _dbHelper = DatabaseHelper.instance;
    _loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      setState(() => _isLoading = true);

      // Load all cattle
      final allCattle = await _dbHelper.cattleHandler.getAllCattle();
      final Map<String, CattleIsar> cattleMap = {};

      // Get all treatments
      final allTreatments = await _dbHelper.healthHandler.getAllTreatments();

      // Only add cattle that have treatment records
      for (final treatment in allTreatments) {
        final cattleId = treatment.cattleId;
        if (cattleId != null) {
          // Safely filter cattle with null checks
          final cattle = allCattle.where((CattleIsar c) {
            if (c.tagId == null) {
              return false;
            }
            String tagIdValue = c.tagId!;
            return tagIdValue.toLowerCase() == cattleId.toLowerCase();
          }).firstOrNull;

          if (cattle != null) {
            cattleMap[cattleId] = cattle;
          }
        }
      }

      if (mounted) {
        setState(() {
          _cattleMap = cattleMap;
          _treatments = allTreatments;
          _animalTypes = []; // Simplified for now
          _tagIds = cattleMap.keys.toList();
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error loading treatments: $e')),
          );
        }
      }
    }
  }

  List<TreatmentIsar> _filterTreatments() {
    List<TreatmentIsar> filteredTreatments = List.from(_treatments);

    if (_searchQuery.isNotEmpty) {
      final lowercaseQuery = _searchQuery.toLowerCase();
      filteredTreatments = filteredTreatments.where((treatment) {
        final cattle = _cattleMap[treatment.cattleId ?? ''];
        final treatmentText = treatment.treatment?.toLowerCase() ?? '';
        final conditionText = treatment.condition?.toLowerCase() ?? '';
        final veterinarianText = treatment.veterinarian?.toLowerCase() ?? '';
        final cattleName = cattle?.name?.toLowerCase() ?? '';

        return treatmentText.contains(lowercaseQuery) ||
               conditionText.contains(lowercaseQuery) ||
               veterinarianText.contains(lowercaseQuery) ||
               cattleName.contains(lowercaseQuery);
      }).toList();
    }

    if (_selectedDateRange != 'All Time') {
      filteredTreatments = filteredTreatments.where((treatment) {
        if (treatment.date == null) return false;

        final treatmentDate = treatment.date!;
        switch (_selectedDateRange) {
          case 'Today':
            final today = DateTime.now();
            return treatmentDate.year == today.year &&
                   treatmentDate.month == today.month &&
                   treatmentDate.day == today.day;
          case '7 Days':
            final sevenDaysAgo = DateTime.now().subtract(const Duration(days: 7));
            return treatmentDate.isAfter(sevenDaysAgo);
          case '30 Days':
            final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
            return treatmentDate.isAfter(thirtyDaysAgo);
          case '90 Days':
            final ninetyDaysAgo = DateTime.now().subtract(const Duration(days: 90));
            return treatmentDate.isAfter(ninetyDaysAgo);
          default:
            return true;
        }
      }).toList();
    }

    if (_selectedCattleId != 'All') {
      filteredTreatments = filteredTreatments.where((treatment) {
        if (treatment.cattleId != _selectedCattleId) {
          return false;
        }
        return true;
      }).toList();
    }

    if (selectedDate != null) {
      filteredTreatments = filteredTreatments.where((treatment) {
        if (treatment.date == null || selectedDate == null) return false;

        return treatment.date?.year == selectedDate?.year &&
               treatment.date?.month == selectedDate?.month &&
               treatment.date?.day == selectedDate?.day;
      }).toList();
    }

    if (selectedDateRange != null) {
      filteredTreatments = filteredTreatments.where((treatment) {
        if (treatment.date == null || selectedDateRange == null) return false;

        final start = selectedDateRange?.start;
        final end = selectedDateRange?.end;

        if (start != null && treatment.date!.isBefore(start)) {
          return false;
        }
        if (end != null && treatment.date!.isAfter(end)) {
          return false;
        }
        return true;
      }).toList();
    }

    return filteredTreatments;
  }

  @override
  Widget build(BuildContext context) {
    final filteredTreatments = _filterTreatments();

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Treatments',
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: const Color(0xFF2E7D32),
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () {
              // Show date picker
              showDatePicker(
                context: context,
                initialDate: DateTime.now(),
                firstDate: DateTime(2000),
                lastDate: DateTime(2100),
              ).then((date) {
                if (date != null) {
                  setState(() {
                    selectedDate = date;
                  });
                }
              });
            },
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          showDialog<void>(
            context: context,
            builder: (context) => TreatmentFormDialog(
              cattle: _cattleMap.values.toList(),
              onSave: (treatment) async {
                await _dbHelper.healthHandler.addOrUpdateTreatment(
                  treatment.cattleId ?? '',
                  treatment
                );
                if (context.mounted) {
                  Navigator.pop(context);
                }
                _loadData();
              },
            ),
          );
        },
        backgroundColor: const Color(0xFF2E7D32),
        child: const Icon(Icons.add),
      ),
      body: Column(
        children: [
          _buildSearchAndFilterSection(),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : filteredTreatments.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.medication_outlined,
                              size: 64,
                              color: Colors.grey[500],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'No Treatments Found',
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: Colors.grey[700],
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Add a new treatment record',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.grey[500],
                              ),
                            ),
                            const SizedBox(height: 24),
                            ElevatedButton.icon(
                              onPressed: () {
                                showDialog<void>(
                                  context: context,
                                  builder: (context) => TreatmentFormDialog(
                                    cattle: _cattleMap.values.toList(),
                                    onSave: (treatment) async {
                                      await _dbHelper.healthHandler.addOrUpdateTreatment(
                                        treatment.cattleId ?? '',
                                        treatment
                                      );
                                      if (context.mounted) {
                                        Navigator.pop(context);
                                      }
                                      _loadData();
                                    },
                                  ),
                                );
                              },
                              icon: const Icon(Icons.add),
                              label: const Text('Add Treatment'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: const Color(0xFF2E7D32),
                                foregroundColor: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      )
                    : RefreshIndicator(
                        onRefresh: _loadData,
                        child: ListView.builder(
                          padding: const EdgeInsets.only(top: 8, bottom: 88),
                          itemCount: filteredTreatments.length,
                          itemBuilder: (context, index) {
                            final treatment = filteredTreatments[index];
                            return _buildTreatmentCard(treatment);
                          },
                        ),
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildTreatmentCard(TreatmentIsar treatment) {
    final cattle = _cattleMap[treatment.cattleId ?? ''];
    if (cattle == null) return const SizedBox.shrink();

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: InkWell(
        onTap: () {
          showDialog<void>(
            context: context,
            builder: (context) => TreatmentFormDialog(
              cattle: _cattleMap.values.toList(),
              treatment: treatment,
              onSave: (updatedTreatment) async {
                await _dbHelper.healthHandler.addOrUpdateTreatment(
                  updatedTreatment.cattleId ?? '',
                  updatedTreatment
                );
                if (context.mounted) {
                  Navigator.pop(context);
                }
                _loadData();
              },
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    cattle.name ?? 'Unknown',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.delete_outline),
                    onPressed: () => _deleteTreatment(treatment.id.toString()),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                'Tag ID: ${cattle.tagId ?? 'N/A'}',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Treatment: ${treatment.treatment ?? 'N/A'}',
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 4),
              Text(
                'Condition: ${treatment.condition ?? 'N/A'}',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
              ),
              if (treatment.dosage != null && treatment.dosage!.isNotEmpty) ...[
                const SizedBox(height: 4),
                Text(
                  'Dosage: ${treatment.dosage}',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
              ],
              const SizedBox(height: 4),
              Text(
                'Date: ${_getFormattedDate(treatment.date)}',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
              ),
              if (treatment.veterinarian != null && treatment.veterinarian!.isNotEmpty) ...[
                const SizedBox(height: 4),
                Text(
                  'Veterinarian: ${treatment.veterinarian}',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
              ],
              if (treatment.cost != null && treatment.cost!.isNotEmpty) ...[
                const SizedBox(height: 4),
                Text(
                  'Cost: \$${treatment.cost}',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _deleteTreatment(String id) async {
    try {
      await _dbHelper.healthHandler.deleteTreatment(id);
      _loadData();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Treatment deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error deleting treatment: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  String _getFormattedDate(DateTime? date) {
    if (date == null) return 'N/A';
    return DateFormat.yMMMd().format(date);
  }
}
*/
