import 'package:flutter/material.dart';
import '../models/farm_isar.dart';

class FarmListItem extends StatelessWidget {
  final FarmIsar farm;
  final VoidCallback onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final bool isActive;

  const FarmListItem({
    Key? key,
    required this.farm,
    required this.onTap,
    this.onEdit,
    this.onDelete,
    this.isActive = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      color: isActive ? Colors.blue.withAlpha(25) : null,
      child: ListTile(
        leading: const CircleAvatar(
          backgroundColor: Colors.blue,
          child: Icon(Icons.home_work, color: Colors.white),
        ),
        title: Text(farm.name ?? 'Unnamed Farm'),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Type: ${farm.farmType.toString().split('.').last}'),
            Text('Cattle: ${farm.cattleCount ?? 0}/${farm.capacity ?? 0}'),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (onEdit != null)
              IconButton(
                icon: const Icon(Icons.edit),
                onPressed: onEdit,
              ),
            if (onDelete != null)
              IconButton(
                icon: const Icon(Icons.delete),
                onPressed: onDelete,
              ),
          ],
        ),
        onTap: onTap,
      ),
    );
  }
}
