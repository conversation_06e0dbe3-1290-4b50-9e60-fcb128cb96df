import 'package:flutter/material.dart';
import 'package:isar/isar.dart';
import 'chart_data_isar.dart';
import 'report_data_isar.dart';

part 'milk_report_data_isar.g.dart';

@collection
class MilkReportDataIsar with ReportDataIsarMixin {
  Id id = Isar.autoIncrement;

  @override
  @Index()
  String? get reportType => super.reportType;
  @override
  set reportType(String? value) => super.reportType = value;

  @override
  @Index(unique: true)
  String? get businessId => super.businessId;
  @override
  set businessId(String? value) => super.businessId = value;

  int? totalRecords;
  double? totalMilk;
  double? averageMilkPerDay;
  double? averageMilkPerCow;
  double? topProducerAmount;
  String? topProducerId;
  String? topProducerName;

  // Monthly milk data for time series chart
  List<DateTime>? milkDates;
  List<double>? milkValues;

  // Average milk by time period
  List<String>? milkTimeLabels; // AM, PM, etc.
  List<double>? milkTimeValues;
  List<int>? milkTimeColors;

  /// Default constructor with name
  MilkReportDataIsar.empty();

  /// Factory constructor to create a new milk report
  factory MilkReportDataIsar.create({
    required String title,
    DateTime? startDate,
    DateTime? endDate,
    String? filterCriteria,
    int? totalRecords,
    double? totalMilk,
    double? averageMilkPerDay,
    double? averageMilkPerCow,
    double? topProducerAmount,
    String? topProducerId,
    String? topProducerName,
    List<DateTime>? milkDates,
    List<double>? milkValues,
    List<String>? milkTimeLabels,
    List<double>? milkTimeValues,
    List<int>? milkTimeColors,
  }) {
    final report = MilkReportDataIsar();

    // Initialize the base report properties
    report.initializeReport(
      reportType: 'milk',
      title: title,
      startDate: startDate,
      endDate: endDate,
      filterCriteria: filterCriteria,
    );

    // Set the milk-specific properties
    report.totalRecords = totalRecords;
    report.totalMilk = totalMilk;
    report.averageMilkPerDay = averageMilkPerDay;
    report.averageMilkPerCow = averageMilkPerCow;
    report.topProducerAmount = topProducerAmount;
    report.topProducerId = topProducerId;
    report.topProducerName = topProducerName;
    report.milkDates = milkDates;
    report.milkValues = milkValues;
    report.milkTimeLabels = milkTimeLabels;
    report.milkTimeValues = milkTimeValues;
    report.milkTimeColors = milkTimeColors;

    return report;
  }

  /// Constructor used in milk_report_screen.dart
  MilkReportDataIsar({
    List<dynamic>? allRecords,
    DateTime? startDate,
    DateTime? endDate,
    String? cattleId,
    String? searchQuery,
  }) {
    // Initialize the base report properties
    initializeReport(
      reportType: 'milk',
      title: 'Milk Production Report',
      startDate: startDate,
      endDate: endDate,
      filterCriteria: searchQuery,
    );

    // Process milk records
    if (allRecords != null && allRecords.isNotEmpty) {
      // Filter records based on criteria
      final filteredRecords = allRecords.where((record) {
        bool matchesCattle =
            cattleId == null || cattleId.isEmpty || record.cattleId == cattleId;
        bool matchesSearch = searchQuery == null ||
            searchQuery.isEmpty ||
            record.cattleId.toLowerCase().contains(searchQuery.toLowerCase());
        bool withinDateRange = true;

        if (startDate != null && record.date != null) {
          withinDateRange = record.date.isAfter(startDate) ||
              record.date.isAtSameMomentAs(startDate);
        }

        if (endDate != null && record.date != null && withinDateRange) {
          withinDateRange = record.date.isBefore(endDate) ||
              record.date.isAtSameMomentAs(endDate);
        }

        return matchesCattle && matchesSearch && withinDateRange;
      }).toList();

      // Set all the properties for the report
      totalRecords = filteredRecords.length;
      totalMilk = filteredRecords.fold<double>(
          0.0, (sum, record) => sum + (record.quantity ?? 0));

      // Calculate averages
      if (totalRecords! > 0) {
        final uniqueDaysCount = _uniqueDaysCount(filteredRecords);
        averageMilkPerDay =
            uniqueDaysCount > 0 ? totalMilk! / uniqueDaysCount : 0;

        // Get unique cattle count
        final uniqueCattle =
            filteredRecords.map((r) => r.cattleId).toSet().length;
        if (uniqueCattle > 0 && totalMilk != null) {
          averageMilkPerCow = totalMilk! / uniqueCattle;
        }
      }

      // Find top producer
      final cattleProduction = <String, double>{};
      final cattleNames = <String, String>{};

      for (final record in filteredRecords) {
        cattleProduction[record.cattleId] =
            (cattleProduction[record.cattleId] ?? 0) + (record.quantity ?? 0);
        cattleNames[record.cattleId] = record.cattleName ?? record.cattleId;
      }

      if (cattleProduction.isNotEmpty) {
        final topProducer = cattleProduction.entries
            .reduce((a, b) => a.value > b.value ? a : b);

        topProducerId = topProducer.key;
        topProducerAmount = topProducer.value;
        topProducerName = cattleNames[topProducerId] ?? topProducerId;
      }

      // Create time distribution data (morning/evening)
      final timeDistribution = <String, double>{
        'Morning': 0.0,
        'Evening': 0.0,
      };

      for (final record in filteredRecords) {
        if (record.isMorning != null) {
          final time = record.isMorning ? 'Morning' : 'Evening';
          timeDistribution[time] =
              (timeDistribution[time] ?? 0) + (record.quantity ?? 0);
        }
      }

      milkTimeLabels = timeDistribution.keys.toList();
      milkTimeValues = timeDistribution.values.toList();
      milkTimeColors = [
        0xFFFFA500, // Orange color as integer
        0xFF2196F3  // Blue color as integer
      ];

      // Create time series data
      if (filteredRecords.isNotEmpty) {
        final dateMap = <DateTime, double>{};
        for (final record in filteredRecords) {
          if (record.date != null) {
            final date =
                DateTime(record.date.year, record.date.month, record.date.day);
            dateMap[date] = (dateMap[date] ?? 0) + (record.quantity ?? 0);
          }
        }

        final sortedDates = dateMap.keys.toList()..sort();
        milkDates = sortedDates;
        milkValues = sortedDates.map((date) => dateMap[date]!).toList();
      }

      // Store the filtered records for the details view
      _filteredRecords = filteredRecords;
    } else {
      // Set defaults if no records
      totalRecords = 0;
      totalMilk = 0;
      averageMilkPerDay = 0;
      averageMilkPerCow = 0;
      _filteredRecords = [];
    }
  }

  // Helper method to count unique days in records
  int _uniqueDaysCount(List<dynamic> records) {
    final uniqueDays = <String>{};
    for (final record in records) {
      if (record.date != null) {
        uniqueDays
            .add('${record.date.year}-${record.date.month}-${record.date.day}');
      }
    }
    return uniqueDays.isNotEmpty
        ? uniqueDays.length
        : 1; // Avoid division by zero
  }

  // Store filtered records for details view
  List<dynamic> _filteredRecords = [];

  // Getter for filtered records
  @ignore
  List<dynamic> get filteredRecords => _filteredRecords;

  @override
  List<DataColumn> getTableColumns() {
    return [
      const DataColumn(label: Text('Metric')),
      const DataColumn(label: Text('Value'), numeric: true),
    ];
  }

  @override
  List<DataRow> getTableRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('Total Records')),
        DataCell(Text('${totalRecords ?? 0}')),
      ]),
      DataRow(cells: [
        const DataCell(Text('Total Milk (liters)')),
        DataCell(Text(totalMilk?.toStringAsFixed(1) ?? '0.0')),
      ]),
      DataRow(cells: [
        const DataCell(Text('Average Milk Per Day (liters)')),
        DataCell(Text(averageMilkPerDay?.toStringAsFixed(1) ?? '0.0')),
      ]),
      DataRow(cells: [
        const DataCell(Text('Average Milk Per Cow (liters)')),
        DataCell(Text(averageMilkPerCow?.toStringAsFixed(1) ?? '0.0')),
      ]),
      DataRow(cells: [
        const DataCell(Text('Top Producer')),
        DataCell(Text(
            '${topProducerName ?? 'N/A'} (${topProducerAmount?.toStringAsFixed(1) ?? '0.0'} liters)')),
      ]),
    ];
  }

  @override
  Map<String, dynamic> getSummaryData() {
    return {
      'Total Records': totalRecords ?? 0,
      'Total Milk (liters)': totalMilk?.toStringAsFixed(1) ?? '0.0',
      'Average Daily Production (liters)':
          averageMilkPerDay?.toStringAsFixed(1) ?? '0.0',
      'Top Producer': topProducerName ?? 'N/A',
    };
  }

  @override
  List<ChartDataIsar> getChartData() {
    final result = <ChartDataIsar>[];

    // Add milking time distribution chart data
    if (milkTimeLabels != null &&
        milkTimeValues != null &&
        milkTimeColors != null) {
      for (int i = 0;
          i < milkTimeLabels!.length &&
              i < milkTimeValues!.length &&
              i < milkTimeColors!.length;
          i++) {
        result.add(ChartDataIsar()
          ..label = milkTimeLabels![i]
          ..value = milkTimeValues![i]
          ..colorValue = milkTimeColors![i]);
      }
    }

    return result;
  }

  // Helper method to get time series milk data
  List<ChartDataIsar> getTimeSeriesChartData() {
    final result = <ChartDataIsar>[];

    // Create time series chart data from milkDates and milkValues
    if (milkDates != null && milkValues != null) {
      for (int i = 0; i < milkDates!.length && i < milkValues!.length; i++) {
        result.add(ChartDataIsar()
              ..date = milkDates![i]
              ..value = milkValues![i]
              ..colorValue = 0xFF2196F3 // Blue color as integer
            );
      }
    }

    return result;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    map.addAll({
      'totalRecords': totalRecords,
      'totalMilk': totalMilk,
      'averageMilkPerDay': averageMilkPerDay,
      'averageMilkPerCow': averageMilkPerCow,
      'topProducerAmount': topProducerAmount,
      'topProducerId': topProducerId,
      'topProducerName': topProducerName,
      'milkDates': milkDates?.map((date) => date.toIso8601String()).toList(),
      'milkValues': milkValues,
      'milkTimeLabels': milkTimeLabels,
      'milkTimeValues': milkTimeValues,
      'milkTimeColors': milkTimeColors,
    });
    return map;
  }

  factory MilkReportDataIsar.fromMap(Map<String, dynamic> map) {
    final report = MilkReportDataIsar();

    // Initialize the base properties
    report.initFromMap(map);

    // Set milk-specific properties
    report.totalRecords = map['totalRecords'] as int?;
    report.totalMilk = map['totalMilk'] as double?;
    report.averageMilkPerDay = map['averageMilkPerDay'] as double?;
    report.averageMilkPerCow = map['averageMilkPerCow'] as double?;
    report.topProducerAmount = map['topProducerAmount'] as double?;
    report.topProducerId = map['topProducerId'] as String?;
    report.topProducerName = map['topProducerName'] as String?;

    // Handle lists
    if (map['milkDates'] != null) {
      report.milkDates = (map['milkDates'] as List)
          .map((dateStr) => DateTime.parse(dateStr as String))
          .toList();
    }

    if (map['milkValues'] != null) {
      report.milkValues = List<double>.from(map['milkValues'] as List);
    }

    if (map['milkTimeLabels'] != null) {
      report.milkTimeLabels = List<String>.from(map['milkTimeLabels'] as List);
    }

    if (map['milkTimeValues'] != null) {
      report.milkTimeValues = List<double>.from(map['milkTimeValues'] as List);
    }

    if (map['milkTimeColors'] != null) {
      report.milkTimeColors = List<int>.from(map['milkTimeColors'] as List);
    }

    return report;
  }
}
