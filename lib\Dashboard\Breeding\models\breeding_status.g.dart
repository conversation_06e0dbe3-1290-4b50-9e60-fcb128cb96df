// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'breeding_status.dart';

// **************************************************************************
// IsarEmbeddedGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

const BreedingStatusSchema = Schema(
  name: r'BreedingStatus',
  id: 4307553154178481609,
  properties: {
    r'breedingDate': PropertySchema(
      id: 0,
      name: r'breedingDate',
      type: IsarType.dateTime,
    ),
    r'expectedCalvingDate': PropertySchema(
      id: 1,
      name: r'expectedCalvingDate',
      type: IsarType.dateTime,
    ),
    r'isPregnant': PropertySchema(
      id: 2,
      name: r'isPregnant',
      type: IsarType.bool,
    ),
    r'lastBreedingMethod': PropertySchema(
      id: 3,
      name: r'lastBreedingMethod',
      type: IsarType.string,
    ),
    r'status': PropertySchema(
      id: 4,
      name: r'status',
      type: IsarType.string,
    )
  },
  estimateSize: _breedingStatusEstimateSize,
  serialize: _breedingStatusSerialize,
  deserialize: _breedingStatusDeserialize,
  deserializeProp: _breedingStatusDeserializeProp,
);

int _breedingStatusEstimateSize(
  BreedingStatus object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.lastBreedingMethod;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.status;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _breedingStatusSerialize(
  BreedingStatus object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeDateTime(offsets[0], object.breedingDate);
  writer.writeDateTime(offsets[1], object.expectedCalvingDate);
  writer.writeBool(offsets[2], object.isPregnant);
  writer.writeString(offsets[3], object.lastBreedingMethod);
  writer.writeString(offsets[4], object.status);
}

BreedingStatus _breedingStatusDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = BreedingStatus(
    breedingDate: reader.readDateTimeOrNull(offsets[0]),
    expectedCalvingDate: reader.readDateTimeOrNull(offsets[1]),
    isPregnant: reader.readBoolOrNull(offsets[2]) ?? false,
    lastBreedingMethod: reader.readStringOrNull(offsets[3]),
    status: reader.readStringOrNull(offsets[4]),
  );
  return object;
}

P _breedingStatusDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 1:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 2:
      return (reader.readBoolOrNull(offset) ?? false) as P;
    case 3:
      return (reader.readStringOrNull(offset)) as P;
    case 4:
      return (reader.readStringOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

extension BreedingStatusQueryFilter
    on QueryBuilder<BreedingStatus, BreedingStatus, QFilterCondition> {
  QueryBuilder<BreedingStatus, BreedingStatus, QAfterFilterCondition>
      breedingDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'breedingDate',
      ));
    });
  }

  QueryBuilder<BreedingStatus, BreedingStatus, QAfterFilterCondition>
      breedingDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'breedingDate',
      ));
    });
  }

  QueryBuilder<BreedingStatus, BreedingStatus, QAfterFilterCondition>
      breedingDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'breedingDate',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingStatus, BreedingStatus, QAfterFilterCondition>
      breedingDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'breedingDate',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingStatus, BreedingStatus, QAfterFilterCondition>
      breedingDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'breedingDate',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingStatus, BreedingStatus, QAfterFilterCondition>
      breedingDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'breedingDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<BreedingStatus, BreedingStatus, QAfterFilterCondition>
      expectedCalvingDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'expectedCalvingDate',
      ));
    });
  }

  QueryBuilder<BreedingStatus, BreedingStatus, QAfterFilterCondition>
      expectedCalvingDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'expectedCalvingDate',
      ));
    });
  }

  QueryBuilder<BreedingStatus, BreedingStatus, QAfterFilterCondition>
      expectedCalvingDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'expectedCalvingDate',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingStatus, BreedingStatus, QAfterFilterCondition>
      expectedCalvingDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'expectedCalvingDate',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingStatus, BreedingStatus, QAfterFilterCondition>
      expectedCalvingDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'expectedCalvingDate',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingStatus, BreedingStatus, QAfterFilterCondition>
      expectedCalvingDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'expectedCalvingDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<BreedingStatus, BreedingStatus, QAfterFilterCondition>
      isPregnantEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isPregnant',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingStatus, BreedingStatus, QAfterFilterCondition>
      lastBreedingMethodIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'lastBreedingMethod',
      ));
    });
  }

  QueryBuilder<BreedingStatus, BreedingStatus, QAfterFilterCondition>
      lastBreedingMethodIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'lastBreedingMethod',
      ));
    });
  }

  QueryBuilder<BreedingStatus, BreedingStatus, QAfterFilterCondition>
      lastBreedingMethodEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'lastBreedingMethod',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingStatus, BreedingStatus, QAfterFilterCondition>
      lastBreedingMethodGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'lastBreedingMethod',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingStatus, BreedingStatus, QAfterFilterCondition>
      lastBreedingMethodLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'lastBreedingMethod',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingStatus, BreedingStatus, QAfterFilterCondition>
      lastBreedingMethodBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'lastBreedingMethod',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingStatus, BreedingStatus, QAfterFilterCondition>
      lastBreedingMethodStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'lastBreedingMethod',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingStatus, BreedingStatus, QAfterFilterCondition>
      lastBreedingMethodEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'lastBreedingMethod',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingStatus, BreedingStatus, QAfterFilterCondition>
      lastBreedingMethodContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'lastBreedingMethod',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingStatus, BreedingStatus, QAfterFilterCondition>
      lastBreedingMethodMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'lastBreedingMethod',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingStatus, BreedingStatus, QAfterFilterCondition>
      lastBreedingMethodIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'lastBreedingMethod',
        value: '',
      ));
    });
  }

  QueryBuilder<BreedingStatus, BreedingStatus, QAfterFilterCondition>
      lastBreedingMethodIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'lastBreedingMethod',
        value: '',
      ));
    });
  }

  QueryBuilder<BreedingStatus, BreedingStatus, QAfterFilterCondition>
      statusIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'status',
      ));
    });
  }

  QueryBuilder<BreedingStatus, BreedingStatus, QAfterFilterCondition>
      statusIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'status',
      ));
    });
  }

  QueryBuilder<BreedingStatus, BreedingStatus, QAfterFilterCondition>
      statusEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'status',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingStatus, BreedingStatus, QAfterFilterCondition>
      statusGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'status',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingStatus, BreedingStatus, QAfterFilterCondition>
      statusLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'status',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingStatus, BreedingStatus, QAfterFilterCondition>
      statusBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'status',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingStatus, BreedingStatus, QAfterFilterCondition>
      statusStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'status',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingStatus, BreedingStatus, QAfterFilterCondition>
      statusEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'status',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingStatus, BreedingStatus, QAfterFilterCondition>
      statusContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'status',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingStatus, BreedingStatus, QAfterFilterCondition>
      statusMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'status',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingStatus, BreedingStatus, QAfterFilterCondition>
      statusIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'status',
        value: '',
      ));
    });
  }

  QueryBuilder<BreedingStatus, BreedingStatus, QAfterFilterCondition>
      statusIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'status',
        value: '',
      ));
    });
  }
}

extension BreedingStatusQueryObject
    on QueryBuilder<BreedingStatus, BreedingStatus, QFilterCondition> {}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BreedingStatus _$BreedingStatusFromJson(Map<String, dynamic> json) =>
    BreedingStatus(
      status: json['status'] as String?,
      isPregnant: json['isPregnant'] as bool? ?? false,
      breedingDate: json['breedingDate'] == null
          ? null
          : DateTime.parse(json['breedingDate'] as String),
      expectedCalvingDate: json['expectedCalvingDate'] == null
          ? null
          : DateTime.parse(json['expectedCalvingDate'] as String),
      lastBreedingMethod: json['lastBreedingMethod'] as String?,
    );

Map<String, dynamic> _$BreedingStatusToJson(BreedingStatus instance) =>
    <String, dynamic>{
      'status': instance.status,
      'isPregnant': instance.isPregnant,
      'breedingDate': instance.breedingDate?.toIso8601String(),
      'expectedCalvingDate': instance.expectedCalvingDate?.toIso8601String(),
      'lastBreedingMethod': instance.lastBreedingMethod,
    };
