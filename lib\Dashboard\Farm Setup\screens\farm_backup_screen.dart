import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import '../models/farm_isar.dart'; // Corrected import path
import '../../../utils/message_utils.dart';
// Using FarmSetupHandler

class FarmBackupScreen extends StatefulWidget {
  final FarmIsar farm;

  const FarmBackupScreen({Key? key, required this.farm}) : super(key: key);

  @override
  State<FarmBackupScreen> createState() => _FarmBackupScreenState();
}

class _FarmBackupScreenState extends State<FarmBackupScreen> {
  bool _isBackingUp = false;
  bool _isRestoring = false;
  String? _lastBackupPath;
  List<String> _backupFiles = [];

  @override
  void initState() {
    super.initState();
    _loadBackupFiles();
  }

  Future<void> _loadBackupFiles() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final backupDir = Directory('${directory.path}/backups');
      if (!await backupDir.exists()) {
        await backupDir.create(recursive: true);
      }

      final files = await backupDir.list().toList();
      setState(() {
        _backupFiles = files
            .where(
                (file) => file.path.contains(widget.farm.farmBusinessId ?? ''))
            .map((file) => file.path)
            .toList()
          ..sort((a, b) => b.compareTo(a)); // Sort by newest first
      });
    } catch (e) {
      debugPrint('Error loading backup files: $e');
      if (mounted) {
        FarmSetupMessageUtils.showError(context, 'Error loading backup files');
      }
    }
  }

  Future<void> _createBackup() async {
    setState(() => _isBackingUp = true);

    try {
      final directory = await getApplicationDocumentsDirectory();
      final backupDir = Directory('${directory.path}/backups');
      if (!await backupDir.exists()) {
        await backupDir.create(recursive: true);
      }

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName =
          '${widget.farm.name?.toLowerCase().replaceAll(' ', '_') ?? 'farm'}_$timestamp.backup';
      final file = File('${backupDir.path}/$fileName');

      // This is a simulated backup since we don't have the actual implementation in FarmSetupHandler
      // In a real implementation, you would call the appropriate method on _farmSetupHandler
      await Future.delayed(const Duration(seconds: 2)); // Simulate work
      final backupData =
          '{"farm_id": "${widget.farm.farmBusinessId}", "timestamp": $timestamp}';
      await file.writeAsString(backupData);

      setState(() {
        _lastBackupPath = file.path;
        _isBackingUp = false;
      });

      await _loadBackupFiles();

      if (mounted) {
        FarmSetupMessageUtils.showSuccess(context, 'Backup created: $fileName');
      }
    } catch (e) {
      debugPrint('Error creating backup: $e');
      setState(() => _isBackingUp = false);
      if (mounted) {
        FarmSetupMessageUtils.showError(context, 'Error creating backup');
      }
    }
  }

  Future<void> _restoreBackup(String backupPath) async {
    setState(() => _isRestoring = true);

    try {
      // This is a simulated restoration since we don't have the actual implementation in FarmSetupHandler
      // In a real implementation, you would call the appropriate method on _farmSetupHandler
      await Future.delayed(const Duration(seconds: 2)); // Simulate work

      setState(() => _isRestoring = false);

      if (mounted) {
        FarmSetupMessageUtils.showSuccess(context, 'Backup restored successfully');
      }
    } catch (e) {
      debugPrint('Error restoring backup: $e');
      setState(() => _isRestoring = false);
      if (mounted) {
        FarmSetupMessageUtils.showError(context, 'Error restoring backup');
      }
    }
  }

  Future<void> _deleteBackup(String backupPath) async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Backup'),
        content: const Text('Are you sure you want to delete this backup?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirm == true) {
      try {
        final file = File(backupPath);
        await file.delete();
        await _loadBackupFiles();
      } catch (e) {
        debugPrint('Error deleting backup: $e');
        if (mounted) {
          FarmSetupMessageUtils.showError(context, 'Error deleting backup');
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('${widget.farm.name ?? "Farm"} Backup'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Create Backup',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Create a backup of all farm data including cattle records, milk production, health records, and financial data.',
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton.icon(
                      onPressed: _isBackingUp ? null : _createBackup,
                      icon: _isBackingUp
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Icon(Icons.backup),
                      label: Text(_isBackingUp
                          ? 'Creating Backup...'
                          : 'Create Backup'),
                    ),
                    if (_lastBackupPath != null) ...[
                      const SizedBox(height: 16),
                      Text(
                        'Last backup: $_lastBackupPath',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Backup History',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    if (_backupFiles.isEmpty)
                      const Text('No backups available')
                    else
                      ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: _backupFiles.length,
                        itemBuilder: (context, index) {
                          final backupPath = _backupFiles[index];
                          final fileName = backupPath.split('/').last;
                          return ListTile(
                            title: Text(fileName),
                            trailing: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                IconButton(
                                  icon: const Icon(Icons.restore),
                                  onPressed: _isRestoring
                                      ? null
                                      : () => _restoreBackup(backupPath),
                                ),
                                IconButton(
                                  icon: const Icon(Icons.delete),
                                  onPressed: () => _deleteBackup(backupPath),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
