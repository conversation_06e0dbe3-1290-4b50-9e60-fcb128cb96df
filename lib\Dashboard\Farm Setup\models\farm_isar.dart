import 'package:isar/isar.dart';

part 'farm_isar.g.dart';

/// Available farm types
enum FarmType {
  dairy,
  breeding,
  mixed,
}

/// Represents a farm in the system
@collection
class FarmIsar {
  // Isar ID - auto incremented primary key
  Id id = Isar.autoIncrement;
  
  // Business ID (UUID) - indexed for quick lookup
  @Index(unique: true)
  String? farmBusinessId;
  
  @Index(caseSensitive: false)
  String? name;
  
  String? location;
  String? logoUrl;
  String? size;
  
  String? ownerName;
  String? ownerContact;
  String? ownerEmail;
  
  double? latitude;
  double? longitude;
  String? address;
  
  @enumerated
  FarmType farmType = FarmType.mixed;
  
  int? cattleCount;
  int? capacity;
  
  // Store timestamp as DateTime
  DateTime? createdAt;
  DateTime? updatedAt;

  // Default constructor for Isar
  FarmIsar();
  
  // Convert to Map for compatibility with existing code
  Map<String, dynamic> toMap() {
    return {
      'id': farmBusinessId,
      'name': name,
      'location': location,
      'logoUrl': logoUrl,
      'size': size,
      'farmType': farmType.toString().split('.').last,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }
  
  Map<String, dynamic> toJson() => toMap();

  // Create from Map for compatibility with existing code
  factory FarmIsar.fromJson(Map<String, dynamic> json) => FarmIsar.fromMap(json);
  
  factory FarmIsar.fromMap(Map<String, dynamic> map) {
    final farm = FarmIsar()
      ..farmBusinessId = map['id'] as String?
      ..name = map['name'] as String?
      ..location = map['location'] as String?
      ..logoUrl = map['logoUrl'] as String?
      ..size = map['size'] as String?
      ..ownerName = map['ownerName']
      ..ownerContact = map['ownerContact']
      ..ownerEmail = map['ownerEmail']
      ..latitude = map['latitude']
      ..longitude = map['longitude']
      ..address = map['address']
      ..cattleCount = map['cattleCount']
      ..capacity = map['capacity'];
    
    // Handle createdAt
    if (map['createdAt'] != null) {
      farm.createdAt = map['createdAt'] is String 
          ? DateTime.parse(map['createdAt'])
          : map['createdAt'];
    }
    
    // Handle updatedAt
    if (map['updatedAt'] != null) {
      farm.updatedAt = map['updatedAt'] is String 
          ? DateTime.parse(map['updatedAt'])
          : map['updatedAt'];
    }
    
    // Handle farm type
    if (map['farmType'] != null) {
      final typeString = map['farmType'] as String;
      switch (typeString.toLowerCase()) {
        case 'dairy':
          farm.farmType = FarmType.dairy;
          break;
        case 'breeding':
          farm.farmType = FarmType.breeding;
          break;
        case 'mixed':
          farm.farmType = FarmType.mixed;
          break;
        default:
          farm.farmType = FarmType.mixed;
      }
    }
    
    return farm;
  }
  
  // Named constructor for convenience
  factory FarmIsar.create({
    required String id,
    required String name,
    required String ownerName,
    required String ownerContact,
    required String ownerEmail,
    double? latitude,
    double? longitude,
    String? address,
    required FarmType farmType,
    required int cattleCount,
    required int capacity,
    required DateTime createdAt,
    required DateTime updatedAt,
  }) {
    return FarmIsar()
      ..farmBusinessId = id
      ..name = name
      ..ownerName = ownerName
      ..ownerContact = ownerContact
      ..ownerEmail = ownerEmail
      ..latitude = latitude
      ..longitude = longitude
      ..address = address
      ..farmType = farmType
      ..cattleCount = cattleCount
      ..capacity = capacity
      ..createdAt = createdAt
      ..updatedAt = updatedAt;
  }
} 