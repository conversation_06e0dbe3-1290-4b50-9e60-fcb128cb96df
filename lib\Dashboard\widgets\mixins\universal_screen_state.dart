import 'package:flutter/material.dart';
import 'dart:async';

/// Universal Screen State Management Mixin
/// 
/// Provides consistent state management patterns across all screens with:
/// - Safe setState with mounted checking
/// - Standardized loading, error, and success states
/// - Consistent error handling and display
/// - Automatic cleanup and disposal
/// - Stream subscription management

/// Screen state enum for consistent state tracking
enum ScreenState {
  initial,
  loading,
  loaded,
  error,
  empty,
  refreshing,
}

/// Universal Screen State Mixin
/// 
/// Mix this into any StatefulWidget's State class to get consistent
/// screen state management with built-in safety and error handling.
mixin UniversalScreenState<T extends StatefulWidget> on State<T> {
  // Core state management
  ScreenState _screenState = ScreenState.initial;
  String? _errorMessage;
  bool _isDisposed = false;

  // Getters for current state
  ScreenState get screenState => _screenState;
  bool get isLoading => _screenState == ScreenState.loading;
  bool get isLoaded => _screenState == ScreenState.loaded;
  bool get hasError => _screenState == ScreenState.error;
  bool get isEmpty => _screenState == ScreenState.empty;
  bool get isRefreshing => _screenState == ScreenState.refreshing;
  bool get isInitial => _screenState == ScreenState.initial;
  String? get errorMessage => _errorMessage;

  // Safe setState that checks mounted status
  void safeSetState(VoidCallback fn) {
    if (!_isDisposed && mounted) {
      setState(fn);
    }
  }

  // State transition methods
  void setLoading() {
    safeSetState(() {
      _screenState = ScreenState.loading;
      _errorMessage = null;
    });
  }

  void setLoaded() {
    safeSetState(() {
      _screenState = ScreenState.loaded;
      _errorMessage = null;
    });
  }

  void setError(String message) {
    safeSetState(() {
      _screenState = ScreenState.error;
      _errorMessage = message;
    });
  }

  void setEmpty() {
    safeSetState(() {
      _screenState = ScreenState.empty;
      _errorMessage = null;
    });
  }

  void setRefreshing() {
    safeSetState(() {
      _screenState = ScreenState.refreshing;
      _errorMessage = null;
    });
  }

  void setInitial() {
    safeSetState(() {
      _screenState = ScreenState.initial;
      _errorMessage = null;
    });
  }

  // Async operation wrapper with automatic state management
  Future<void> executeWithLoading(Future<void> Function() operation) async {
    setLoading();
    try {
      await operation();
      setLoaded();
    } catch (e) {
      setError(_getReadableErrorMessage(e));
    }
  }

  // Refresh operation wrapper
  Future<void> executeRefresh(Future<void> Function() operation) async {
    setRefreshing();
    try {
      await operation();
      setLoaded();
    } catch (e) {
      setError(_getReadableErrorMessage(e));
    }
  }

  // Convert exceptions to user-friendly messages
  String _getReadableErrorMessage(dynamic error) {
    final errorString = error.toString();
    
    if (errorString.contains('database')) {
      return 'Database error: Unable to access data';
    } else if (errorString.contains('connection') || errorString.contains('network')) {
      return 'Network error: Please check your connection';
    } else if (errorString.contains('permission')) {
      return 'Permission error: Access denied';
    } else if (errorString.contains('timeout')) {
      return 'Request timeout: Operation took too long';
    } else if (errorString.contains('not found')) {
      return 'Data not found: The requested information is unavailable';
    } else {
      return 'An unexpected error occurred. Please try again.';
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    super.dispose();
  }
}

/// Enhanced Screen State Mixin with Stream Management
/// 
/// Extends UniversalScreenState with stream subscription management
/// for screens that need to listen to data streams.
mixin UniversalScreenStateWithStreams<T extends StatefulWidget> on UniversalScreenState<T> {
  final List<StreamSubscription> _subscriptions = [];

  // Add a stream subscription with automatic cleanup
  void addSubscription(StreamSubscription subscription) {
    _subscriptions.add(subscription);
  }

  // Listen to a stream with automatic subscription management
  void listenToStream<S>(
    Stream<S> stream,
    void Function(S) onData, {
    Function? onError,
    void Function()? onDone,
  }) {
    final subscription = stream.listen(
      onData,
      onError: onError ?? (error) => setError(_getReadableErrorMessage(error)),
      onDone: onDone,
    );
    addSubscription(subscription);
  }

  @override
  void dispose() {
    // Cancel all subscriptions
    for (final subscription in _subscriptions) {
      subscription.cancel();
    }
    _subscriptions.clear();
    super.dispose();
  }
}

/// Data Loading Mixin
/// 
/// Provides common data loading patterns with automatic state management.
mixin UniversalDataLoader<T extends StatefulWidget> on UniversalScreenState<T> {
  // Load data with automatic state management
  Future<void> loadData(Future<void> Function() loadOperation) async {
    await executeWithLoading(loadOperation);
  }

  // Refresh data with automatic state management
  Future<void> refreshData(Future<void> Function() refreshOperation) async {
    await executeRefresh(refreshOperation);
  }

  // Load data on init with error handling
  void loadDataOnInit(Future<void> Function() loadOperation) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        loadData(loadOperation);
      }
    });
  }
}

/// Form State Mixin
/// 
/// Provides form-specific state management patterns.
mixin UniversalFormState<T extends StatefulWidget> on UniversalScreenState<T> {
  bool _isSaving = false;
  bool _isValidating = false;

  bool get isSaving => _isSaving;
  bool get isValidating => _isValidating;
  bool get isFormBusy => _isSaving || _isValidating;

  void setSaving(bool saving) {
    safeSetState(() {
      _isSaving = saving;
    });
  }

  void setValidating(bool validating) {
    safeSetState(() {
      _isValidating = validating;
    });
  }

  // Save operation with automatic state management
  Future<bool> executeSave(Future<void> Function() saveOperation) async {
    setSaving(true);
    try {
      await saveOperation();
      setSaving(false);
      return true;
    } catch (e) {
      setSaving(false);
      setError(_getReadableErrorMessage(e));
      return false;
    }
  }

  // Validation operation with automatic state management
  Future<bool> executeValidation(Future<bool> Function() validationOperation) async {
    setValidating(true);
    try {
      final isValid = await validationOperation();
      setValidating(false);
      return isValid;
    } catch (e) {
      setValidating(false);
      setError(_getReadableErrorMessage(e));
      return false;
    }
  }
}

/// Universal Data Refresh Mixin
///
/// Provides comprehensive data refresh patterns with:
/// - Pull-to-refresh support
/// - Periodic refresh timers
/// - Manual refresh controls
/// - Refresh indicators and state management
/// - Automatic retry mechanisms
mixin UniversalDataRefresh<T extends StatefulWidget> on UniversalScreenState<T> {
  Timer? _periodicRefreshTimer;
  Timer? _retryTimer;
  bool _isPeriodicRefreshEnabled = false;
  Duration _refreshInterval = const Duration(minutes: 5);
  int _retryAttempts = 0;
  int _maxRetryAttempts = 3;
  Duration _retryDelay = const Duration(seconds: 2);

  // Getters for refresh state
  bool get isPeriodicRefreshEnabled => _isPeriodicRefreshEnabled;
  Duration get refreshInterval => _refreshInterval;
  int get retryAttempts => _retryAttempts;

  /// Enable periodic refresh with custom interval
  void enablePeriodicRefresh({
    Duration interval = const Duration(minutes: 5),
    bool startImmediately = false,
  }) {
    _refreshInterval = interval;
    _isPeriodicRefreshEnabled = true;

    _periodicRefreshTimer?.cancel();
    _periodicRefreshTimer = Timer.periodic(interval, (_) {
      if (mounted && !isLoading && !isRefreshing) {
        _performPeriodicRefresh();
      }
    });

    if (startImmediately) {
      _performPeriodicRefresh();
    }
  }

  /// Disable periodic refresh
  void disablePeriodicRefresh() {
    _isPeriodicRefreshEnabled = false;
    _periodicRefreshTimer?.cancel();
    _periodicRefreshTimer = null;
  }

  /// Perform manual refresh with pull-to-refresh support
  Future<void> performRefresh(Future<void> Function() refreshOperation) async {
    _retryAttempts = 0;
    _retryTimer?.cancel();

    await executeRefresh(() async {
      try {
        await refreshOperation();
      } catch (e) {
        // If refresh fails, schedule retry if enabled
        if (_retryAttempts < _maxRetryAttempts) {
          _scheduleRetry(refreshOperation);
        }
        rethrow;
      }
    });
  }

  /// Perform refresh with automatic retry on failure
  Future<void> performRefreshWithRetry(
    Future<void> Function() refreshOperation, {
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 2),
  }) async {
    _maxRetryAttempts = maxRetries;
    _retryDelay = retryDelay;
    _retryAttempts = 0;

    await performRefresh(refreshOperation);
  }

  /// Schedule automatic retry
  void _scheduleRetry(Future<void> Function() refreshOperation) {
    _retryAttempts++;
    _retryTimer?.cancel();

    _retryTimer = Timer(_retryDelay * _retryAttempts, () {
      if (mounted && _retryAttempts <= _maxRetryAttempts) {
        performRefresh(refreshOperation);
      }
    });
  }

  /// Perform periodic refresh (internal)
  void _performPeriodicRefresh() {
    // Override in implementing class to define periodic refresh behavior
    // This is called automatically by the timer
  }

  /// Force immediate refresh
  void forceRefresh(Future<void> Function() refreshOperation) {
    _retryAttempts = 0;
    _retryTimer?.cancel();
    performRefresh(refreshOperation);
  }

  @override
  void dispose() {
    _periodicRefreshTimer?.cancel();
    _retryTimer?.cancel();
    super.dispose();
  }
}

/// Universal Pull-to-Refresh Mixin
///
/// Provides standardized pull-to-refresh functionality with consistent styling.
mixin UniversalPullToRefresh<T extends StatefulWidget> on UniversalDataRefresh<T> {
  /// Build RefreshIndicator with consistent styling
  Widget buildRefreshIndicator({
    required Widget child,
    required Future<void> Function() onRefresh,
    String? semanticsLabel,
    Color? color,
  }) {
    return RefreshIndicator(
      onRefresh: () => performRefresh(onRefresh),
      semanticsLabel: semanticsLabel ?? 'Refresh data',
      color: color,
      child: child,
    );
  }

  /// Build RefreshIndicator for ListView
  Widget buildRefreshableList({
    required List<Widget> children,
    required Future<void> Function() onRefresh,
    EdgeInsets? padding,
    ScrollPhysics? physics,
    String? semanticsLabel,
    Color? color,
  }) {
    return buildRefreshIndicator(
      onRefresh: onRefresh,
      semanticsLabel: semanticsLabel,
      color: color,
      child: ListView(
        padding: padding ?? const EdgeInsets.all(16.0),
        physics: physics ?? const AlwaysScrollableScrollPhysics(),
        children: children,
      ),
    );
  }

  /// Build RefreshIndicator for ListView.builder
  Widget buildRefreshableListBuilder({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    required Future<void> Function() onRefresh,
    EdgeInsets? padding,
    ScrollPhysics? physics,
    String? semanticsLabel,
    Color? color,
  }) {
    return buildRefreshIndicator(
      onRefresh: onRefresh,
      semanticsLabel: semanticsLabel,
      color: color,
      child: ListView.builder(
        padding: padding ?? const EdgeInsets.all(8.0),
        physics: physics ?? const AlwaysScrollableScrollPhysics(),
        itemCount: itemCount,
        itemBuilder: itemBuilder,
      ),
    );
  }
}
