import 'package:isar/isar.dart';
import 'package:uuid/uuid.dart';
import 'package:flutter/material.dart';

part 'event_type_isar.g.dart';

@collection
class EventTypeIsar {
  Id id = Isar.autoIncrement;
  
  @Index(unique: true)
  String? businessId;
  
  String name = '';
  String? description;
  DateTime? createdAt;
  DateTime? updatedAt;
  
  /// Color value for the event type
  int? colorValue;
  
  /// Static IDs for predefined event types to avoid ID regeneration on reinstall
  static const Map<String, String> staticIds = {
    'vaccination': 'static_event_vaccination_id',
    'health check': 'static_event_health_check_id',
    'breeding': 'static_event_breeding_id',
    'pregnancy check': 'static_event_pregnancy_check_id',
    'birth/calving': 'static_event_birth_calving_id',
    'deworming': 'static_event_deworming_id',
    'weight recording': 'static_event_weight_recording_id',
    'milk recording': 'static_event_milk_recording_id',
    'veterinary visit': 'static_event_veterinary_visit_id',
    'feed change': 'static_event_feed_change_id',
    'medication': 'static_event_medication_id',
    'purchase': 'static_event_purchase_id',
    'sale': 'static_event_sale_id',
    'transport': 'static_event_transport_id',
    'weaning': 'static_event_weaning_id',
  };
  
  // Default constructor
  EventTypeIsar();
  
  // Named constructor to create a new instance
  factory EventTypeIsar.create({
    String? name,
    String? description,
    Color color = Colors.blue,
  }) {
    // Use static ID if this is a predefined event type
    final String normalizedName = (name ?? '').toLowerCase();
    final String businessId = staticIds[normalizedName] ?? const Uuid().v4();
    
    final eventType = EventTypeIsar()
      ..businessId = businessId
      ..name = name ?? ''
      ..description = description
      ..colorValue = color.value // ignore: deprecated_member_use (value is correct for storing ARGB int)
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();
    
    return eventType;
  }
  
  /// Get color for this event type
  @ignore
  Color get color {
    // Use stored color value if it exists
    if (colorValue != null) {
      return Color(colorValue!);
    }
    
    // Default color
    return Colors.blue;
  }

  /// Set the color
  set color(Color value) {
    colorValue = value.value; // ignore: deprecated_member_use (value is correct for storing ARGB int)
  }
  
  // Convert to Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'businessId': businessId,
      'name': name,
      'description': description,
      'colorValue': colorValue,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }
  
  // Create from Map
  factory EventTypeIsar.fromMap(Map<String, dynamic> map) {
    return EventTypeIsar()
      ..id = map['id'] ?? Isar.autoIncrement
      ..businessId = map['businessId']
      ..name = map['name'] ?? ''
      ..description = map['description']
      ..colorValue = map['colorValue']
      ..createdAt = map['createdAt'] != null
          ? DateTime.parse(map['createdAt'])
          : null
      ..updatedAt = map['updatedAt'] != null
          ? DateTime.parse(map['updatedAt'])
          : null;
  }
  
  // JSON methods
  String toJson() => toMap().toString();
  
  factory EventTypeIsar.fromJson(String source) =>
      EventTypeIsar.fromMap(Map<String, dynamic>.from(source as Map));
      
  // Clone method
  EventTypeIsar clone() {
    return EventTypeIsar()
      ..id = id
      ..businessId = businessId
      ..name = name
      ..description = description
      ..colorValue = colorValue
      ..createdAt = createdAt
      ..updatedAt = updatedAt;
  }
} 