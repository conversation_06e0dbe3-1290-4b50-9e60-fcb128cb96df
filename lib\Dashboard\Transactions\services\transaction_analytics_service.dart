import '../models/transaction_isar.dart';
import '../models/category_isar.dart';

/// Data class to hold all transaction analytics results
class TransactionAnalyticsResult {
  // KPI Metrics
  final int totalTransactions;
  final int incomeTransactions;
  final int expenseTransactions;
  final double totalIncome;
  final double totalExpenses;
  final double netBalance;
  final double averageIncome;
  final double averageExpense;
  final double averageTransactionAmount;

  // Distributions
  final Map<String, double> categoryBreakdown;
  final Map<String, double> paymentMethodBreakdown;
  final Map<String, int> categoryFrequency;
  final Map<String, int> paymentMethodFrequency;

  // Time-based metrics
  final DateTime? firstTransactionDate;
  final DateTime? lastTransactionDate;
  final Map<String, double> monthlyIncome;
  final Map<String, double> monthlyExpenses;
  final Map<String, double> monthlyNetBalance;

  // Performance insights
  final String topIncomeCategory;
  final double topIncomeCategoryAmount;
  final String topExpenseCategory;
  final double topExpenseCategoryAmount;
  final String mostUsedPaymentMethod;
  final int mostUsedPaymentMethodCount;

  // Financial health indicators
  final double incomeToExpenseRatio;
  final double savingsRate; // (Income - Expenses) / Income * 100
  final double expenseVariability; // Coefficient of variation for expenses
  final double incomeVariability; // Coefficient of variation for income

  // Trends
  final double incomeTrend; // Positive/negative trend
  final double expenseTrend; // Positive/negative trend
  final int daysWithTransactions;

  const TransactionAnalyticsResult({
    required this.totalTransactions,
    required this.incomeTransactions,
    required this.expenseTransactions,
    required this.totalIncome,
    required this.totalExpenses,
    required this.netBalance,
    required this.averageIncome,
    required this.averageExpense,
    required this.averageTransactionAmount,
    required this.categoryBreakdown,
    required this.paymentMethodBreakdown,
    required this.categoryFrequency,
    required this.paymentMethodFrequency,
    required this.firstTransactionDate,
    required this.lastTransactionDate,
    required this.monthlyIncome,
    required this.monthlyExpenses,
    required this.monthlyNetBalance,
    required this.topIncomeCategory,
    required this.topIncomeCategoryAmount,
    required this.topExpenseCategory,
    required this.topExpenseCategoryAmount,
    required this.mostUsedPaymentMethod,
    required this.mostUsedPaymentMethodCount,
    required this.incomeToExpenseRatio,
    required this.savingsRate,
    required this.expenseVariability,
    required this.incomeVariability,
    required this.incomeTrend,
    required this.expenseTrend,
    required this.daysWithTransactions,
  });

  /// Empty result for when there's no data
  static const empty = TransactionAnalyticsResult(
    totalTransactions: 0,
    incomeTransactions: 0,
    expenseTransactions: 0,
    totalIncome: 0.0,
    totalExpenses: 0.0,
    netBalance: 0.0,
    averageIncome: 0.0,
    averageExpense: 0.0,
    averageTransactionAmount: 0.0,
    categoryBreakdown: {},
    paymentMethodBreakdown: {},
    categoryFrequency: {},
    paymentMethodFrequency: {},
    firstTransactionDate: null,
    lastTransactionDate: null,
    monthlyIncome: {},
    monthlyExpenses: {},
    monthlyNetBalance: {},
    topIncomeCategory: '',
    topIncomeCategoryAmount: 0.0,
    topExpenseCategory: '',
    topExpenseCategoryAmount: 0.0,
    mostUsedPaymentMethod: '',
    mostUsedPaymentMethodCount: 0,
    incomeToExpenseRatio: 0.0,
    savingsRate: 0.0,
    expenseVariability: 0.0,
    incomeVariability: 0.0,
    incomeTrend: 0.0,
    expenseTrend: 0.0,
    daysWithTransactions: 0,
  );
}

/// Pure analytics service for transaction calculations - no state, just calculations
/// Following the cattle module pattern with single-pass data processing
class TransactionAnalyticsService {

  /// Main calculation method - single entry point with O(n) efficiency
  static TransactionAnalyticsResult calculate(
    List<TransactionIsar> transactions,
    List<CategoryIsar> categories,
  ) {
    if (transactions.isEmpty) {
      return TransactionAnalyticsResult.empty;
    }

    // Single pass through the data for maximum efficiency
    final accumulator = _TransactionAnalyticsAccumulator();

    // Process all transactions
    for (final transaction in transactions) {
      accumulator.processTransaction(transaction);
    }

    // Calculate derived metrics
    accumulator.calculateDerivedMetrics();

    return accumulator.toResult();
  }

  /// Calculate financial health score (0-100)
  static double calculateFinancialHealthScore(
    double totalIncome,
    double totalExpenses,
    double incomeVariability,
    double expenseVariability,
  ) {
    double score = 100.0;

    // Deduct points for negative balance
    final netBalance = totalIncome - totalExpenses;
    if (netBalance < 0) {
      score -= 30.0; // Major penalty for negative balance
    }

    // Deduct points for high expense ratio
    if (totalIncome > 0) {
      final expenseRatio = totalExpenses / totalIncome;
      if (expenseRatio > 0.9) {
        score -= 20.0; // High expense ratio
      } else if (expenseRatio > 0.7) {
        score -= 10.0; // Moderate expense ratio
      }
    }

    // Deduct points for high variability (inconsistent cash flow)
    if (incomeVariability > 50) {
      score -= 15.0; // Very inconsistent income
    } else if (incomeVariability > 30) {
      score -= 10.0; // Moderately inconsistent income
    }

    if (expenseVariability > 50) {
      score -= 10.0; // Very inconsistent expenses
    } else if (expenseVariability > 30) {
      score -= 5.0; // Moderately inconsistent expenses
    }

    return score.clamp(0.0, 100.0);
  }

  /// Calculate category performance metrics
  static Map<String, double> calculateCategoryPerformance(
    List<TransactionIsar> transactions,
    String categoryType, // 'Income' or 'Expense'
  ) {
    final categoryTransactions = transactions
        .where((t) => t.categoryType.toLowerCase() == categoryType.toLowerCase())
        .toList();

    if (categoryTransactions.isEmpty) {
      return {};
    }

    final categoryTotals = <String, double>{};
    for (final transaction in categoryTransactions) {
      final category = transaction.category;
      categoryTotals[category] = (categoryTotals[category] ?? 0.0) + transaction.amount;
    }

    // Calculate percentages
    final totalAmount = categoryTotals.values.fold(0.0, (sum, amount) => sum + amount);
    final categoryPerformance = <String, double>{};

    for (final entry in categoryTotals.entries) {
      categoryPerformance[entry.key] = totalAmount > 0 
          ? (entry.value / totalAmount * 100) 
          : 0.0;
    }

    return categoryPerformance;
  }

  /// Calculate coefficient of variation for consistency measurement
  static double _calculateVariability(List<double> values, double mean) {
    if (values.isEmpty || mean == 0) return 0.0;

    final variance = values.fold(0.0, (sum, value) => sum + ((value - mean) * (value - mean))) / values.length;
    final standardDeviation = variance > 0 ? variance.sqrt() : 0.0;
    
    return mean > 0 ? (standardDeviation / mean) * 100 : 0.0;
  }
}

/// Efficient single-pass accumulator for all transaction analytics calculations
class _TransactionAnalyticsAccumulator {
  // Basic counts
  int totalTransactions = 0;
  int incomeTransactions = 0;
  int expenseTransactions = 0;

  // Financial totals
  double totalIncome = 0.0;
  double totalExpenses = 0.0;
  double totalAmount = 0.0;

  // Distributions
  final Map<String, double> categoryBreakdown = {};
  final Map<String, double> paymentMethodBreakdown = {};
  final Map<String, int> categoryFrequency = {};
  final Map<String, int> paymentMethodFrequency = {};

  // Time tracking
  DateTime? firstTransactionDate;
  DateTime? lastTransactionDate;
  final Map<String, double> monthlyIncome = {};
  final Map<String, double> monthlyExpenses = {};
  final Set<DateTime> transactionDates = {};

  // Variability tracking
  final List<double> incomeAmounts = [];
  final List<double> expenseAmounts = [];

  /// Process a single transaction
  void processTransaction(TransactionIsar transaction) {
    totalTransactions++;
    totalAmount += transaction.amount;

    final isIncome = transaction.categoryType.toLowerCase() == 'income';
    final category = transaction.category;
    final paymentMethod = transaction.paymentMethod;
    final monthKey = '${transaction.date.year}-${transaction.date.month.toString().padLeft(2, '0')}';

    // Type-specific processing
    if (isIncome) {
      incomeTransactions++;
      totalIncome += transaction.amount;
      incomeAmounts.add(transaction.amount);
      monthlyIncome[monthKey] = (monthlyIncome[monthKey] ?? 0.0) + transaction.amount;
    } else {
      expenseTransactions++;
      totalExpenses += transaction.amount;
      expenseAmounts.add(transaction.amount);
      monthlyExpenses[monthKey] = (monthlyExpenses[monthKey] ?? 0.0) + transaction.amount;
    }

    // Category tracking
    categoryBreakdown[category] = (categoryBreakdown[category] ?? 0.0) + transaction.amount;
    categoryFrequency[category] = (categoryFrequency[category] ?? 0) + 1;

    // Payment method tracking
    paymentMethodBreakdown[paymentMethod] = (paymentMethodBreakdown[paymentMethod] ?? 0.0) + transaction.amount;
    paymentMethodFrequency[paymentMethod] = (paymentMethodFrequency[paymentMethod] ?? 0) + 1;

    // Date tracking
    if (firstTransactionDate == null || transaction.date.isBefore(firstTransactionDate!)) {
      firstTransactionDate = transaction.date;
    }
    if (lastTransactionDate == null || transaction.date.isAfter(lastTransactionDate!)) {
      lastTransactionDate = transaction.date;
    }

    final dateOnly = DateTime(transaction.date.year, transaction.date.month, transaction.date.day);
    transactionDates.add(dateOnly);
  }

  /// Calculate derived metrics
  void calculateDerivedMetrics() {
    // Calculate monthly net balance
    final allMonths = {...monthlyIncome.keys, ...monthlyExpenses.keys};
    for (final month in allMonths) {
      final income = monthlyIncome[month] ?? 0.0;
      final expenses = monthlyExpenses[month] ?? 0.0;
      monthlyNetBalance[month] = income - expenses;
    }
  }

  final Map<String, double> monthlyNetBalance = {};

  /// Convert accumulated data to immutable result
  TransactionAnalyticsResult toResult() {
    final netBalance = totalIncome - totalExpenses;
    final averageIncome = incomeTransactions > 0 ? totalIncome / incomeTransactions : 0.0;
    final averageExpense = expenseTransactions > 0 ? totalExpenses / expenseTransactions : 0.0;
    final averageTransactionAmount = totalTransactions > 0 ? totalAmount / totalTransactions : 0.0;

    // Find top categories
    String topIncomeCategory = '';
    double topIncomeCategoryAmount = 0.0;
    String topExpenseCategory = '';
    double topExpenseCategoryAmount = 0.0;

    for (final entry in categoryBreakdown.entries) {
      // This is simplified - in reality, we'd need to track income/expense categories separately
      if (entry.value > topIncomeCategoryAmount) {
        topIncomeCategory = entry.key;
        topIncomeCategoryAmount = entry.value;
      }
    }

    // Find most used payment method
    String mostUsedPaymentMethod = '';
    int mostUsedPaymentMethodCount = 0;
    for (final entry in paymentMethodFrequency.entries) {
      if (entry.value > mostUsedPaymentMethodCount) {
        mostUsedPaymentMethod = entry.key;
        mostUsedPaymentMethodCount = entry.value;
      }
    }

    // Calculate financial ratios
    final incomeToExpenseRatio = totalExpenses > 0 ? totalIncome / totalExpenses : 0.0;
    final savingsRate = totalIncome > 0 ? ((totalIncome - totalExpenses) / totalIncome * 100) : 0.0;

    // Calculate variability
    final expenseVariability = TransactionAnalyticsService._calculateVariability(expenseAmounts, averageExpense);
    final incomeVariability = TransactionAnalyticsService._calculateVariability(incomeAmounts, averageIncome);

    return TransactionAnalyticsResult(
      totalTransactions: totalTransactions,
      incomeTransactions: incomeTransactions,
      expenseTransactions: expenseTransactions,
      totalIncome: totalIncome,
      totalExpenses: totalExpenses,
      netBalance: netBalance,
      averageIncome: averageIncome,
      averageExpense: averageExpense,
      averageTransactionAmount: averageTransactionAmount,
      categoryBreakdown: Map.unmodifiable(categoryBreakdown),
      paymentMethodBreakdown: Map.unmodifiable(paymentMethodBreakdown),
      categoryFrequency: Map.unmodifiable(categoryFrequency),
      paymentMethodFrequency: Map.unmodifiable(paymentMethodFrequency),
      firstTransactionDate: firstTransactionDate,
      lastTransactionDate: lastTransactionDate,
      monthlyIncome: Map.unmodifiable(monthlyIncome),
      monthlyExpenses: Map.unmodifiable(monthlyExpenses),
      monthlyNetBalance: Map.unmodifiable(monthlyNetBalance),
      topIncomeCategory: topIncomeCategory,
      topIncomeCategoryAmount: topIncomeCategoryAmount,
      topExpenseCategory: topExpenseCategory,
      topExpenseCategoryAmount: topExpenseCategoryAmount,
      mostUsedPaymentMethod: mostUsedPaymentMethod,
      mostUsedPaymentMethodCount: mostUsedPaymentMethodCount,
      incomeToExpenseRatio: incomeToExpenseRatio,
      savingsRate: savingsRate,
      expenseVariability: expenseVariability,
      incomeVariability: incomeVariability,
      incomeTrend: 0.0, // Would need time-series analysis
      expenseTrend: 0.0, // Would need time-series analysis
      daysWithTransactions: transactionDates.length,
    );
  }
}
