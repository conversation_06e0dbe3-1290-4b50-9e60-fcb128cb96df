import 'package:flutter/material.dart';

enum StatItemSize { small, medium, large }

class StatItem extends StatelessWidget {
  final String label;
  final String value;
  final IconData icon;
  final Color color;

  // Enhanced features
  final StatItemSize size;
  final String? tooltip;
  final VoidCallback? onTap;
  final Widget? badge;
  final String? description;
  final TextStyle? valueStyle;
  final TextStyle? labelStyle;
  final bool centerText;
  final double? circleRadius;
  final double? iconSize;

  const StatItem({
    super.key,
    required this.label,
    required this.value,
    required this.icon,
    required this.color,
    this.size = StatItemSize.medium,
    this.tooltip,
    this.onTap,
    this.badge,
    this.description,
    this.valueStyle,
    this.labelStyle,
    this.centerText = true,
    this.circleRadius,
    this.iconSize,
  });

  @override
  Widget build(BuildContext context) {
    // Determine sizes based on the size variant
    final double actualRadius = circleRadius ?? _getRadius();
    final double actualIconSize = iconSize ?? _getIconSize();
    final double valueFontSize = _getValueFontSize();
    final double labelFontSize = _getLabelFontSize();

    final Widget statItem = Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Avatar with icon
        Stack(
          clipBehavior: Clip.none,
          children: [
            CircleAvatar(
              radius: actualRadius,
              backgroundColor: color.withAlpha(51), // 0.2 * 255 = 51
              child: Icon(
                icon,
                color: color,
                size: actualIconSize,
              ),
            ),
            // Positioned badge if provided
            if (badge != null)
              Positioned(
                top: -5,
                right: -5,
                child: badge!,
              ),
          ],
        ),
        const SizedBox(height: 8),
        // Value text
        Text(
          value,
          style: valueStyle ??
              TextStyle(
                fontSize: valueFontSize,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
          textAlign: centerText ? TextAlign.center : TextAlign.start,
        ),
        const SizedBox(height: 4),
        // Label text
        Text(
          label,
          style: labelStyle ??
              TextStyle(
                fontSize: labelFontSize,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
          textAlign: centerText ? TextAlign.center : TextAlign.start,
        ),
        // Optional description
        if (description != null) ...[
          const SizedBox(height: 4),
          Text(
            description!,
            style: TextStyle(
              fontSize: labelFontSize - 1,
              color: Colors.grey[500],
              fontStyle: FontStyle.italic,
            ),
            textAlign: centerText ? TextAlign.center : TextAlign.start,
          ),
        ],
      ],
    );

    // Apply tooltip if provided
    final Widget wrappedStatItem = tooltip != null
        ? Tooltip(
            message: tooltip!,
            child: statItem,
          )
        : statItem;

    // Apply tap handler if provided
    final Widget interactiveStatItem = onTap != null
        ? InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(actualRadius),
            child: wrappedStatItem,
          )
        : wrappedStatItem;

    // Don't wrap in Expanded - this should be handled by the parent widget
    // based on the layout type (row, grid, wrap)
    return interactiveStatItem;
  }

  // Helper methods to determine sizes based on the selected size variant
  double _getRadius() {
    switch (size) {
      case StatItemSize.small:
        return 16;
      case StatItemSize.medium:
        return 20;
      case StatItemSize.large:
        return 24;
    }
  }

  double _getIconSize() {
    switch (size) {
      case StatItemSize.small:
        return 16;
      case StatItemSize.medium:
        return 20;
      case StatItemSize.large:
        return 28;
    }
  }

  double _getValueFontSize() {
    switch (size) {
      case StatItemSize.small:
        return 14;
      case StatItemSize.medium:
        return 16;
      case StatItemSize.large:
        return 20;
    }
  }

  double _getLabelFontSize() {
    switch (size) {
      case StatItemSize.small:
        return 12;
      case StatItemSize.medium:
        return 13;
      case StatItemSize.large:
        return 14;
    }
  }
}
