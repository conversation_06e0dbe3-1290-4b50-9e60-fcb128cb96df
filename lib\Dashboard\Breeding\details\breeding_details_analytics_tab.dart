import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../models/breeding_record_isar.dart';
import '../models/pregnancy_record_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../services/breeding_repository.dart';
import '../../Cattle/services/cattle_repository.dart';
import '../../widgets/index.dart';
import '../../../constants/app_tabs.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';

class BreedingDetailsAnalyticsTab extends StatefulWidget {
  final BreedingRecordIsar? breedingRecord;
  final PregnancyRecordIsar? pregnancyRecord;
  final CattleIsar? cattle;

  const BreedingDetailsAnalyticsTab({
    Key? key,
    this.breedingRecord,
    this.pregnancyRecord,
    this.cattle,
  }) : super(key: key);

  @override
  State<BreedingDetailsAnalyticsTab> createState() => _BreedingDetailsAnalyticsTabState();
}

class _BreedingDetailsAnalyticsTabState extends State<BreedingDetailsAnalyticsTab>
    with AutomaticKeepAliveClientMixin {
  
  final BreedingRepository _breedingRepository = GetIt.instance<BreedingRepository>();
  final CattleRepository _cattleRepository = GetIt.instance<CattleRepository>();

  List<BreedingRecordIsar> _allBreedingRecords = [];
  List<PregnancyRecordIsar> _allPregnancyRecords = [];

  bool _isLoading = true;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _loadAnalyticsData();
  }

  Future<void> _loadAnalyticsData() async {
    try {
      setState(() => _isLoading = true);

      final cattleId = widget.cattle?.businessId ??
                      widget.breedingRecord?.cattleId ??
                      widget.pregnancyRecord?.cattleId;

      if (cattleId != null) {
        // Load all breeding and pregnancy records for this cattle
        final results = await Future.wait([
          _breedingRepository.getBreedingRecordsForCattle(cattleId),
          _breedingRepository.getPregnancyRecordsForCattle(cattleId),
          _cattleRepository.getAllCattle(),
        ]);

        if (mounted) {
          setState(() {
            _allBreedingRecords = results[0] as List<BreedingRecordIsar>;
            _allPregnancyRecords = results[1] as List<PregnancyRecordIsar>;
            // Related cattle data loaded but not stored locally
            _isLoading = false;
          });
        }
      } else {
        if (mounted) {
          setState(() => _isLoading = false);
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    if (_isLoading) {
      return UniversalLoadingIndicator.breeding();
    }

    final hasAnyData = _allBreedingRecords.isNotEmpty || _allPregnancyRecords.isNotEmpty;

    if (!hasAnyData) {
      return UniversalEmptyState.breeding(
        title: 'No Breeding Analytics Data',
        message: 'Add breeding records for ${widget.cattle?.name ?? 'this cattle'} to see comprehensive analytics.',
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'Breeding Analytics for ${widget.cattle?.name ?? 'Cattle'}',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),

          // Summary Cards
          _buildSummaryCards(),
          const SizedBox(height: 24),

          // Breeding Success Rate
          _buildSuccessRateCard(),
          const SizedBox(height: 24),

          // Method Distribution
          if (_allBreedingRecords.isNotEmpty) ...[
            _buildMethodDistribution(),
            const SizedBox(height: 24),
          ],

          // Pregnancy Timeline
          if (_allPregnancyRecords.isNotEmpty) ...[
            _buildPregnancyTimeline(),
            const SizedBox(height: 24),
          ],

          // Breeding Insights
          _buildBreedingInsights(),
        ],
      ),
    );
  }

  Widget _buildSummaryCards() {
    final totalBreeding = _allBreedingRecords.length;
    final totalPregnancies = _allPregnancyRecords.length;
    final activePregnancies = _allPregnancyRecords.where((p) => p.status?.toLowerCase() == 'active').length;
    final completedPregnancies = _allPregnancyRecords.where((p) => p.status?.toLowerCase() == 'completed').length;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Breeding Summary',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildSummaryCard(
                'Breeding Records',
                totalBreeding.toString(),
                Icons.favorite,
                UniversalEmptyStateTheme.breeding,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildSummaryCard(
                'Pregnancies',
                totalPregnancies.toString(),
                Icons.pregnant_woman,
                Colors.purple,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildSummaryCard(
                'Active',
                activePregnancies.toString(),
                Icons.pending,
                Colors.orange,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildSummaryCard(
                'Completed',
                completedPregnancies.toString(),
                Icons.child_care,
                Colors.green,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSuccessRateCard() {
    final breedingCount = _allBreedingRecords.length;
    final pregnancyCount = _allPregnancyRecords.length;
    final successRate = breedingCount > 0 ? (pregnancyCount / breedingCount * 100) : 0.0;

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Breeding Success Rate',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Column(
                    children: [
                      Text(
                        '${successRate.toStringAsFixed(1)}%',
                        style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: _getSuccessRateColor(successRate),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Success Rate',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    children: [
                      Text(
                        '$pregnancyCount / $breedingCount',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Pregnancies / Breedings',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            LinearProgressIndicator(
              value: successRate / 100,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(_getSuccessRateColor(successRate)),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMethodDistribution() {
    final methodDistribution = <String, int>{};
    for (final record in _allBreedingRecords) {
      final method = record.method ?? 'Unknown';
      methodDistribution[method] = (methodDistribution[method] ?? 0) + 1;
    }

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Breeding Method Distribution',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            if (methodDistribution.isNotEmpty)
              SizedBox(
                height: 200,
                child: PieChart(
                  PieChartData(
                    sections: _buildPieChartSections(methodDistribution),
                    centerSpaceRadius: 40,
                    sectionsSpace: 2,
                  ),
                ),
              ),
            const SizedBox(height: 16),
            _buildLegend(methodDistribution),
          ],
        ),
      ),
    );
  }

  Widget _buildPregnancyTimeline() {
    final sortedPregnancies = List<PregnancyRecordIsar>.from(_allPregnancyRecords)
      ..sort((a, b) => (a.startDate ?? DateTime.now()).compareTo(b.startDate ?? DateTime.now()));

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Pregnancy Timeline',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...sortedPregnancies.take(5).map((pregnancy) => _buildPregnancyTimelineItem(pregnancy)),
          ],
        ),
      ),
    );
  }

  Widget _buildPregnancyTimelineItem(PregnancyRecordIsar pregnancy) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: _getPregnancyStatusColor(pregnancy.status),
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Breeding: ${_formatDate(pregnancy.startDate)}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (pregnancy.expectedCalvingDate != null)
                  Text(
                    'Expected Calving: ${_formatDate(pregnancy.expectedCalvingDate)}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                Text(
                  'Status: ${pregnancy.status ?? 'Unknown'}',
                  style: TextStyle(
                    fontSize: 12,
                    color: _getPregnancyStatusColor(pregnancy.status),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBreedingInsights() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Breeding Insights',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ..._generateInsights(),
          ],
        ),
      ),
    );
  }

  List<Widget> _generateInsights() {
    final insights = <Widget>[];
    
    // Success rate insight
    final breedingCount = _allBreedingRecords.length;
    final pregnancyCount = _allPregnancyRecords.length;
    final successRate = breedingCount > 0 ? (pregnancyCount / breedingCount * 100) : 0.0;

    insights.add(_buildInsightItem(
      'Success Rate Analysis',
      successRate > 80 ? 'Excellent breeding success rate' : 
      successRate > 60 ? 'Good breeding success rate' : 'Consider improving breeding practices',
      Icons.trending_up,
      successRate > 80 ? Colors.green : successRate > 60 ? Colors.orange : Colors.red,
    ));

    // Recent activity
    final recentBreedings = _allBreedingRecords.where((r) {
      if (r.date == null) return false;
      final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
      return r.date!.isAfter(thirtyDaysAgo);
    }).length;

    insights.add(_buildInsightItem(
      'Recent Activity',
      '$recentBreedings breeding records in the last 30 days',
      Icons.timeline,
      recentBreedings > 0 ? Colors.green : Colors.orange,
    ));

    // Active pregnancies
    final activePregnancies = _allPregnancyRecords.where((p) => p.status?.toLowerCase() == 'active').length;

    insights.add(_buildInsightItem(
      'Active Pregnancies',
      '$activePregnancies pregnancies currently active',
      Icons.pregnant_woman,
      activePregnancies > 0 ? Colors.purple : Colors.grey,
    ));

    return insights;
  }

  Widget _buildInsightItem(String title, String description, IconData icon, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<PieChartSectionData> _buildPieChartSections(Map<String, int> data) {
    final colors = [Colors.blue, Colors.green, Colors.orange, Colors.red, Colors.purple];
    final total = data.values.fold(0, (sum, value) => sum + value);
    
    return data.entries.toList().asMap().entries.map((entry) {
      final index = entry.key;
      final mapEntry = entry.value;
      final percentage = (mapEntry.value / total) * 100;
      
      return PieChartSectionData(
        color: colors[index % colors.length],
        value: mapEntry.value.toDouble(),
        title: '${percentage.toStringAsFixed(1)}%',
        radius: 60,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );
    }).toList();
  }

  Widget _buildLegend(Map<String, int> data) {
    final colors = [Colors.blue, Colors.green, Colors.orange, Colors.red, Colors.purple];
    
    return Wrap(
      spacing: 16,
      runSpacing: 8,
      children: data.entries.toList().asMap().entries.map((entry) {
        final index = entry.key;
        final mapEntry = entry.value;
        
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 16,
              height: 16,
              decoration: BoxDecoration(
                color: colors[index % colors.length],
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 8),
            Text('${mapEntry.key} (${mapEntry.value})'),
          ],
        );
      }).toList(),
    );
  }

  Color _getSuccessRateColor(double rate) {
    if (rate >= 80) return Colors.green;
    if (rate >= 60) return Colors.orange;
    return Colors.red;
  }

  Color _getPregnancyStatusColor(String? status) {
    switch (status?.toLowerCase()) {
      case 'active':
        return Colors.orange;
      case 'confirmed':
        return Colors.blue;
      case 'completed':
        return Colors.green;
      case 'failed':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'Unknown';
    return DateFormat('MMM dd, yyyy').format(date);
  }
}
