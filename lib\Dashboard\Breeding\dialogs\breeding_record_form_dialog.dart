import 'package:flutter/material.dart';
import '../models/breeding_record_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../../utils/date_utils.dart';

/// Dialog for adding/editing breeding records
/// Follows the universal form dialog pattern with validation and cattle selection
class BreedingRecordFormDialog extends StatefulWidget {
  final BreedingRecordIsar? breedingRecord;
  final List<CattleIsar> cattle;
  final Function(BreedingRecordIsar) onSave;

  const BreedingRecordFormDialog({
    super.key,
    this.breedingRecord,
    required this.cattle,
    required this.onSave,
  });

  @override
  State<BreedingRecordFormDialog> createState() => _BreedingRecordFormDialogState();
}

class _BreedingRecordFormDialogState extends State<BreedingRecordFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final _notesController = TextEditingController();

  CattleIsar? _selectedCattle;
  CattleIsar? _selectedBull;
  DateTime _breedingDate = DateTime.now();
  String _breedingMethod = 'Natural';
  String _status = 'Pending';
  bool _isLoading = false;

  final List<String> _breedingMethods = ['Natural', 'Artificial Insemination'];
  final List<String> _statusOptions = ['Pending', 'Confirmed', 'Failed', 'Unknown'];

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    if (widget.breedingRecord != null) {
      final record = widget.breedingRecord!;
      _breedingDate = record.breedingDate ?? DateTime.now();
      _breedingMethod = record.breedingMethod ?? 'Natural';
      _status = record.status ?? 'Pending';
      _notesController.text = record.notes ?? '';
      
      // Find selected cattle
      if (record.cattleId != null) {
        _selectedCattle = widget.cattle.firstWhere(
          (cattle) => cattle.businessId == record.cattleId,
          orElse: () => widget.cattle.first,
        );
      }
      
      // Find selected bull
      if (record.bullId != null) {
        _selectedBull = widget.cattle.firstWhere(
          (cattle) => cattle.businessId == record.bullId,
          orElse: () => widget.cattle.first,
        );
      }
    }
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Filter cattle for female selection
    final femaleCattle = widget.cattle.where((cattle) => 
        cattle.gender == CattleGender.female).toList();
    
    // Filter cattle for bull selection
    final maleCattle = widget.cattle.where((cattle) => 
        cattle.gender == CattleGender.male).toList();

    return AlertDialog(
      title: Text(widget.breedingRecord == null ? 'Add Breeding Record' : 'Edit Breeding Record'),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.8,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Female Cattle Selection
                DropdownButtonFormField<CattleIsar>(
                  value: _selectedCattle,
                  decoration: const InputDecoration(
                    labelText: 'Select Female Cattle',
                    border: OutlineInputBorder(),
                  ),
                  items: femaleCattle.map((cattle) {
                    return DropdownMenuItem(
                      value: cattle,
                      child: Text('${cattle.name} (${cattle.tagId})'),
                    );
                  }).toList(),
                  onChanged: (cattle) {
                    setState(() {
                      _selectedCattle = cattle;
                    });
                  },
                  validator: (value) {
                    if (value == null) {
                      return 'Please select a female cattle';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Bull Selection (Optional for AI)
                DropdownButtonFormField<CattleIsar>(
                  value: _selectedBull,
                  decoration: const InputDecoration(
                    labelText: 'Select Bull (Optional)',
                    border: OutlineInputBorder(),
                  ),
                  items: [
                    const DropdownMenuItem<CattleIsar>(
                      value: null,
                      child: Text('No Bull Selected'),
                    ),
                    ...maleCattle.map((cattle) {
                      return DropdownMenuItem(
                        value: cattle,
                        child: Text('${cattle.name} (${cattle.tagId})'),
                      );
                    }),
                  ],
                  onChanged: (cattle) {
                    setState(() {
                      _selectedBull = cattle;
                    });
                  },
                ),
                const SizedBox(height: 16),

                // Breeding Date
                InkWell(
                  onTap: _selectDate,
                  child: InputDecorator(
                    decoration: const InputDecoration(
                      labelText: 'Breeding Date',
                      border: OutlineInputBorder(),
                      suffixIcon: Icon(Icons.calendar_today),
                    ),
                    child: Text(
                      AppDateUtils.formatDate(_breedingDate),
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Breeding Method
                DropdownButtonFormField<String>(
                  value: _breedingMethod,
                  decoration: const InputDecoration(
                    labelText: 'Breeding Method',
                    border: OutlineInputBorder(),
                  ),
                  items: _breedingMethods.map((method) {
                    return DropdownMenuItem(
                      value: method,
                      child: Text(method),
                    );
                  }).toList(),
                  onChanged: (method) {
                    setState(() {
                      _breedingMethod = method!;
                    });
                  },
                ),
                const SizedBox(height: 16),

                // Status
                DropdownButtonFormField<String>(
                  value: _status,
                  decoration: const InputDecoration(
                    labelText: 'Status',
                    border: OutlineInputBorder(),
                  ),
                  items: _statusOptions.map((status) {
                    return DropdownMenuItem(
                      value: status,
                      child: Text(status),
                    );
                  }).toList(),
                  onChanged: (status) {
                    setState(() {
                      _status = status!;
                    });
                  },
                ),
                const SizedBox(height: 16),

                // Notes
                TextFormField(
                  controller: _notesController,
                  decoration: const InputDecoration(
                    labelText: 'Notes (Optional)',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _saveBreedingRecord,
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(widget.breedingRecord == null ? 'Add' : 'Save'),
        ),
      ],
    );
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _breedingDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (date != null) {
      setState(() {
        _breedingDate = date;
      });
    }
  }

  Future<void> _saveBreedingRecord() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final record = widget.breedingRecord ?? BreedingRecordIsar();
      
      record.cattleId = _selectedCattle!.businessId;
      record.bullId = _selectedBull?.businessId;
      record.breedingDate = _breedingDate;
      record.breedingMethod = _breedingMethod;
      record.status = _status;
      record.notes = _notesController.text.isNotEmpty ? _notesController.text : null;
      record.updatedAt = DateTime.now();
      
      if (widget.breedingRecord == null) {
        record.createdAt = DateTime.now();
      }

      widget.onSave(record);
      Navigator.of(context).pop();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error saving breeding record: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
