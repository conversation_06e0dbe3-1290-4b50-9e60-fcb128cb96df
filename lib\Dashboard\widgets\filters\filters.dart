/// Universal Filter System - Core Logic and Models
///
/// This file contains the unified filter controller, state management,
/// and all filter-related models and handlers.

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'filter_constants.dart';

/// Unified filter controller with apply-based filtering
class Filter<PERSON><PERSON><PERSON>er extends ChangeNotifier {
  // Applied (active) state - these are the filters currently in effect
  String _searchQuery = '';
  String? _sortBy;
  bool _isAscending = true;
  DateTime? _startDate;
  DateTime? _endDate;
  final Map<String, dynamic> _activeFilters = {};
  final Map<String, dynamic> _globalFilters = {};

  // Pending state - these are the filters being edited but not yet applied
  String _pendingSearchQuery = '';
  String? _pendingSortBy;
  bool _pendingIsAscending = true;
  DateTime? _pendingStartDate;
  DateTime? _pendingEndDate;
  final Map<String, dynamic> _pendingActiveFilters = {};
  final Map<String, dynamic> _pendingGlobalFilters = {};

  // Getters for applied state
  String get searchQuery => _searchQuery;
  String? get sortBy => _sortBy;
  bool get isAscending => _isAscending;
  DateTime? get startDate => _startDate;
  DateTime? get endDate => _endDate;
  Map<String, dynamic> get activeFilters => Map.unmodifiable(_activeFilters);
  Map<String, dynamic> get globalFilters => Map.unmodifiable(_globalFilters);
  bool get hasActiveFilters => _activeFilters.isNotEmpty || _globalFilters.isNotEmpty;

  /// Centralized logic to check if any filter is currently active
  /// This includes search, date range, sort, and all filter types
  bool get isAnyFilterActive =>
    _searchQuery.isNotEmpty ||
    _startDate != null ||
    _sortBy != null ||
    hasActiveFilters;

  // Getters for pending state (used by UI components)
  String get pendingSearchQuery => _pendingSearchQuery;
  String? get pendingSortBy => _pendingSortBy;
  bool get pendingIsAscending => _pendingIsAscending;
  DateTime? get pendingStartDate => _pendingStartDate;
  DateTime? get pendingEndDate => _pendingEndDate;
  Map<String, dynamic> get pendingActiveFilters => Map.unmodifiable(_pendingActiveFilters);
  Map<String, dynamic> get pendingGlobalFilters => Map.unmodifiable(_pendingGlobalFilters);
  bool get hasPendingChanges => _hasPendingChanges();

  // Check if there are pending changes
  bool _hasPendingChanges() {
    return _pendingSearchQuery != _searchQuery ||
           _pendingSortBy != _sortBy ||
           _pendingIsAscending != _isAscending ||
           _pendingStartDate != _startDate ||
           _pendingEndDate != _endDate ||
           !_mapsEqual(_pendingActiveFilters, _activeFilters) ||
           !_mapsEqual(_pendingGlobalFilters, _globalFilters);
  }

  // Helper method to compare maps
  bool _mapsEqual(Map<String, dynamic> map1, Map<String, dynamic> map2) {
    if (map1.length != map2.length) return false;
    for (final key in map1.keys) {
      if (map1[key] != map2[key]) return false;
    }
    return true;
  }

  // Initialize pending state from current applied state
  void initializePendingState() {
    _pendingSearchQuery = _searchQuery;
    _pendingSortBy = _sortBy;
    _pendingIsAscending = _isAscending;
    _pendingStartDate = _startDate;
    _pendingEndDate = _endDate;
    _pendingActiveFilters.clear();
    _pendingActiveFilters.addAll(_activeFilters);
    _pendingGlobalFilters.clear();
    _pendingGlobalFilters.addAll(_globalFilters);
  }

  // Search methods (now work with pending state)
  void setSearchQuery(String query) {
    if (_pendingSearchQuery != query) {
      _pendingSearchQuery = query;
      // Don't notify listeners - changes are pending
    }
  }

  // Sort methods (now work with pending state)
  void setSortBy(String? sortField) {
    debugPrint('🔍 FilterController.setSortBy() called with: $sortField');
    debugPrint('🔍 Current _pendingSortBy: $_pendingSortBy');

    if (_pendingSortBy != sortField) {
      _pendingSortBy = sortField;
      debugPrint('🔍 Updated _pendingSortBy to: $_pendingSortBy');
      // Don't notify listeners - changes are pending
    } else {
      debugPrint('🔍 No change needed - _pendingSortBy already: $_pendingSortBy');
    }
  }

  void setSortDirection(bool ascending) {
    if (_pendingIsAscending != ascending) {
      _pendingIsAscending = ascending;
      // Don't notify listeners - changes are pending
    }
  }

  /// Set sort direction and apply immediately (for interactive sort arrows)
  void setSortDirectionAndApply(bool ascending) {
    if (_isAscending != ascending) {
      _pendingIsAscending = ascending;
      _isAscending = ascending;
      notifyListeners();
    }
  }

  // Date methods (now work with pending state)
  void setDateRange(DateTime? start, DateTime? end) {
    if (_pendingStartDate != start || _pendingEndDate != end) {
      _pendingStartDate = start;
      _pendingEndDate = end;
      // Don't notify listeners - changes are pending
    }
  }

  // Global filter methods (now work with pending state)
  void updateFilter(String key, dynamic value) {
    if (_pendingActiveFilters[key] != value) {
      if (value == null || value == 'All') {
        _pendingActiveFilters.remove(key);
      } else {
        _pendingActiveFilters[key] = value;
      }
      // Don't notify listeners - changes are pending
    }
  }

  // Global filter methods
  void updateGlobalFilter(String key, dynamic value) {
    if (_pendingGlobalFilters[key] != value) {
      if (value == null || value == 'All') {
        _pendingGlobalFilters.remove(key);
      } else {
        _pendingGlobalFilters[key] = value;
      }
      // Don't notify listeners - changes are pending
    }
  }

  // Apply pending changes to active state
  void applyFilters() {
    bool hasChanges = _hasPendingChanges();

    debugPrint('🔍 FilterController.applyFilters() - hasChanges: $hasChanges');
    debugPrint('🔍 Before apply - sortBy: $_sortBy, pendingSortBy: $_pendingSortBy');

    if (hasChanges) {
      _searchQuery = _pendingSearchQuery;
      _sortBy = _pendingSortBy;
      _isAscending = _pendingIsAscending;
      _startDate = _pendingStartDate;
      _endDate = _pendingEndDate;

      _activeFilters.clear();
      _activeFilters.addAll(_pendingActiveFilters);

      _globalFilters.clear();
      _globalFilters.addAll(_pendingGlobalFilters);

      debugPrint('🔍 After apply - sortBy: $_sortBy, isAscending: $_isAscending');
      debugPrint('🔍 FilterController.applyFilters() - calling notifyListeners()');

      notifyListeners(); // Only notify when filters are actually applied
    } else {
      debugPrint('🔍 FilterController.applyFilters() - no changes to apply');
    }
  }

  // Discard pending changes and revert to applied state
  void discardPendingChanges() {
    initializePendingState();
    // No need to notify listeners since applied state hasn't changed
  }

  // Clear methods (now work with pending state)
  void clearGlobalFilters() {
    if (_pendingGlobalFilters.isNotEmpty) {
      _pendingGlobalFilters.clear();
      // Don't notify listeners - changes are pending
    }
  }

  void clearDateRange() {
    if (_pendingStartDate != null || _pendingEndDate != null) {
      _pendingStartDate = null;
      _pendingEndDate = null;
      // Don't notify listeners - changes are pending
    }
  }

  void clearSort() {
    if (_pendingSortBy != null || !_pendingIsAscending) {
      _pendingSortBy = null;
      _pendingIsAscending = true;
      // Don't notify listeners - changes are pending
    }
  }

  // Constructor to initialize pending state
  FilterController() {
    initializePendingState();
  }

  // Clear all pending filters
  void clearAll() {
    _pendingSearchQuery = '';
    _pendingSortBy = null;
    _pendingIsAscending = true;
    _pendingStartDate = null;
    _pendingEndDate = null;
    _pendingActiveFilters.clear();
    _pendingGlobalFilters.clear();
    // Don't notify listeners - changes are pending
  }

  // Clear all applied filters (for immediate clearing without dialog)
  void clearAllApplied() {
    bool hasChanges = false;
    if (_searchQuery.isNotEmpty) {
      _searchQuery = '';
      hasChanges = true;
    }
    if (_sortBy != null) {
      _sortBy = null;
      hasChanges = true;
    }
    if (!_isAscending) {
      _isAscending = true;
      hasChanges = true;
    }
    if (_startDate != null || _endDate != null) {
      _startDate = null;
      _endDate = null;
      hasChanges = true;
    }
    if (_activeFilters.isNotEmpty) {
      _activeFilters.clear();
      hasChanges = true;
    }
    if (_globalFilters.isNotEmpty) {
      _globalFilters.clear();
      hasChanges = true;
    }
    if (hasChanges) {
      initializePendingState(); // Sync pending state
      notifyListeners();
    }
  }
}



/// Theme definitions for consistent styling
class FilterTheme {
  final Color color;

  const FilterTheme({required this.color});

  static const FilterTheme cattle = FilterTheme(color: Colors.green);
  static const FilterTheme breeding = FilterTheme(color: Colors.pink);
  static const FilterTheme health = FilterTheme(color: Colors.red);
  static const FilterTheme milk = FilterTheme(color: Colors.blue);
  static const FilterTheme weight = FilterTheme(color: Colors.orange);
  static const FilterTheme transaction = FilterTheme(color: Colors.purple);
}

/// Filter option for dropdowns
class FilterOption {
  final String value;
  final String label;
  final IconData? icon;
  final String? description;

  const FilterOption({
    required this.value,
    required this.label,
    this.icon,
    this.description,
  });
}

/// Sort field definition
class SortField {
  final String key;
  final String label;
  final IconData icon;
  final String? description;

  const SortField({
    required this.key,
    required this.label,
    required this.icon,
    this.description,
  });

  // Common sort fields for most modules
  static const List<SortField> commonFields = [
    SortField(
      key: 'name',
      label: 'Name',
      icon: Icons.sort_by_alpha,
      description: 'Sort alphabetically by name',
    ),
    SortField(
      key: 'createdAt',
      label: 'Date Added',
      icon: Icons.add_circle_outline,
      description: 'Sort by when the record was created',
    ),
  ];

  // Cattle-specific sort fields
  static const List<SortField> cattleFields = [
    SortField(
      key: 'tagId',
      label: 'Tag ID',
      icon: Icons.tag,
      description: 'Sort by cattle tag identifier',
    ),
    SortField(
      key: 'dateOfBirth',
      label: 'Date of Birth',
      icon: Icons.cake,
      description: 'Sort by cattle birth date',
    ),
    SortField(
      key: 'purchaseDate',
      label: 'Purchase Date',
      icon: Icons.shopping_cart,
      description: 'Sort by when cattle was purchased',
    ),
  ];

  // Transaction-specific sort fields
  static const List<SortField> transactionFields = [
    SortField(
      key: 'date',
      label: 'Transaction Date',
      icon: Icons.date_range,
      description: 'Sort by transaction date',
    ),
    SortField(
      key: 'amount',
      label: 'Amount',
      icon: Icons.attach_money,
      description: 'Sort by transaction amount',
    ),
    SortField(
      key: 'category',
      label: 'Category',
      icon: Icons.category,
      description: 'Sort by transaction category',
    ),
    SortField(
      key: 'description',
      label: 'Description',
      icon: Icons.description,
      description: 'Sort by transaction description',
    ),
  ];

  // Weight-specific sort fields
  static const List<SortField> weightFields = [
    SortField(
      key: 'measurementDate',
      label: 'Measurement Date',
      icon: Icons.date_range,
      description: 'Sort by when weight was measured',
    ),
    SortField(
      key: 'weight',
      label: 'Weight',
      icon: Icons.monitor_weight,
      description: 'Sort by weight value',
    ),
    SortField(
      key: 'weightGain',
      label: 'Weight Gain',
      icon: Icons.trending_up,
      description: 'Sort by weight gain amount',
    ),
    SortField(
      key: 'measurementMethod',
      label: 'Measurement Method',
      icon: Icons.scale,
      description: 'Sort by how weight was measured',
    ),
  ];
}

/// Date range option for predefined selections
class DateRangeOption {
  final String label;
  final IconData icon;
  final DateTime startDate;
  final DateTime endDate;
  final String? description;

  const DateRangeOption({
    required this.label,
    required this.icon,
    required this.startDate,
    required this.endDate,
    this.description,
  });
}

/// Global filter options for all modules
/// NOTE: This class now serves as a fallback. Use FilterDataService for dynamic loading.
class GlobalFilterOptions {
  // Static fallback options (used only if dynamic loading fails)
  static const List<FilterOption> fallbackAnimalTypes = [
    FilterOption(value: 'All', label: 'All Animal Types'),
    FilterOption(value: 'type_cow', label: 'Cattle'),
    FilterOption(value: 'type_goat', label: 'Goat'),
    FilterOption(value: 'type_sheep', label: 'Sheep'),
  ];

  static const List<FilterOption> fallbackBreeds = [
    FilterOption(value: 'All', label: 'All Breeds'),
    FilterOption(value: 'unknown', label: 'Unknown Breed'),
  ];

  static const List<FilterOption> fallbackCattle = [
    FilterOption(value: 'All', label: 'All Cattle'),
  ];

  // These remain static as they are standard options
  // NOTE: Use FilterDataService for dynamic loading instead of these fallbacks
  static const List<FilterOption> genders = [
    FilterOption(value: CommonFilterValues.all, label: FilterLabels.allGenders),
    FilterOption(value: GenderFilterValues.male, label: FilterLabels.male),
    FilterOption(value: GenderFilterValues.female, label: FilterLabels.female),
  ];

  static const List<FilterOption> cattleAgeGroups = [
    FilterOption(value: AgeGroupFilterValues.all, label: FilterLabels.allAges),
    FilterOption(value: AgeGroupFilterValues.zeroToSixMonths, label: FilterLabels.zeroToSixMonths),
    FilterOption(value: AgeGroupFilterValues.sixMonthsToTwoYears, label: FilterLabels.sixMonthsToTwoYears),
    FilterOption(value: AgeGroupFilterValues.twoToEightYears, label: FilterLabels.twoToEightYears),
    FilterOption(value: AgeGroupFilterValues.eightPlusYears, label: FilterLabels.eightPlusYears),
  ];

  static const List<FilterOption> cattleStatus = [
    FilterOption(value: StatusFilterValues.all, label: FilterLabels.allStatus),
    FilterOption(value: StatusFilterValues.active, label: FilterLabels.active),
    FilterOption(value: StatusFilterValues.sold, label: FilterLabels.sold),
    FilterOption(value: StatusFilterValues.deceased, label: FilterLabels.deceased),
    FilterOption(value: StatusFilterValues.transferred, label: FilterLabels.transferred),
  ];
}

/// Universal filter handlers for applying filters to data with comprehensive null safety
///
/// This class provides utility methods for filtering and sorting operations
/// that are commonly used across the application. All comparators handle
/// null values gracefully by sorting them to the end of the list.
///
/// Example usage:
/// ```dart
/// final sortComparators = {
///   'name': FilterHandlers.stringComparator<CattleIsar>((cattle) => cattle.name),
///   'dateOfBirth': FilterHandlers.dateComparator<CattleIsar>((cattle) => cattle.dateOfBirth),
///   'weight': FilterHandlers.numericComparator<CattleIsar>((cattle) => cattle.weight),
///   'isActive': FilterHandlers.booleanComparator<CattleIsar>((cattle) => cattle.isActive),
/// };
/// ```
class FilterHandlers {
  /// Apply search filter to a list of items
  static List<T> applySearch<T>({
    required List<T> items,
    required String searchQuery,
    required List<String Function(T)> searchFields,
  }) {
    if (searchQuery.isEmpty || searchFields.isEmpty) {
      return items;
    }

    final searchLower = searchQuery.toLowerCase();
    return items.where((item) {
      return searchFields.any((extractor) {
        final value = extractor(item);
        return value.toLowerCase().contains(searchLower);
      });
    }).toList();
  }

  /// Apply date range filter to a list of items
  static List<T> applyDateFilter<T>({
    required List<T> items,
    required DateTime? startDate,
    required DateTime? endDate,
    required DateTime? Function(T) dateExtractor,
  }) {
    if (startDate == null || endDate == null) {
      return items;
    }

    return items.where((item) {
      final itemDate = dateExtractor(item);
      if (itemDate == null) return false;

      final normalizedStart = DateTime(startDate.year, startDate.month, startDate.day);
      final normalizedEnd = DateTime(endDate.year, endDate.month, endDate.day, 23, 59, 59);

      return itemDate.isAfter(normalizedStart.subtract(const Duration(milliseconds: 1))) &&
             itemDate.isBefore(normalizedEnd.add(const Duration(milliseconds: 1)));
    }).toList();
  }

  /// Apply sorting to a list of items
  static List<T> applySort<T>({
    required List<T> items,
    required String? sortField,
    required bool isAscending,
    required Map<String, int Function(T, T)> sortComparators,
  }) {
    if (sortField == null || !sortComparators.containsKey(sortField)) {
      return items;
    }

    final comparator = sortComparators[sortField]!;
    final sortedItems = List<T>.from(items);

    sortedItems.sort((a, b) {
      final result = comparator(a, b);
      return isAscending ? result : -result;
    });

    return sortedItems;
  }

  /// Apply global filters to a list of items
  /// Optimized to chain where clauses and call toList() only once
  static List<T> applyGlobalFilters<T>({
    required List<T> items,
    required Map<String, dynamic> activeFilters,
    required Map<String, bool Function(T, dynamic)> filterPredicates,
  }) {
    // Start with the original iterable
    Iterable<T> result = items;

    // Chain all where clauses without converting to list
    for (final entry in activeFilters.entries) {
      final filterKey = entry.key;
      final filterValue = entry.value;

      if (filterValue == null ||
          filterValue.toString().isEmpty ||
          filterValue == 'All' ||
          !filterPredicates.containsKey(filterKey)) {
        continue;
      }

      final predicate = filterPredicates[filterKey]!;
      result = result.where((item) => predicate(item, filterValue));
    }

    // Convert to list only once at the end
    return result.toList();
  }

  /// Create a string comparator with full null safety
  static int Function(T, T) stringComparator<T>(String? Function(T) extractor) {
    return (a, b) {
      final aValue = extractor(a);
      final bValue = extractor(b);

      // Handle null values: nulls sort to the end
      if (aValue == null && bValue == null) return 0;
      if (aValue == null) return 1;
      if (bValue == null) return -1;

      return aValue.compareTo(bValue);
    };
  }

  /// Create a DateTime comparator with full null safety
  static int Function(T, T) dateComparator<T>(DateTime? Function(T) extractor) {
    return (a, b) {
      final aValue = extractor(a);
      final bValue = extractor(b);

      // Handle null values: nulls sort to the end
      if (aValue == null && bValue == null) return 0;
      if (aValue == null) return 1;
      if (bValue == null) return -1;

      return aValue.compareTo(bValue);
    };
  }

  /// Create a numeric comparator with full null safety
  static int Function(T, T) numericComparator<T>(num? Function(T) extractor) {
    return (a, b) {
      final aValue = extractor(a);
      final bValue = extractor(b);

      // Handle null values: nulls sort to the end
      if (aValue == null && bValue == null) return 0;
      if (aValue == null) return 1;
      if (bValue == null) return -1;

      return aValue.compareTo(bValue);
    };
  }

  /// Create a boolean comparator with full null safety
  /// false < true, with nulls sorting to the end
  static int Function(T, T) booleanComparator<T>(bool? Function(T) extractor) {
    return (a, b) {
      final aValue = extractor(a);
      final bValue = extractor(b);

      // Handle null values: nulls sort to the end
      if (aValue == null && bValue == null) return 0;
      if (aValue == null) return 1;
      if (bValue == null) return -1;

      // false (0) < true (1)
      return aValue == bValue ? 0 : (aValue ? 1 : -1);
    };
  }

  /// Get predefined date ranges
  static List<DateRangeOption> getPredefinedDateRanges() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    return [
      DateRangeOption(
        label: 'Today',
        icon: Icons.today,
        startDate: today,
        endDate: today,
        description: 'Records from today',
      ),
      DateRangeOption(
        label: 'Last 7 Days',
        icon: Icons.date_range,
        startDate: today.subtract(const Duration(days: 6)),
        endDate: today,
        description: 'Records from the past week',
      ),
      DateRangeOption(
        label: 'Last 30 Days',
        icon: Icons.calendar_month,
        startDate: today.subtract(const Duration(days: 29)),
        endDate: today,
        description: 'Records from the past month',
      ),
      DateRangeOption(
        label: 'This Month',
        icon: Icons.calendar_today,
        startDate: DateTime(now.year, now.month, 1),
        endDate: today,
        description: 'Records from this month',
      ),
    ];
  }

  /// Format date range for display
  static String formatDateRange(DateTime? startDate, DateTime? endDate) {
    if (startDate == null || endDate == null) {
      return 'No date filter';
    }

    final formatter = DateFormat('MMM d, yyyy');

    if (startDate.year == endDate.year &&
        startDate.month == endDate.month &&
        startDate.day == endDate.day) {
      return formatter.format(startDate);
    }

    return '${formatter.format(startDate)} - ${formatter.format(endDate)}';
  }
}
