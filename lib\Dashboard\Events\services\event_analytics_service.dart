import '../models/event_isar.dart';
import '../models/event_type_isar.dart';
import '../../Cattle/models/cattle_isar.dart';

/// Data class to hold all event analytics results
class EventAnalyticsResult {
  // KPI Metrics
  final int totalEvents;
  final int completedEvents;
  final int pendingEvents;
  final int overdueEvents;
  final int missedEvents;
  final double completionRate;
  final double overdueRate;

  // Event type distributions
  final Map<String, int> eventTypeDistribution;
  final Map<String, int> priorityDistribution;
  final Map<String, int> statusDistribution;

  // Time-based metrics
  final Map<String, int> monthlyEventCounts;
  final Map<String, int> weeklyEventCounts;
  final int eventsThisWeek;
  final int eventsThisMonth;
  final int upcomingEvents; // Next 7 days

  // Performance insights
  final String mostCommonEventType;
  final int mostCommonEventTypeCount;
  final String mostActiveCattle;
  final int mostActiveCattleEventCount;
  final double averageEventsPerCattle;

  // Scheduling insights
  final int eventsScheduledToday;
  final int eventsScheduledTomorrow;
  final int highPriorityPendingEvents;
  final List<String> criticalUpcomingEvents;

  // Trends
  final double eventFrequencyTrend; // Events per month trend
  final double completionTrend; // Completion rate trend
  final int daysWithEvents;

  const EventAnalyticsResult({
    required this.totalEvents,
    required this.completedEvents,
    required this.pendingEvents,
    required this.overdueEvents,
    required this.missedEvents,
    required this.completionRate,
    required this.overdueRate,
    required this.eventTypeDistribution,
    required this.priorityDistribution,
    required this.statusDistribution,
    required this.monthlyEventCounts,
    required this.weeklyEventCounts,
    required this.eventsThisWeek,
    required this.eventsThisMonth,
    required this.upcomingEvents,
    required this.mostCommonEventType,
    required this.mostCommonEventTypeCount,
    required this.mostActiveCattle,
    required this.mostActiveCattleEventCount,
    required this.averageEventsPerCattle,
    required this.eventsScheduledToday,
    required this.eventsScheduledTomorrow,
    required this.highPriorityPendingEvents,
    required this.criticalUpcomingEvents,
    required this.eventFrequencyTrend,
    required this.completionTrend,
    required this.daysWithEvents,
  });

  /// Empty result for when there's no data
  static const empty = EventAnalyticsResult(
    totalEvents: 0,
    completedEvents: 0,
    pendingEvents: 0,
    overdueEvents: 0,
    missedEvents: 0,
    completionRate: 0.0,
    overdueRate: 0.0,
    eventTypeDistribution: {},
    priorityDistribution: {},
    statusDistribution: {},
    monthlyEventCounts: {},
    weeklyEventCounts: {},
    eventsThisWeek: 0,
    eventsThisMonth: 0,
    upcomingEvents: 0,
    mostCommonEventType: '',
    mostCommonEventTypeCount: 0,
    mostActiveCattle: '',
    mostActiveCattleEventCount: 0,
    averageEventsPerCattle: 0.0,
    eventsScheduledToday: 0,
    eventsScheduledTomorrow: 0,
    highPriorityPendingEvents: 0,
    criticalUpcomingEvents: [],
    eventFrequencyTrend: 0.0,
    completionTrend: 0.0,
    daysWithEvents: 0,
  );
}

/// Pure analytics service for event calculations - no state, just calculations
/// Following the cattle module pattern with single-pass data processing
class EventAnalyticsService {

  /// Main calculation method - single entry point with O(n) efficiency
  static EventAnalyticsResult calculate(
    List<EventIsar> events,
    List<EventTypeIsar> eventTypes,
    List<CattleIsar> cattle,
  ) {
    if (events.isEmpty) {
      return EventAnalyticsResult.empty;
    }

    // Single pass through the data for maximum efficiency
    final accumulator = _EventAnalyticsAccumulator();

    // Process all events
    for (final event in events) {
      accumulator.processEvent(event);
    }

    // Calculate derived metrics
    accumulator.calculateDerivedMetrics(cattle);

    return accumulator.toResult();
  }

  /// Calculate event completion rate for a specific cattle
  static double calculateCattleEventCompletionRate(
    String cattleId,
    List<EventIsar> events,
  ) {
    final cattleEvents = events.where((e) => e.cattleId == cattleId).toList();
    if (cattleEvents.isEmpty) return 100.0; // No events = perfect completion

    final completedEvents = cattleEvents.where((e) => e.isCompleted).length;
    return (completedEvents / cattleEvents.length * 100).clamp(0.0, 100.0);
  }

  /// Get upcoming critical events (high priority, due soon)
  static List<EventIsar> getCriticalUpcomingEvents(
    List<EventIsar> events,
    {int daysAhead = 7}
  ) {
    final now = DateTime.now();
    final threshold = now.add(Duration(days: daysAhead));

    return events.where((event) {
      if (event.isCompleted || event.isMissed) return false;
      
      final eventDate = event.eventDate ?? event.dueDate;
      if (eventDate == null) return false;

      return eventDate.isAfter(now) && 
             eventDate.isBefore(threshold) && 
             event.priority == EventPriority.high;
    }).toList()
      ..sort((a, b) {
        final dateA = a.eventDate ?? a.dueDate ?? DateTime.now();
        final dateB = b.eventDate ?? b.dueDate ?? DateTime.now();
        return dateA.compareTo(dateB);
      });
  }

  /// Calculate event scheduling efficiency
  static double calculateSchedulingEfficiency(List<EventIsar> events) {
    if (events.isEmpty) return 100.0;

    final scheduledEvents = events.where((e) => e.eventDate != null || e.dueDate != null).length;
    return (scheduledEvents / events.length * 100).clamp(0.0, 100.0);
  }

  /// Identify event patterns for a specific cattle
  static Map<String, dynamic> analyzeCattleEventPatterns(
    String cattleId,
    List<EventIsar> events,
  ) {
    final cattleEvents = events.where((e) => e.cattleId == cattleId).toList();
    
    if (cattleEvents.isEmpty) {
      return {
        'totalEvents': 0,
        'completionRate': 100.0,
        'mostCommonType': '',
        'averageEventsPerMonth': 0.0,
        'lastEventDate': null,
      };
    }

    // Sort by date
    cattleEvents.sort((a, b) {
      final dateA = a.eventDate ?? a.createdAt ?? DateTime.now();
      final dateB = b.eventDate ?? b.createdAt ?? DateTime.now();
      return dateA.compareTo(dateB);
    });

    // Reuse the existing helper method instead of duplicating logic
    final completionRate = calculateCattleEventCompletionRate(cattleId, events);

    // Find most common event type
    final typeCount = <String, int>{};
    for (final event in cattleEvents) {
      final type = event.type?.value ?? 'Unknown';
      typeCount[type] = (typeCount[type] ?? 0) + 1;
    }

    String mostCommonType = '';
    int maxCount = 0;
    for (final entry in typeCount.entries) {
      if (entry.value > maxCount) {
        mostCommonType = entry.key;
        maxCount = entry.value;
      }
    }

    // Calculate average events per month
    final firstEvent = cattleEvents.first;
    final lastEvent = cattleEvents.last;
    final firstDate = firstEvent.eventDate ?? firstEvent.createdAt ?? DateTime.now();
    final lastDate = lastEvent.eventDate ?? lastEvent.createdAt ?? DateTime.now();
    
    final monthsDifference = ((lastDate.difference(firstDate).inDays) / 30.0).clamp(1.0, double.infinity);
    final averageEventsPerMonth = cattleEvents.length / monthsDifference;

    return {
      'totalEvents': cattleEvents.length,
      'completionRate': completionRate,
      'mostCommonType': mostCommonType,
      'averageEventsPerMonth': averageEventsPerMonth,
      'lastEventDate': lastDate,
    };
  }
}

/// Efficient single-pass accumulator for all event analytics calculations
class _EventAnalyticsAccumulator {
  // Basic counts
  int totalEvents = 0;
  int completedEvents = 0;
  int pendingEvents = 0;
  int overdueEvents = 0;
  int missedEvents = 0;

  // Distributions
  final Map<String, int> eventTypeDistribution = {};
  final Map<String, int> priorityDistribution = {};
  final Map<String, int> statusDistribution = {};

  // Time tracking
  final Map<String, int> monthlyEventCounts = {};
  final Map<String, int> weeklyEventCounts = {};
  final Set<DateTime> eventDates = {};

  // Cattle tracking
  final Map<String, int> cattleEventCounts = {};

  // Scheduling tracking
  int eventsScheduledToday = 0;
  int eventsScheduledTomorrow = 0;
  int highPriorityPendingEvents = 0;
  int upcomingEvents = 0;
  final List<String> criticalUpcomingEvents = [];

  /// Process a single event
  void processEvent(EventIsar event) {
    totalEvents++;

    // Status processing
    if (event.isCompleted) {
      completedEvents++;
      statusDistribution['Completed'] = (statusDistribution['Completed'] ?? 0) + 1;
    } else if (event.isMissed) {
      missedEvents++;
      statusDistribution['Missed'] = (statusDistribution['Missed'] ?? 0) + 1;
    } else {
      pendingEvents++;
      statusDistribution['Pending'] = (statusDistribution['Pending'] ?? 0) + 1;

      // Check if overdue
      final eventDate = event.eventDate ?? event.dueDate;
      if (eventDate != null && eventDate.isBefore(DateTime.now())) {
        overdueEvents++;
        statusDistribution['Overdue'] = (statusDistribution['Overdue'] ?? 0) + 1;
      }

      // Check for high priority pending events
      if (event.priority == EventPriority.high) {
        highPriorityPendingEvents++;
      }
    }

    // Event type processing
    final eventType = event.type?.value ?? 'Unknown';
    eventTypeDistribution[eventType] = (eventTypeDistribution[eventType] ?? 0) + 1;

    // Priority processing
    final priority = event.priority.name;
    priorityDistribution[priority] = (priorityDistribution[priority] ?? 0) + 1;

    // Time-based processing
    final eventDate = event.eventDate ?? event.createdAt;
    if (eventDate != null) {
      // Monthly tracking
      final monthKey = '${eventDate.year}-${eventDate.month.toString().padLeft(2, '0')}';
      monthlyEventCounts[monthKey] = (monthlyEventCounts[monthKey] ?? 0) + 1;

      // Weekly tracking (ISO week)
      final weekKey = _getISOWeekKey(eventDate);
      weeklyEventCounts[weekKey] = (weeklyEventCounts[weekKey] ?? 0) + 1;

      // Daily tracking
      final dateOnly = DateTime(eventDate.year, eventDate.month, eventDate.day);
      eventDates.add(dateOnly);

      // Today/tomorrow tracking
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final tomorrow = today.add(const Duration(days: 1));

      if (dateOnly.isAtSameMomentAs(today)) {
        eventsScheduledToday++;
      } else if (dateOnly.isAtSameMomentAs(tomorrow)) {
        eventsScheduledTomorrow++;
      }

      // Upcoming events (next 7 days)
      final nextWeek = today.add(const Duration(days: 7));
      if (dateOnly.isAfter(today) && dateOnly.isBefore(nextWeek)) {
        upcomingEvents++;

        // Critical upcoming events
        if (event.priority == EventPriority.high && !event.isCompleted) {
          criticalUpcomingEvents.add(event.title ?? 'Untitled Event');
        }
      }
    }

    // Cattle tracking
    if (event.cattleId != null) {
      cattleEventCounts[event.cattleId!] = (cattleEventCounts[event.cattleId!] ?? 0) + 1;
    }
  }

  /// Calculate derived metrics
  void calculateDerivedMetrics(List<CattleIsar> cattle) {
    // Additional calculations can be added here if needed
  }

  /// Convert accumulated data to immutable result
  EventAnalyticsResult toResult() {
    final completionRate = totalEvents > 0 
        ? (completedEvents / totalEvents * 100).clamp(0.0, 100.0)
        : 0.0;

    final overdueRate = totalEvents > 0 
        ? (overdueEvents / totalEvents * 100).clamp(0.0, 100.0)
        : 0.0;

    // Find most common event type
    String mostCommonEventType = '';
    int mostCommonEventTypeCount = 0;
    for (final entry in eventTypeDistribution.entries) {
      if (entry.value > mostCommonEventTypeCount) {
        mostCommonEventType = entry.key;
        mostCommonEventTypeCount = entry.value;
      }
    }

    // Find most active cattle
    String mostActiveCattle = '';
    int mostActiveCattleEventCount = 0;
    for (final entry in cattleEventCounts.entries) {
      if (entry.value > mostActiveCattleEventCount) {
        mostActiveCattle = entry.key;
        mostActiveCattleEventCount = entry.value;
      }
    }

    final averageEventsPerCattle = cattleEventCounts.isNotEmpty 
        ? totalEvents / cattleEventCounts.length 
        : 0.0;

    // Calculate events this week/month
    final now = DateTime.now();
    final thisWeekKey = _getISOWeekKey(now);
    final thisMonthKey = '${now.year}-${now.month.toString().padLeft(2, '0')}';

    final eventsThisWeek = weeklyEventCounts[thisWeekKey] ?? 0;
    final eventsThisMonth = monthlyEventCounts[thisMonthKey] ?? 0;

    return EventAnalyticsResult(
      totalEvents: totalEvents,
      completedEvents: completedEvents,
      pendingEvents: pendingEvents,
      overdueEvents: overdueEvents,
      missedEvents: missedEvents,
      completionRate: completionRate,
      overdueRate: overdueRate,
      eventTypeDistribution: Map.unmodifiable(eventTypeDistribution),
      priorityDistribution: Map.unmodifiable(priorityDistribution),
      statusDistribution: Map.unmodifiable(statusDistribution),
      monthlyEventCounts: Map.unmodifiable(monthlyEventCounts),
      weeklyEventCounts: Map.unmodifiable(weeklyEventCounts),
      eventsThisWeek: eventsThisWeek,
      eventsThisMonth: eventsThisMonth,
      upcomingEvents: upcomingEvents,
      mostCommonEventType: mostCommonEventType,
      mostCommonEventTypeCount: mostCommonEventTypeCount,
      mostActiveCattle: mostActiveCattle,
      mostActiveCattleEventCount: mostActiveCattleEventCount,
      averageEventsPerCattle: averageEventsPerCattle,
      eventsScheduledToday: eventsScheduledToday,
      eventsScheduledTomorrow: eventsScheduledTomorrow,
      highPriorityPendingEvents: highPriorityPendingEvents,
      criticalUpcomingEvents: List.unmodifiable(criticalUpcomingEvents),
      eventFrequencyTrend: 0.0, // Would need time-series analysis
      completionTrend: 0.0, // Would need time-series analysis
      daysWithEvents: eventDates.length,
    );
  }

  /// Get ISO week key for grouping
  String _getISOWeekKey(DateTime date) {
    // Simplified ISO week calculation
    final dayOfYear = date.difference(DateTime(date.year, 1, 1)).inDays + 1;
    final week = ((dayOfYear - date.weekday + 10) / 7).floor();
    return '${date.year}-W${week.toString().padLeft(2, '0')}';
  }
}
