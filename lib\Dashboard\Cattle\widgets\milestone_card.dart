import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

/// A card that displays pregnancy milestones
class MilestoneCard extends StatelessWidget {
  final List<Map<String, dynamic>> milestones;
  final String title;
  final String emptyMessage;
  final Color baseColor;
  final Widget? trailing;
  final VoidCallback? onInfoTap;

  const MilestoneCard({
    super.key,
    required this.milestones,
    this.title = 'Pregnancy Milestones',
    this.emptyMessage = 'No milestones available',
    this.baseColor = const Color(0xFF2E7D32), // Green
    this.trailing,
    this.onInfoTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Card header with subtle background
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: baseColor.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(Icons.timeline, color: baseColor, size: 24),
                    const SizedBox(width: 8),
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: baseColor,
                      ),
                    ),
                  ],
                ),
                if (trailing != null)
                  trailing!
                else if (onInfoTap != null)
                  IconButton(
                    icon: Icon(Icons.info_outline, color: baseColor),
                    onPressed: onInfoTap,
                    tooltip: 'More information',
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
              ],
            ),
          ),

          // Milestone content
          if (milestones.isEmpty)
            Padding(
              padding: const EdgeInsets.all(16),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.timeline_outlined,
                      size: 48,
                      color: baseColor.withValues(alpha: 0.4),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      emptyMessage,
                      style: TextStyle(
                        fontSize: 14,
                        color: baseColor.withValues(alpha: 0.7),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            )
          else
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // Timeline
                  for (int i = 0; i < milestones.length; i++)
                    _buildMilestoneItem(
                      context,
                      milestones[i],
                      i == milestones.length - 1
                    ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  // Define a list of distinct colors for milestones
  static const List<Color> milestoneColors = [
    Color(0xFF1976D2), // Blue
    Color(0xFF7B1FA2), // Purple
    Color(0xFF388E3C), // Green
    Color(0xFFD32F2F), // Red
    Color(0xFF0097A7), // Cyan
  ];

  Widget _buildMilestoneItem(
    BuildContext context,
    Map<String, dynamic> milestone,
    bool isLast
  ) {
    final title = milestone['title'] as String;
    final description = milestone['description'] as String;
    final date = milestone['date'] as DateTime;
    final icon = milestone['icon'] as IconData;
    final color = milestone['color'] as Color;
    // Note: isPassed variable is defined but not used in current implementation

    final now = DateTime.now();
    final daysUntil = date.difference(now).inDays;
    final isToday = daysUntil == 0;
    final isFuture = daysUntil > 0;

    // Format time text
    String timeText;
    if (isToday) {
      timeText = 'Today';
    } else if (isFuture) {
      timeText = 'In $daysUntil days';
    } else {
      timeText = '${daysUntil.abs()} days ago';
    }

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Timeline line and dot
        SizedBox(
          width: 32,
          child: Column(
            children: [
              // Colored milestone dot
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                  border: Border.all(color: color, width: 2),
                  boxShadow: [
                    BoxShadow(
                      color: color.withValues(alpha: 0.2),
                      blurRadius: 3,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 18,
                ),
              ),
              // Timeline connecting line
              if (!isLast)
                Container(
                  width: 2,
                  height: 40,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        color,
                        color.withValues(alpha: 0.3),
                      ],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                    ),
                  ),
                  margin: const EdgeInsets.symmetric(vertical: 4),
                ),
            ],
          ),
        ),
        const SizedBox(width: 16),
        // Milestone content
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Title and date row
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      title,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: color,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(width: 8),
                  // Date pill
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.15),
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
                    ),
                    child: Text(
                      DateFormat('MMM dd, yyyy').format(date),
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: color,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              // Description
              Text(
                description,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 4),
              // Time indicator
              Text(
                timeText,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              if (!isLast) const SizedBox(height: 16),
            ],
          ),
        ),
      ],
    );
  }


}
