import 'package:isar/isar.dart';
import 'package:uuid/uuid.dart';

part 'breeding_event_isar.g.dart';

/// Represents a breeding calendar event in the Isar database
@collection
class BreedingEventIsar {
  /// Isar database ID
  Id id = Isar.autoIncrement;

  /// Business ID (UUID) for the event - indexed for quick lookup
  @Index(unique: true)
  String? businessId;

  /// ID of the cattle involved in the event
  @Index(type: IndexType.value)
  String? cattleId;

  /// Name of the cattle (stored for quick access)
  String? cattleName;

  /// Type of event (Heat, Breeding, Expected Heat, Expected Calving)
  @Index()
  String? eventType;

  /// Date of the event
  DateTime? date;

  /// Status of the event (if applicable)
  String? status;

  /// Additional details about the event
  String? details;

  /// Related record ID (breeding or pregnancy record)
  @Index()
  String? relatedRecordId;

  /// Creation timestamp
  DateTime? createdAt;

  /// Last update timestamp
  DateTime? updatedAt;

  /// Default constructor for Isar
  BreedingEventIsar();

  /// Factory constructor for creating a new breeding event
  factory BreedingEventIsar.create({
    required String cattleId,
    required String cattleName,
    required String eventType,
    required DateTime date,
    String? status = '',
    String? details = '',
    String? relatedRecordId,
    String? businessId,
  }) {
    return BreedingEventIsar()
      ..businessId = businessId ?? const Uuid().v4()
      ..cattleId = cattleId
      ..cattleName = cattleName
      ..eventType = eventType
      ..date = date
      ..status = status
      ..details = details
      ..relatedRecordId = relatedRecordId
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();
  }

  /// Convert to a map for serialization
  Map<String, dynamic> toMap() {
    return {
      'id': businessId,
      'cattleId': cattleId,
      'cattleName': cattleName,
      'eventType': eventType,
      'date': date?.toIso8601String(),
      'status': status,
      'details': details,
      'relatedRecordId': relatedRecordId,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  /// Alias for toMap
  Map<String, dynamic> toJson() => toMap();

  /// Create from a map
  factory BreedingEventIsar.fromMap(Map<String, dynamic> map) {
    return BreedingEventIsar()
      ..businessId = map['id'] as String?
      ..cattleId = map['cattleId'] as String?
      ..cattleName = map['cattleName'] as String?
      ..eventType = map['eventType'] as String?
      ..date = map['date'] != null
          ? map['date'] is String
              ? DateTime.parse(map['date'])
              : map['date'] as DateTime
          : null
      ..status = map['status'] as String?
      ..details = map['details'] as String?
      ..relatedRecordId = map['relatedRecordId'] as String?
      ..createdAt = map['createdAt'] != null
          ? map['createdAt'] is String
              ? DateTime.parse(map['createdAt'])
              : map['createdAt'] as DateTime
          : DateTime.now()
      ..updatedAt = map['updatedAt'] != null
          ? map['updatedAt'] is String
              ? DateTime.parse(map['updatedAt'])
              : map['updatedAt'] as DateTime
          : DateTime.now();
  }

  /// Create from JSON
  factory BreedingEventIsar.fromJson(Map<String, dynamic> json) =>
      BreedingEventIsar.fromMap(json);

  /// Create a copy with updated fields
  BreedingEventIsar copyWith({
    String? businessId,
    String? cattleId,
    String? cattleName,
    String? eventType,
    DateTime? date,
    String? status,
    String? details,
    String? relatedRecordId,
  }) {
    return BreedingEventIsar()
      ..id = id
      ..businessId = businessId ?? this.businessId
      ..cattleId = cattleId ?? this.cattleId
      ..cattleName = cattleName ?? this.cattleName
      ..eventType = eventType ?? this.eventType
      ..date = date ?? this.date
      ..status = status ?? this.status
      ..details = details ?? this.details
      ..relatedRecordId = relatedRecordId ?? this.relatedRecordId
      ..createdAt = createdAt
      ..updatedAt = DateTime.now();
  }

  @override
  String toString() => '$eventType - $cattleName ($cattleId)';
} 