import 'package:isar/isar.dart';
import 'package:uuid/uuid.dart';

part 'backup_settings_isar.g.dart';

@collection
class BackupSettingsIsar {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  String? businessId;

  @Index()
  String? farmBusinessId; // Reference to the farm these settings belong to

  bool autoBackupEnabled = true;
  int autoBackupFrequency = 7; // Days
  String backupLocation = 'local';
  int? backupFrequencyDays; // Changed from String? to int? to match generated code
  
  DateTime? lastBackupDate;

  DateTime? createdAt;
  DateTime? updatedAt;

  BackupSettingsIsar() {
    createdAt = DateTime.now();
    updatedAt = DateTime.now();
    backupFrequencyDays = autoBackupFrequency; // Update this to use the int directly instead of string conversion
  }

  factory BackupSettingsIsar.create({
    required String farmBusinessId,
    bool autoBackupEnabled = true,
    int autoBackupFrequency = 7,
    String backupLocation = 'local',
    DateTime? lastBackupDate,
  }) {
    return BackupSettingsIsar()
      ..businessId = const Uuid().v4()
      ..farmBusinessId = farmBusinessId
      ..autoBackupEnabled = autoBackupEnabled
      ..autoBackupFrequency = autoBackupFrequency
      ..backupFrequencyDays = autoBackupFrequency // Update this to use the int directly
      ..backupLocation = backupLocation
      ..lastBackupDate = lastBackupDate
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'farmBusinessId': farmBusinessId,
      'autoBackupEnabled': autoBackupEnabled,
      'autoBackupFrequency': autoBackupFrequency,
      'backupFrequencyDays': backupFrequencyDays,
      'backupLocation': backupLocation,
      'lastBackupDate': lastBackupDate?.toIso8601String(),
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  factory BackupSettingsIsar.fromMap(Map<String, dynamic> map) {
    return BackupSettingsIsar()
      ..id = map['id'] ?? Isar.autoIncrement
      ..farmBusinessId = map['farmBusinessId'] as String?
      ..autoBackupEnabled = map['autoBackupEnabled'] ?? true
      ..autoBackupFrequency = map['autoBackupFrequency'] ?? 7
      ..backupFrequencyDays = map['backupFrequencyDays'] as int? ?? 7 // Updated to int instead of string
      ..backupLocation = map['backupLocation'] ?? 'local'
      ..lastBackupDate = map['lastBackupDate'] != null ? DateTime.parse(map['lastBackupDate'] as String) : null
      ..createdAt = map['createdAt'] != null ? DateTime.parse(map['createdAt'] as String) : DateTime.now()
      ..updatedAt = map['updatedAt'] != null ? DateTime.parse(map['updatedAt'] as String) : DateTime.now();
  }

  BackupSettingsIsar copyWith({
    String? businessId,
    String? farmBusinessId,
    bool? autoBackupEnabled,
    int? autoBackupFrequency,
    int? backupFrequencyDays, // Changed from String? to int?
    String? backupLocation,
    DateTime? lastBackupDate,
  }) {
    final settings = BackupSettingsIsar()
      ..id = id
      ..businessId = businessId ?? this.businessId
      ..farmBusinessId = farmBusinessId ?? this.farmBusinessId
      ..autoBackupEnabled = autoBackupEnabled ?? this.autoBackupEnabled
      ..autoBackupFrequency = autoBackupFrequency ?? this.autoBackupFrequency
      ..backupFrequencyDays = backupFrequencyDays ?? this.backupFrequencyDays
      ..backupLocation = backupLocation ?? this.backupLocation
      ..lastBackupDate = lastBackupDate ?? this.lastBackupDate
      ..createdAt = createdAt
      ..updatedAt = DateTime.now();
    
    // Ensure backupFrequencyDays is synchronized with autoBackupFrequency
    if (autoBackupFrequency != null && backupFrequencyDays == null) {
      settings.backupFrequencyDays = autoBackupFrequency; // Use the int directly
    }
    
    return settings;
  }

  factory BackupSettingsIsar.createDefault(String farmId) {
    return BackupSettingsIsar()
      ..businessId = farmId
      ..autoBackupEnabled = true
      ..autoBackupFrequency = 7
      ..backupFrequencyDays = 7 // Updated to int instead of string
      ..backupLocation = 'local'
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();
  }

  BackupSettingsIsar clone() {
    return BackupSettingsIsar()
      ..id = id
      ..businessId = businessId
      ..farmBusinessId = farmBusinessId
      ..autoBackupEnabled = autoBackupEnabled
      ..autoBackupFrequency = autoBackupFrequency
      ..backupFrequencyDays = backupFrequencyDays
      ..backupLocation = backupLocation
      ..lastBackupDate = lastBackupDate
      ..createdAt = createdAt
      ..updatedAt = updatedAt;
  }
} 