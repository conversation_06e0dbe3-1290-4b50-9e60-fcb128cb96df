import 'package:flutter/material.dart';

class SetupMenuItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final VoidCallback onTap;
  final double size;
  final Color? color;

  const SetupMenuItem({
    Key? key,
    required this.icon,
    required this.title,
    required this.onTap,
    this.size = 48,
    this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(15),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(15),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: size,
                color: color ?? _getIconColor(title),
              ),
              const SizedBox(height: 8),
              Flexible(
                child: Text(
                  title,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getIconColor(String title) {
    switch (title.toLowerCase().split('\n')[0]) {
      case 'income':
        return Colors.green;
      case 'expense':
        return Colors.red;
      case 'cattle':
        return Colors.blue;
      case 'animal':
        return Colors.brown;
      case 'gestation':
        return Colors.purple;
      case 'currency':
        return Colors.amber;
      case 'farm':
        return Colors.teal;
      case 'milk':
        return Colors.lightBlue;
      case 'users':
        return Colors.indigo;
      case 'data':
        return Colors.deepOrange;
      case 'alert':
        return Colors.orange;
      case 'event':
        return Colors.pink;
      default:
        return const Color(0xFF2E7D32);
    }
  }
}
