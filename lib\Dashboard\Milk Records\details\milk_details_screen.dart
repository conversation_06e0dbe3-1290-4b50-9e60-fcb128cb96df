/*
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../models/milk_record_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../services/milk_handler.dart';
import '../../Cattle/services/cattle_handler.dart';
import '../../../utils/message_utils.dart';
import '../../../constants/app_tabs.dart';
import '../../widgets/index.dart';
import 'milk_details_analytics_tab.dart';
import 'milk_details_records_tab.dart';

class MilkDetailScreen extends StatefulWidget {
  final MilkRecordIsar milkRecord;
  final String title;
  final VoidCallback? onRefresh;

  const MilkDetailScreen({
    Key? key,
    required this.milkRecord,
    required this.title,
    this.onRefresh,
  }) : super(key: key);

  @override
  State<MilkDetailScreen> createState() => _MilkDetailScreenState();
}

class _MilkDetailScreenState extends State<MilkDetailScreen>
    with SingleTickerProviderStateMixin, UniversalScreenState, UniversalDataRefresh {
  late TabController _tabController;
  final MilkRepository _milkRepository = GetIt.instance<MilkRepository>();
  final CattleHandler _cattleHandler = GetIt.instance<CattleHandler>();

  MilkRecordIsar? _milkRecord;
  CattleIsar? _cattle;
  List<MilkRecordIsar> _relatedRecords = [];
  bool _isLoading = true;

  // Tab configuration
  late List<TabItem> _tabs;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(() {
      setState(() {}); // Rebuild to update colors
    });

    _milkRecord = widget.milkRecord;
    _loadData();

    // Initialize tabs
    _tabs = [
      TabItem(
        icon: Icons.analytics,
        label: 'Analytics',
        color: UniversalEmptyStateTheme.milk,
        screen: Container(),
      ),
      TabItem(
        icon: Icons.list_alt,
        label: 'Records',
        color: UniversalEmptyStateTheme.milk,
        screen: Container(),
      ),
    ];
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      setState(() => _isLoading = true);

      // Load cattle information
      if (_milkRecord?.cattleBusinessId != null) {
        final cattle = await _cattleHandler.getCattleById(_milkRecord!.cattleBusinessId!);
        
        // Load related milk records for the same cattle
        final relatedRecords = await _milkHandler.getMilkRecordsForCattle(
          _milkRecord!.cattleBusinessId!,
        );

        if (mounted) {
          setState(() {
            _cattle = cattle;
            _relatedRecords = relatedRecords;
            _isLoading = false;
          });
        }
      } else {
        if (mounted) {
          setState(() => _isLoading = false);
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        MilkMessageUtils.showError(context, 'Failed to load milk details: $e');
      }
    }
  }

  Future<void> _refreshData() async {
    await performRefresh(() => _loadData());
    widget.onRefresh?.call();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        backgroundColor: UniversalEmptyStateTheme.milk,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshData,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: _isLoading
          ? UniversalLoadingIndicator.milk()
          : _milkRecord == null
              ? UniversalEmptyState.milk(
                  title: 'Milk Record Not Found',
                  message: 'The requested milk record could not be loaded.',
                )
              : Column(
                  children: [
                    // Header with basic info
                    _buildHeaderCard(),
                    
                    // Tab Bar
                    ReusableTabBar.controlled(
                      controller: _tabController,
                      tabs: _tabs,
                      useMulticolor: false,
                      indicatorColor: UniversalEmptyStateTheme.milk,
                    ),
                    
                    // Tab Content
                    Expanded(
                      child: TabBarView(
                        controller: _tabController,
                        children: [
                          // Analytics Tab
                          MilkDetailsAnalyticsTab(
                            milkRecord: _milkRecord!,
                            cattle: _cattle,
                            relatedRecords: _relatedRecords,
                          ),

                          // Records Tab
                          MilkDetailsRecordsTab(
                            milkRecord: _milkRecord!,
                            cattle: _cattle,
                            relatedRecords: _relatedRecords,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
    );
  }

  Widget _buildHeaderCard() {
    return Card(
      margin: const EdgeInsets.all(16.0),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: UniversalEmptyStateTheme.milk,
                  child: const Icon(
                    Icons.water_drop,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Milk Production Record',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (_cattle?.name != null)
                        Text(
                          'Cattle: ${_cattle!.name}',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: UniversalEmptyStateTheme.milk.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: UniversalEmptyStateTheme.milk.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Text(
                    '${_milkRecord?.quantity?.toStringAsFixed(1) ?? '0.0'}L',
                    style: TextStyle(
                      color: UniversalEmptyStateTheme.milk,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    'Date',
                    _milkRecord?.date != null
                        ? _formatDate(_milkRecord!.date!)
                        : 'Unknown',
                    Icons.calendar_today,
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    'Session',
                    _milkRecord?.session ?? 'Unknown',
                    Icons.schedule,
                  ),
                ),
              ],
            ),
            if (_milkRecord?.notes != null && _milkRecord!.notes!.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                'Notes',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                _milkRecord!.notes!,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.grey[600]),
        const SizedBox(width: 8),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
*/
