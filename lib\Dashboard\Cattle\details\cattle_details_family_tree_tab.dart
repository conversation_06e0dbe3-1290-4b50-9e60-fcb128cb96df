import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/cattle_isar.dart';
import '../controllers/cattle_details_controller.dart';
import '../utils/cattle_age_calculator.dart';
import '../../../utils/navigation_utils.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_constants.dart';
import '../../../constants/app_layout.dart';
import '../../widgets/index.dart';
import '../../widgets/universal_record_card.dart';

class CattleDetailsFamilyTreeTab extends StatelessWidget {
  const CattleDetailsFamilyTreeTab({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<CattleDetailsController>(
      builder: (context, controller, child) {
        final cattle = controller.cattle;
        final motherCattle = controller.motherCattle;
        final calves = controller.offspring;
        final siblings = controller.siblings;

        if (cattle == null) {
          return const Center(child: Text('No cattle data available'));
        }

        final bool hasRelationships = motherCattle != null ||
            calves.isNotEmpty ||
            siblings.isNotEmpty;

        return SingleChildScrollView(
          padding: const EdgeInsets.all(kPaddingMedium),
          child: hasRelationships
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildFamilyTreeHeader(cattle),
                    const SizedBox(height: kSpacingLarge),

                    // Mother section
                    if (motherCattle != null) ...[
                      _buildRelationshipSection(
                        title: 'Mother',
                        subtitle: 'Parent lineage',
                        icon: Icons.arrow_upward_rounded,
                        color: AppColors.cattleKpiColors[0],
                        relatives: [motherCattle],
                        relationship: 'Mother',
                      ),
                      const SizedBox(height: kSpacingLarge),
                    ],

                    // Siblings section
                    if (siblings.isNotEmpty) ...[
                      _buildRelationshipSection(
                        title: 'Siblings',
                        subtitle: '${siblings.length} sibling${siblings.length == 1 ? '' : 's'} from same mother',
                        icon: Icons.people_rounded,
                        color: AppColors.cattleKpiColors[1],
                        relatives: siblings,
                        relationship: 'Sibling',
                      ),
                      const SizedBox(height: kSpacingLarge),
                    ],

                    // Calves section
                    if (calves.isNotEmpty) ...[
                      _buildRelationshipSection(
                        title: 'Offspring',
                        subtitle: '${calves.length} calf${calves.length == 1 ? '' : 'ves'} born',
                        icon: Icons.arrow_downward_rounded,
                        color: AppColors.cattleKpiColors[2],
                        relatives: calves,
                        relationship: 'Calf',
                      ),
                    ],
                  ],
                )
              : _buildEmptyState(),
        );
      },
    );
  }

  /// Build family tree header section
  Widget _buildFamilyTreeHeader(CattleIsar cattle) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        UniversalFormField.sectionHeader(
          title: 'Family Tree',
          icon: Icons.family_restroom_rounded,
          color: AppColors.cattleKpiSection,
          subtitle: 'Family relationships for ${cattle.name ?? cattle.tagId ?? 'this cattle'}',
          filled: true,
          padding: EdgeInsets.zero,
        ),
      ],
    );
  }

  /// Build relationship section using UniversalRecordCard style
  Widget _buildRelationshipSection({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required List<CattleIsar> relatives,
    required String relationship,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        UniversalFormField.sectionHeader(
          title: title,
          icon: icon,
          color: color,
          subtitle: subtitle,
          filled: true,
          padding: EdgeInsets.zero,
        ),
        const SizedBox(height: kSpacingMedium),
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: relatives.length,
          separatorBuilder: (context, index) => const SizedBox(height: kSpacingSmall),
          itemBuilder: (context, index) {
            final relative = relatives[index];
            return _buildUniversalRelativeCard(relative, relationship, color);
          },
        ),
      ],
    );
  }

  /// Build relative card using UniversalRecordCard style
  Widget _buildUniversalRelativeCard(CattleIsar relative, String relationship, Color sectionColor) {
    final bool isMale = relative.gender == CattleGender.male;
    final Color genderColor = isMale
        ? AppColors.cattleGenderColors['Male']!
        : AppColors.cattleGenderColors['Female']!;

    // Calculate age using the unified utility
    final String age = CattleAgeCalculator.calculateAgeDisplay(relative);

    // Prepare row data for UniversalRecordCard
    final String nameAndTag = '${relative.name ?? 'Unknown'} (${relative.tagId ?? 'No Tag'})';
    final String genderText = relative.gender.displayName;
    final String ageText = age;
    final String relationshipText = relationship;

    // Optional third row for health/weight info
    String? row3Left;
    String? row3Right;
    IconData? row3LeftIcon;
    IconData? row3RightIcon;

    if (relative.healthInfo?.status != null &&
        relative.healthInfo!.status != HealthStatusType.unknown) {
      row3Left = relative.healthInfo!.status.displayName;
      row3LeftIcon = relative.healthInfo!.status.displayIcon;
    }

    if (relationship == 'Calf' && relative.weight != null) {
      row3Right = '${relative.weight} kg';
      row3RightIcon = Icons.monitor_weight_rounded;
    }

    return Builder(
      builder: (context) => UniversalRecordCard(
        // Row 1: Name (Tag) + Gender
        row1Left: nameAndTag,
        row1Right: genderText,
        row1LeftIcon: isMale ? Icons.male_rounded : Icons.female_rounded,
        row1RightIcon: isMale ? Icons.male_rounded : Icons.female_rounded,
        row1RightColor: genderColor,

        // Row 2: Age + Relationship
        row2Left: ageText,
        row2Right: relationshipText,
        row2LeftIcon: Icons.cake_rounded,
        row2RightIcon: _getRelationshipIcon(relationship),

        // Row 3: Health status + Weight (optional)
        row3Left: row3Left,
        row3Right: row3Right,
        row3LeftIcon: row3LeftIcon,
        row3RightIcon: row3RightIcon,

        // Styling
        primaryColor: sectionColor,

        // Navigation
        onTap: () {
          final tagId = relative.tagId;
          if (tagId != null && tagId.isNotEmpty) {
            navigateToCattleDetails(context, tagId);
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                  content: Text(
                      'Cannot navigate: Tag ID missing for ${relative.name ?? 'relative'}')),
            );
          }
        },
      ),
    );
  }



  /// Get appropriate icon for relationship type
  IconData _getRelationshipIcon(String relationship) {
    switch (relationship.toLowerCase()) {
      case 'mother':
        return Icons.arrow_upward_rounded;
      case 'sibling':
        return Icons.people_rounded;
      case 'calf':
        return Icons.arrow_downward_rounded;
      default:
        return Icons.family_restroom_rounded;
    }
  }

  /// Build modern empty state
  Widget _buildEmptyState() {
    return Center(
      child: Container(
        margin: const EdgeInsets.all(kPaddingLarge),
        padding: const EdgeInsets.all(kPaddingLarge * 2),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(kBorderRadius * 3),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 20,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Enhanced icon with gradient background
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppColors.cattleKpiColors[0].withValues(alpha: 0.8),
                    AppColors.cattleKpiColors[0].withValues(alpha: 0.6),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(kBorderRadius * 3),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.cattleKpiColors[0].withValues(alpha: 0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: const Icon(
                Icons.family_restroom_rounded,
                size: 40,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: kSpacingLarge),

            // Title
            const Text(
              'No Family Relationships',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: kSpacingMedium),

            // Description
            Text(
              'This cattle has no recorded mother, siblings, or calves in the system. Family relationships will appear here when available.',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: kSpacingLarge),

            // Helpful tips
            Container(
              padding: const EdgeInsets.all(kPaddingMedium),
              decoration: BoxDecoration(
                color: AppColors.cattleKpiColors[0].withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(kBorderRadius),
                border: Border.all(
                  color: AppColors.cattleKpiColors[0].withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.lightbulb_outline_rounded,
                    size: 20,
                    color: AppColors.cattleKpiColors[0],
                  ),
                  const SizedBox(width: kSpacingSmall),
                  Expanded(
                    child: Text(
                      'Add mother information when creating or editing cattle records to build family trees.',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.cattleKpiColors[0],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

}
