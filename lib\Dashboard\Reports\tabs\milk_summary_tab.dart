import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';
import '../models/milk_report_data_isar.dart';
import '../models/chart_data_isar.dart';

class MilkSummaryTab extends StatelessWidget {
  final MilkReportDataIsar reportData;
  final currencyFormat = NumberFormat.currency(locale: 'en_US', symbol: '\$');
  final quantityFormat = NumberFormat('##0.00');

  MilkSummaryTab({
    Key? key,
    required this.reportData,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final summaryData = reportData.getSummaryData();
    final chartData = reportData.getChartData();
    final timeSeriesData = reportData.getTimeSeriesChartData();

    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSummaryCard(summaryData),
            const SizedBox(height: 24),
            if (chartData.isNotEmpty) _buildTimeDistributionChart(chartData),
            const SizedBox(height: 24),
            if (timeSeriesData.isNotEmpty)
              _buildProductionTrend(timeSeriesData),
            const SizedBox(height: 24),
            _buildMorningEveningComparison(),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCard(Map<String, dynamic> summaryData) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Milk Production Summary',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildSummaryRow(
                'Total Records', '${summaryData['Total Records']}'),
            _buildSummaryRow(
                'Total Milk', '${summaryData['Total Milk (liters)']} L'),
            _buildSummaryRow('Average Daily Production',
                '${summaryData['Average Daily Production (liters)']} L'),
            _buildSummaryRow('Top Producer', summaryData['Top Producer']),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.blue,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeDistributionChart(List<ChartDataIsar> chartData) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Distribution by Time',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 300,
              child: PieChart(
                PieChartData(
                  sections: chartData.map((data) {
                    return PieChartSectionData(
                      value: data.value ?? 0,
                      title:
                          '${data.label ?? ""}\n${(data.value ?? 0).toStringAsFixed(1)} L',
                      color: Color(data.colorValue ?? Colors.grey.value), // ignore: deprecated_member_use (value is correct for storing ARGB int)
                      radius: 100,
                      titleStyle: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    );
                  }).toList(),
                  sectionsSpace: 2,
                  centerSpaceRadius: 40,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductionTrend(List<ChartDataIsar> timeSeriesData) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Milk Production Trend',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 300,
              child: LineChart(
                LineChartData(
                  gridData: const FlGridData(show: true),
                  titlesData: FlTitlesData(
                    leftTitles: const AxisTitles(
                      sideTitles:
                          SideTitles(showTitles: true, reservedSize: 40),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 30,
                        getTitlesWidget: (value, meta) {
                          if (value.toInt() >= 0 &&
                              value.toInt() < timeSeriesData.length) {
                            final date = timeSeriesData[value.toInt()].date;
                            return date != null
                                ? Text(DateFormat('MM/dd').format(date))
                                : const Text('');
                          }
                          return const Text('');
                        },
                      ),
                    ),
                  ),
                  borderData: FlBorderData(show: true),
                  lineBarsData: [
                    LineChartBarData(
                      spots: List.generate(timeSeriesData.length, (index) {
                        return FlSpot(
                            index.toDouble(), timeSeriesData[index].value ?? 0);
                      }),
                      isCurved: true,
                      color: Colors.blue,
                      barWidth: 3,
                      isStrokeCapRound: true,
                      dotData: const FlDotData(show: false),
                      belowBarData: BarAreaData(
                          show: true, color: Colors.blue.withAlpha(25)),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMorningEveningComparison() {
    // Since we don't have direct access to morning/evening data,
    // we'll create a placeholder widget
    double morningTotal = reportData.totalMilk != null
        ? reportData.totalMilk! * 0.4
        : 0; // Placeholder value
    double eveningTotal = reportData.totalMilk != null
        ? reportData.totalMilk! * 0.6
        : 0; // Placeholder value
    double total = reportData.totalMilk ?? 0;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Morning vs Evening Production',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'This chart would typically show a comparison between morning and evening milk production. '
              'In this implementation, we\'re using placeholder values based on the total milk production.',
              style: TextStyle(color: Colors.grey[600]),
            ),
            SizedBox(
              height: 200,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildDistributionBar(
                    'Morning',
                    morningTotal,
                    total,
                    const Color(0xFF2E7D32),
                  ),
                  const SizedBox(width: 32),
                  _buildDistributionBar(
                    'Evening',
                    eveningTotal,
                    total,
                    const Color(0xFF66BB6A),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDistributionBar(
    String label,
    double value,
    double total,
    Color color,
  ) {
    final percentage = total > 0 ? (value / total * 100) : 0;

    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Text(
          '${quantityFormat.format(value)}L',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: 60,
          height: 120,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: Colors.grey[200],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Container(
                width: 60,
                height: percentage * 1.2, // Scale to fit the 120px height
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(8),
                    bottomRight: Radius.circular(8),
                  ),
                  color: color,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
          ),
        ),
        Text(
          '${percentage.toStringAsFixed(1)}%',
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 12,
          ),
        ),
      ],
    );
  }
}
