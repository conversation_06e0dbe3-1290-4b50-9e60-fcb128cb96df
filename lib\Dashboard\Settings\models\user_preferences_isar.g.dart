// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_preferences_isar.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetUserPreferencesIsarCollection on Isar {
  IsarCollection<UserPreferencesIsar> get userPreferencesIsars =>
      this.collection();
}

const UserPreferencesIsarSchema = CollectionSchema(
  name: r'UserPreferencesIsar',
  id: -2918659535799530546,
  properties: {
    r'createdAt': PropertySchema(
      id: 0,
      name: r'createdAt',
      type: IsarType.dateTime,
    ),
    r'dashboardLayout': PropertySchema(
      id: 1,
      name: r'dashboardLayout',
      type: IsarType.string,
    ),
    r'defaultViewMode': PropertySchema(
      id: 2,
      name: r'defaultViewMode',
      type: IsarType.string,
    ),
    r'enableReminders': PropertySchema(
      id: 3,
      name: r'enableReminders',
      type: IsarType.bool,
    ),
    r'enableTutorials': PropertySchema(
      id: 4,
      name: r'enableTutorials',
      type: IsarType.bool,
    ),
    r'itemsPerPage': PropertySchema(
      id: 5,
      name: r'itemsPerPage',
      type: IsarType.long,
    ),
    r'reminderTime': PropertySchema(
      id: 6,
      name: r'reminderTime',
      type: IsarType.string,
    ),
    r'showWelcomeScreen': PropertySchema(
      id: 7,
      name: r'showWelcomeScreen',
      type: IsarType.bool,
    ),
    r'updatedAt': PropertySchema(
      id: 8,
      name: r'updatedAt',
      type: IsarType.dateTime,
    )
  },
  estimateSize: _userPreferencesIsarEstimateSize,
  serialize: _userPreferencesIsarSerialize,
  deserialize: _userPreferencesIsarDeserialize,
  deserializeProp: _userPreferencesIsarDeserializeProp,
  idName: r'id',
  indexes: {},
  links: {},
  embeddedSchemas: {},
  getId: _userPreferencesIsarGetId,
  getLinks: _userPreferencesIsarGetLinks,
  attach: _userPreferencesIsarAttach,
  version: '3.1.0+1',
);

int _userPreferencesIsarEstimateSize(
  UserPreferencesIsar object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.dashboardLayout;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.defaultViewMode;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.reminderTime;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _userPreferencesIsarSerialize(
  UserPreferencesIsar object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeDateTime(offsets[0], object.createdAt);
  writer.writeString(offsets[1], object.dashboardLayout);
  writer.writeString(offsets[2], object.defaultViewMode);
  writer.writeBool(offsets[3], object.enableReminders);
  writer.writeBool(offsets[4], object.enableTutorials);
  writer.writeLong(offsets[5], object.itemsPerPage);
  writer.writeString(offsets[6], object.reminderTime);
  writer.writeBool(offsets[7], object.showWelcomeScreen);
  writer.writeDateTime(offsets[8], object.updatedAt);
}

UserPreferencesIsar _userPreferencesIsarDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = UserPreferencesIsar();
  object.createdAt = reader.readDateTimeOrNull(offsets[0]);
  object.dashboardLayout = reader.readStringOrNull(offsets[1]);
  object.defaultViewMode = reader.readStringOrNull(offsets[2]);
  object.enableReminders = reader.readBoolOrNull(offsets[3]);
  object.enableTutorials = reader.readBoolOrNull(offsets[4]);
  object.id = id;
  object.itemsPerPage = reader.readLongOrNull(offsets[5]);
  object.reminderTime = reader.readStringOrNull(offsets[6]);
  object.showWelcomeScreen = reader.readBoolOrNull(offsets[7]);
  object.updatedAt = reader.readDateTimeOrNull(offsets[8]);
  return object;
}

P _userPreferencesIsarDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 1:
      return (reader.readStringOrNull(offset)) as P;
    case 2:
      return (reader.readStringOrNull(offset)) as P;
    case 3:
      return (reader.readBoolOrNull(offset)) as P;
    case 4:
      return (reader.readBoolOrNull(offset)) as P;
    case 5:
      return (reader.readLongOrNull(offset)) as P;
    case 6:
      return (reader.readStringOrNull(offset)) as P;
    case 7:
      return (reader.readBoolOrNull(offset)) as P;
    case 8:
      return (reader.readDateTimeOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _userPreferencesIsarGetId(UserPreferencesIsar object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _userPreferencesIsarGetLinks(
    UserPreferencesIsar object) {
  return [];
}

void _userPreferencesIsarAttach(
    IsarCollection<dynamic> col, Id id, UserPreferencesIsar object) {
  object.id = id;
}

extension UserPreferencesIsarQueryWhereSort
    on QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QWhere> {
  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension UserPreferencesIsarQueryWhere
    on QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QWhereClause> {
  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterWhereClause>
      idEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterWhereClause>
      idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterWhereClause>
      idGreaterThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterWhereClause>
      idLessThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterWhereClause>
      idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension UserPreferencesIsarQueryFilter on QueryBuilder<UserPreferencesIsar,
    UserPreferencesIsar, QFilterCondition> {
  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      createdAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      createdAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      createdAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      createdAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      createdAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      createdAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'createdAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      dashboardLayoutIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'dashboardLayout',
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      dashboardLayoutIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'dashboardLayout',
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      dashboardLayoutEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'dashboardLayout',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      dashboardLayoutGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'dashboardLayout',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      dashboardLayoutLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'dashboardLayout',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      dashboardLayoutBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'dashboardLayout',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      dashboardLayoutStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'dashboardLayout',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      dashboardLayoutEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'dashboardLayout',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      dashboardLayoutContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'dashboardLayout',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      dashboardLayoutMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'dashboardLayout',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      dashboardLayoutIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'dashboardLayout',
        value: '',
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      dashboardLayoutIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'dashboardLayout',
        value: '',
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      defaultViewModeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'defaultViewMode',
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      defaultViewModeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'defaultViewMode',
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      defaultViewModeEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'defaultViewMode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      defaultViewModeGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'defaultViewMode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      defaultViewModeLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'defaultViewMode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      defaultViewModeBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'defaultViewMode',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      defaultViewModeStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'defaultViewMode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      defaultViewModeEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'defaultViewMode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      defaultViewModeContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'defaultViewMode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      defaultViewModeMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'defaultViewMode',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      defaultViewModeIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'defaultViewMode',
        value: '',
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      defaultViewModeIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'defaultViewMode',
        value: '',
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      enableRemindersIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'enableReminders',
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      enableRemindersIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'enableReminders',
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      enableRemindersEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'enableReminders',
        value: value,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      enableTutorialsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'enableTutorials',
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      enableTutorialsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'enableTutorials',
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      enableTutorialsEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'enableTutorials',
        value: value,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      idEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      itemsPerPageIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'itemsPerPage',
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      itemsPerPageIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'itemsPerPage',
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      itemsPerPageEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'itemsPerPage',
        value: value,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      itemsPerPageGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'itemsPerPage',
        value: value,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      itemsPerPageLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'itemsPerPage',
        value: value,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      itemsPerPageBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'itemsPerPage',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      reminderTimeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'reminderTime',
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      reminderTimeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'reminderTime',
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      reminderTimeEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'reminderTime',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      reminderTimeGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'reminderTime',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      reminderTimeLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'reminderTime',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      reminderTimeBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'reminderTime',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      reminderTimeStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'reminderTime',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      reminderTimeEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'reminderTime',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      reminderTimeContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'reminderTime',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      reminderTimeMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'reminderTime',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      reminderTimeIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'reminderTime',
        value: '',
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      reminderTimeIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'reminderTime',
        value: '',
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      showWelcomeScreenIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'showWelcomeScreen',
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      showWelcomeScreenIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'showWelcomeScreen',
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      showWelcomeScreenEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'showWelcomeScreen',
        value: value,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      updatedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      updatedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      updatedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      updatedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      updatedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterFilterCondition>
      updatedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'updatedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension UserPreferencesIsarQueryObject on QueryBuilder<UserPreferencesIsar,
    UserPreferencesIsar, QFilterCondition> {}

extension UserPreferencesIsarQueryLinks on QueryBuilder<UserPreferencesIsar,
    UserPreferencesIsar, QFilterCondition> {}

extension UserPreferencesIsarQuerySortBy
    on QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QSortBy> {
  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterSortBy>
      sortByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterSortBy>
      sortByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterSortBy>
      sortByDashboardLayout() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dashboardLayout', Sort.asc);
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterSortBy>
      sortByDashboardLayoutDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dashboardLayout', Sort.desc);
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterSortBy>
      sortByDefaultViewMode() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'defaultViewMode', Sort.asc);
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterSortBy>
      sortByDefaultViewModeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'defaultViewMode', Sort.desc);
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterSortBy>
      sortByEnableReminders() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'enableReminders', Sort.asc);
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterSortBy>
      sortByEnableRemindersDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'enableReminders', Sort.desc);
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterSortBy>
      sortByEnableTutorials() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'enableTutorials', Sort.asc);
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterSortBy>
      sortByEnableTutorialsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'enableTutorials', Sort.desc);
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterSortBy>
      sortByItemsPerPage() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'itemsPerPage', Sort.asc);
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterSortBy>
      sortByItemsPerPageDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'itemsPerPage', Sort.desc);
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterSortBy>
      sortByReminderTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reminderTime', Sort.asc);
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterSortBy>
      sortByReminderTimeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reminderTime', Sort.desc);
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterSortBy>
      sortByShowWelcomeScreen() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'showWelcomeScreen', Sort.asc);
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterSortBy>
      sortByShowWelcomeScreenDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'showWelcomeScreen', Sort.desc);
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterSortBy>
      sortByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterSortBy>
      sortByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }
}

extension UserPreferencesIsarQuerySortThenBy
    on QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QSortThenBy> {
  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterSortBy>
      thenByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterSortBy>
      thenByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterSortBy>
      thenByDashboardLayout() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dashboardLayout', Sort.asc);
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterSortBy>
      thenByDashboardLayoutDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dashboardLayout', Sort.desc);
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterSortBy>
      thenByDefaultViewMode() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'defaultViewMode', Sort.asc);
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterSortBy>
      thenByDefaultViewModeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'defaultViewMode', Sort.desc);
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterSortBy>
      thenByEnableReminders() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'enableReminders', Sort.asc);
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterSortBy>
      thenByEnableRemindersDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'enableReminders', Sort.desc);
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterSortBy>
      thenByEnableTutorials() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'enableTutorials', Sort.asc);
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterSortBy>
      thenByEnableTutorialsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'enableTutorials', Sort.desc);
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterSortBy>
      thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterSortBy>
      thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterSortBy>
      thenByItemsPerPage() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'itemsPerPage', Sort.asc);
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterSortBy>
      thenByItemsPerPageDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'itemsPerPage', Sort.desc);
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterSortBy>
      thenByReminderTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reminderTime', Sort.asc);
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterSortBy>
      thenByReminderTimeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reminderTime', Sort.desc);
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterSortBy>
      thenByShowWelcomeScreen() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'showWelcomeScreen', Sort.asc);
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterSortBy>
      thenByShowWelcomeScreenDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'showWelcomeScreen', Sort.desc);
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterSortBy>
      thenByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QAfterSortBy>
      thenByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }
}

extension UserPreferencesIsarQueryWhereDistinct
    on QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QDistinct> {
  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QDistinct>
      distinctByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'createdAt');
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QDistinct>
      distinctByDashboardLayout({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'dashboardLayout',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QDistinct>
      distinctByDefaultViewMode({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'defaultViewMode',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QDistinct>
      distinctByEnableReminders() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'enableReminders');
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QDistinct>
      distinctByEnableTutorials() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'enableTutorials');
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QDistinct>
      distinctByItemsPerPage() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'itemsPerPage');
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QDistinct>
      distinctByReminderTime({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'reminderTime', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QDistinct>
      distinctByShowWelcomeScreen() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'showWelcomeScreen');
    });
  }

  QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QDistinct>
      distinctByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'updatedAt');
    });
  }
}

extension UserPreferencesIsarQueryProperty
    on QueryBuilder<UserPreferencesIsar, UserPreferencesIsar, QQueryProperty> {
  QueryBuilder<UserPreferencesIsar, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<UserPreferencesIsar, DateTime?, QQueryOperations>
      createdAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'createdAt');
    });
  }

  QueryBuilder<UserPreferencesIsar, String?, QQueryOperations>
      dashboardLayoutProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'dashboardLayout');
    });
  }

  QueryBuilder<UserPreferencesIsar, String?, QQueryOperations>
      defaultViewModeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'defaultViewMode');
    });
  }

  QueryBuilder<UserPreferencesIsar, bool?, QQueryOperations>
      enableRemindersProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'enableReminders');
    });
  }

  QueryBuilder<UserPreferencesIsar, bool?, QQueryOperations>
      enableTutorialsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'enableTutorials');
    });
  }

  QueryBuilder<UserPreferencesIsar, int?, QQueryOperations>
      itemsPerPageProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'itemsPerPage');
    });
  }

  QueryBuilder<UserPreferencesIsar, String?, QQueryOperations>
      reminderTimeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'reminderTime');
    });
  }

  QueryBuilder<UserPreferencesIsar, bool?, QQueryOperations>
      showWelcomeScreenProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'showWelcomeScreen');
    });
  }

  QueryBuilder<UserPreferencesIsar, DateTime?, QQueryOperations>
      updatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'updatedAt');
    });
  }
}
