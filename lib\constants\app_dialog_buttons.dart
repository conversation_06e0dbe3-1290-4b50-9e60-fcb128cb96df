import 'package:flutter/material.dart';

/// Universal Dialog Action Buttons System
/// 
/// Provides consistent button styling and behavior for all dialogs throughout the app.
/// Includes primary actions (add, save, edit, update), secondary actions (cancel, clear),
/// and destructive actions (delete) with appropriate styling and accessibility.

class UniversalDialogButtons {
  
  // ============================================================================
  // PRIMARY ACTION BUTTONS (Main dialog actions)
  // ============================================================================
  
  /// Creates an "Add" button for dialogs
  /// Used when creating new records or items
  static Widget add({
    required VoidCallback onPressed,
    String text = 'Add',
    bool isLoading = false,
    Color? backgroundColor,
  }) {
    return _buildPrimaryButton(
      onPressed: onPressed,
      text: text,
      icon: Icons.add,
      iconColor: Colors.white,
      isLoading: isLoading,
      backgroundColor: backgroundColor ?? const Color(0xFF2E7D32), // Green - Add
    );
  }

  /// Creates a "Save" button for dialogs
  /// Used when saving form data or changes
  static Widget save({
    required VoidCallback onPressed,
    String text = 'Save',
    bool isLoading = false,
    Color? backgroundColor,
  }) {
    return _buildPrimaryButton(
      onPressed: onPressed,
      text: text,
      icon: Icons.save,
      iconColor: Colors.white,
      isLoading: isLoading,
      backgroundColor: backgroundColor ?? const Color(0xFF388E3C), // Green - Save
    );
  }

  /// Creates an "Edit" button for dialogs
  /// Used when entering edit mode
  static Widget edit({
    required VoidCallback onPressed,
    String text = 'Edit',
    bool isLoading = false,
    Color? backgroundColor,
  }) {
    return _buildPrimaryButton(
      onPressed: onPressed,
      text: text,
      icon: Icons.edit,
      iconColor: Colors.white,
      isLoading: isLoading,
      backgroundColor: backgroundColor ?? const Color(0xFF7B1FA2), // Purple - Edit
    );
  }

  /// Creates an "Update" button for dialogs
  /// Used when updating existing records
  static Widget update({
    required VoidCallback onPressed,
    String text = 'Update',
    bool isLoading = false,
    Color? backgroundColor,
  }) {
    return _buildPrimaryButton(
      onPressed: onPressed,
      text: text,
      icon: Icons.update,
      iconColor: Colors.white,
      isLoading: isLoading,
      backgroundColor: backgroundColor ?? const Color(0xFF8E24AA), // Purple - Update
    );
  }

  // ============================================================================
  // SECONDARY ACTION BUTTONS (Supporting actions)
  // ============================================================================
  
  /// Creates a "Cancel" button for dialogs
  /// Used to close dialog without saving changes
  static Widget cancel({
    required VoidCallback onPressed,
    String text = 'Cancel',
    Color? backgroundColor,
  }) {
    return Expanded(
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor ?? const Color(0xFF1976D2), // Blue - Cancel
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2,
          minimumSize: const Size(120, 48),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.close, size: 18, color: Colors.white),
            const SizedBox(width: 8),
            Text(
              text,
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
          ],
        ),
      ),
    );
  }

  /// Creates a "Clear" button for dialogs
  /// Used to clear form fields or reset data
  static Widget clear({
    required VoidCallback onPressed,
    String text = 'Clear',
    Color? backgroundColor,
  }) {
    return Expanded(
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor ?? const Color(0xFF0288D1), // Blue - Clear
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2,
          minimumSize: const Size(120, 48),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.clear_all, size: 18, color: Colors.white),
            const SizedBox(width: 8),
            Text(
              text,
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
          ],
        ),
      ),
    );
  }

  // ============================================================================
  // DESTRUCTIVE ACTION BUTTONS (Dangerous actions)
  // ============================================================================
  
  /// Creates a "Delete" button for dialogs
  /// Used for destructive actions with red styling
  static Widget delete({
    required VoidCallback onPressed,
    String text = 'Delete',
    bool isLoading = false,
    bool showIcon = true,
  }) {
    return Expanded(
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.red,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2,
          minimumSize: const Size(120, 48),
        ),
        child: isLoading
            ? const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Colors.white,
                ),
              )
            : Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (showIcon) ...[
                    const Icon(Icons.delete, size: 18, color: Colors.white),
                    const SizedBox(width: 8),
                  ],
                  Text(
                    text,
                    style: const TextStyle(fontWeight: FontWeight.w600),
                  ),
                ],
              ),
      ),
    );
  }

  // ============================================================================
  // UTILITY METHODS (Internal helpers)
  // ============================================================================
  
  /// Internal method to build consistent primary action buttons
  static Widget _buildPrimaryButton({
    required VoidCallback onPressed,
    required String text,
    required IconData icon,
    required bool isLoading,
    required Color backgroundColor,
    Color iconColor = Colors.white,
  }) {
    return Expanded(
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2,
          minimumSize: const Size(120, 48), // Consistent minimum size
        ),
        child: isLoading
            ? const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Colors.white,
                ),
              )
            : Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(icon, size: 18, color: iconColor),
                  const SizedBox(width: 8),
                  Text(
                    text,
                    style: const TextStyle(fontWeight: FontWeight.w600),
                  ),
                ],
              ),
      ),
    );
  }

  // ============================================================================
  // COMMON BUTTON COMBINATIONS (Pre-built button rows)
  // ============================================================================
  
  /// Creates a standard Cancel + Save button row
  static Widget cancelSaveRow({
    required VoidCallback onCancel,
    required VoidCallback onSave,
    String cancelText = 'Cancel',
    String saveText = 'Save',
    bool isSaving = false,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        cancel(onPressed: onCancel, text: cancelText),
        const SizedBox(width: 12),
        save(onPressed: onSave, text: saveText, isLoading: isSaving),
      ],
    );
  }

  /// Creates a standard Cancel + Add button row
  static Widget cancelAddRow({
    required VoidCallback onCancel,
    required VoidCallback onAdd,
    String cancelText = 'Cancel',
    String addText = 'Add',
    bool isAdding = false,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        cancel(onPressed: onCancel, text: cancelText),
        const SizedBox(width: 12),
        add(onPressed: onAdd, text: addText, isLoading: isAdding),
      ],
    );
  }

  /// Creates a standard Cancel + Update button row
  static Widget cancelUpdateRow({
    required VoidCallback onCancel,
    required VoidCallback onUpdate,
    String cancelText = 'Cancel',
    String updateText = 'Update',
    bool isUpdating = false,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        cancel(onPressed: onCancel, text: cancelText),
        const SizedBox(width: 12),
        update(onPressed: onUpdate, text: updateText, isLoading: isUpdating),
      ],
    );
  }

  /// Creates a Clear + Cancel + Save button row
  static Widget clearCancelSaveRow({
    required VoidCallback onClear,
    required VoidCallback onCancel,
    required VoidCallback onSave,
    String clearText = 'Clear',
    String cancelText = 'Cancel',
    String saveText = 'Save',
    bool isSaving = false,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        clear(onPressed: onClear, text: clearText),
        Row(
          children: [
            cancel(onPressed: onCancel, text: cancelText),
            const SizedBox(width: 12),
            save(onPressed: onSave, text: saveText, isLoading: isSaving),
          ],
        ),
      ],
    );
  }
}
