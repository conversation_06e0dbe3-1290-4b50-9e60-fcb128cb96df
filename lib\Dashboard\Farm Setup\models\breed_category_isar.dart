import 'package:flutter/material.dart';
import 'package:isar/isar.dart';
import 'package:uuid/uuid.dart';

part 'breed_category_isar.g.dart';

/// Represents a category of breeds in the system
@collection
class BreedCategoryIsar {
  /// Isar database ID (auto-incremented)
  Id id = Isar.autoIncrement;

  /// Business ID (UUID) for the breed category - indexed for quick lookup
  @Index(unique: true)
  String? businessId;

  /// Name of the breed category
  @Index(caseSensitive: false)
  String? name;

  /// Description of the breed category
  String? description;

  /// Icon code point to represent this breed category in the UI
  int? iconCodePoint;

  /// Icon font family
  String? iconFontFamily;

  /// Icon font package
  String? iconFontPackage;

  /// Color value for the breed category (stored as integer)
  int? colorValue;

  /// The ID of the animal type this breed category belongs to
  @Index(type: IndexType.value)
  String? animalTypeId;

  /// Date when the record was created
  DateTime? createdAt;

  /// Date when the record was last updated
  DateTime? updatedAt;

  /// Farm Business ID (UUID) for multi-farm support (even if not using)
  String? farmBusinessId;

  /// Default constructor for Isar
  BreedCategoryIsar();

  /// Static constant IDs for predefined breeds to avoid ID regeneration on reinstall
  /// Format: breed_[animal]_[name] - simple, readable, and easy to use
  static const Map<String, String> staticIds = {
    // Cow breeds
    'holstein': 'breed_cow_holstein',
    'angus': 'breed_cow_angus',

    // Buffalo breeds
    'murrah': 'breed_buffalo_murrah',
    'nili-ravi': 'breed_buffalo_niliravi',

    // Goat breeds
    'alpine': 'breed_goat_alpine',
    'boer': 'breed_goat_boer',

    // Sheep breeds
    'merino': 'breed_sheep_merino',
    'suffolk': 'breed_sheep_suffolk',

    // Horse breeds
    'arabian': 'breed_horse_arabian',
    'quarter horse': 'breed_horse_quarterhorse',
    // Add new breeds below with the same format
  };

  /// Direct access to common breed IDs for easier reference
  /// Cow breeds
  static const String holsteinId = 'breed_cow_holstein';
  static const String angusId = 'breed_cow_angus';

  /// Buffalo breeds
  static const String murrahId = 'breed_buffalo_murrah';
  static const String niliraviId = 'breed_buffalo_niliravi';

  /// Goat breeds
  static const String alpineId = 'breed_goat_alpine';
  static const String boerId = 'breed_goat_boer';

  /// Sheep breeds
  static const String merinoId = 'breed_sheep_merino';
  static const String suffolkId = 'breed_sheep_suffolk';

  /// Horse breeds
  static const String arabianId = 'breed_horse_arabian';
  static const String quarterhorseId = 'breed_horse_quarterhorse';

  /// Get all breed IDs for a specific animal type
  /// Returns a list of breed IDs for the given animal type
  static List<String> getBreedsForAnimalType(String animalType) {
    final normalizedType = animalType.toLowerCase();
    final prefix = 'breed_${normalizedType}_';

    return staticIds.values
        .where((id) => id.startsWith(prefix))
        .toList();
  }

  /// Get a breed ID by name and animal type
  /// Returns the breed ID if found, null otherwise
  static String? getBreedId(String breedName, String animalType) {
    final normalizedBreed = breedName.toLowerCase().replaceAll(' ', '');
    final normalizedType = animalType.toLowerCase();
    final expectedId = 'breed_${normalizedType}_$normalizedBreed';

    // Check if the ID exists in the values
    if (staticIds.values.contains(expectedId)) {
      return expectedId;
    }

    // If not found by direct match, try to find by key
    return staticIds[normalizedBreed];
  }

  /// Named constructor for creating a BreedCategory with values
  factory BreedCategoryIsar.create({
    required String name,
    required String animalTypeId,
    String? description,
    required IconData icon,
    required Color color,
    String? businessId,
  }) {
    final String normalizedName = name.toLowerCase();
    // Use provided businessId, or a static ID if the breed is predefined, otherwise generate a new UUID
    final String id = businessId ?? staticIds[normalizedName] ?? const Uuid().v4();

    return BreedCategoryIsar()
      ..businessId = id
      ..name = name
      ..animalTypeId = animalTypeId
      ..description = description
      ..iconCodePoint = icon.codePoint
      ..iconFontFamily = icon.fontFamily
      ..colorValue = color.value // ignore: deprecated_member_use (value is correct for storing ARGB int)
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();
  }

  /// Get the icon data for use in the UI
  @ignore
  IconData get icon {
    if (iconCodePoint != null) {
      return IconData(
        iconCodePoint!,
        fontFamily: iconFontFamily ?? 'MaterialIcons',
      );
    }
    return Icons.pets_outlined;
  }

  /// Set the icon data
  set icon(IconData value) {
    iconCodePoint = value.codePoint;
    iconFontFamily = value.fontFamily;
  }

  /// Get color for this breed
  @ignore
  Color get color {
    if (colorValue != null) {
      return Color(colorValue!);
    }
    return const Color(0xFF2E7D32); // Default green color
  }

  /// Set the color value
  set color(Color value) {
    colorValue = value.value; // ignore: deprecated_member_use (value is correct for storing ARGB int)
  }

  factory BreedCategoryIsar.fromMap(Map<String, dynamic> map) {
    final breedCategory = BreedCategoryIsar()
      ..businessId = map['id'] as String?
      ..name = map['name'] as String?
      ..animalTypeId = map['animalTypeId'] as String?
      ..description = map['description'] as String?;

    if (map['iconCodePoint'] != null) {
      breedCategory.iconCodePoint = map['iconCodePoint'] as int;
      breedCategory.iconFontFamily =
          map['iconFontFamily'] as String? ?? 'MaterialIcons';
    }

    if (map['colorValue'] != null) {
      breedCategory.colorValue = map['colorValue'] as int;
    }

    if (map['createdAt'] != null) {
      breedCategory.createdAt = DateTime.parse(map['createdAt'] as String);
    }

    if (map['updatedAt'] != null) {
      breedCategory.updatedAt = DateTime.parse(map['updatedAt'] as String);
    }

    return breedCategory;
  }

  Map<String, dynamic> toMap() {
    return {
      'id': businessId,
      'name': name,
      'animalTypeId': animalTypeId,
      'description': description,
      'iconCodePoint': iconCodePoint,
      'iconFontFamily': iconFontFamily,
      'colorValue': colorValue,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  BreedCategoryIsar copyWith({
    String? businessId,
    String? name,
    String? animalTypeId,
    String? description,
    IconData? icon,
    Color? color,
  }) {
    final result = BreedCategoryIsar()
      ..id = id
      ..businessId = businessId ?? this.businessId
      ..name = name ?? this.name
      ..animalTypeId = animalTypeId ?? this.animalTypeId
      ..description = description ?? this.description
      ..createdAt = createdAt
      ..updatedAt = DateTime.now();

    if (icon != null) {
      result.iconCodePoint = icon.codePoint;
      result.iconFontFamily = icon.fontFamily;
    } else if (iconCodePoint != null) {
      result.iconCodePoint = iconCodePoint;
      result.iconFontFamily = iconFontFamily;
    }

    if (color != null) {
      result.colorValue = color.value; // ignore: deprecated_member_use (value is correct for storing ARGB int)
    } else if (colorValue != null) {
      result.colorValue = colorValue;
    }

    return result;
  }

  Map<String, dynamic> toJson() => toMap();
  factory BreedCategoryIsar.fromJson(Map<String, dynamic> json) =>
      BreedCategoryIsar.fromMap(json);
}
