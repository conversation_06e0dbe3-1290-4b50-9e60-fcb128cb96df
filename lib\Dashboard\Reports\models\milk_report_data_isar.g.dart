// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'milk_report_data_isar.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetMilkReportDataIsarCollection on Isar {
  IsarCollection<MilkReportDataIsar> get milkReportDataIsars =>
      this.collection();
}

const MilkReportDataIsarSchema = CollectionSchema(
  name: r'MilkReportDataIsar',
  id: -15223730739067972,
  properties: {
    r'averageMilkPerCow': PropertySchema(
      id: 0,
      name: r'averageMilkPerCow',
      type: IsarType.double,
    ),
    r'averageMilkPerDay': PropertySchema(
      id: 1,
      name: r'averageMilkPerDay',
      type: IsarType.double,
    ),
    r'businessId': PropertySchema(
      id: 2,
      name: r'businessId',
      type: IsarType.string,
    ),
    r'createdAt': PropertySchema(
      id: 3,
      name: r'createdAt',
      type: IsarType.dateTime,
    ),
    r'endDate': PropertySchema(
      id: 4,
      name: r'endDate',
      type: IsarType.dateTime,
    ),
    r'filterCriteria': PropertySchema(
      id: 5,
      name: r'filterCriteria',
      type: IsarType.string,
    ),
    r'generatedAt': PropertySchema(
      id: 6,
      name: r'generatedAt',
      type: IsarType.dateTime,
    ),
    r'milkDates': PropertySchema(
      id: 7,
      name: r'milkDates',
      type: IsarType.dateTimeList,
    ),
    r'milkTimeColors': PropertySchema(
      id: 8,
      name: r'milkTimeColors',
      type: IsarType.longList,
    ),
    r'milkTimeLabels': PropertySchema(
      id: 9,
      name: r'milkTimeLabels',
      type: IsarType.stringList,
    ),
    r'milkTimeValues': PropertySchema(
      id: 10,
      name: r'milkTimeValues',
      type: IsarType.doubleList,
    ),
    r'milkValues': PropertySchema(
      id: 11,
      name: r'milkValues',
      type: IsarType.doubleList,
    ),
    r'reportType': PropertySchema(
      id: 12,
      name: r'reportType',
      type: IsarType.string,
    ),
    r'startDate': PropertySchema(
      id: 13,
      name: r'startDate',
      type: IsarType.dateTime,
    ),
    r'title': PropertySchema(
      id: 14,
      name: r'title',
      type: IsarType.string,
    ),
    r'topProducerAmount': PropertySchema(
      id: 15,
      name: r'topProducerAmount',
      type: IsarType.double,
    ),
    r'topProducerId': PropertySchema(
      id: 16,
      name: r'topProducerId',
      type: IsarType.string,
    ),
    r'topProducerName': PropertySchema(
      id: 17,
      name: r'topProducerName',
      type: IsarType.string,
    ),
    r'totalMilk': PropertySchema(
      id: 18,
      name: r'totalMilk',
      type: IsarType.double,
    ),
    r'totalRecords': PropertySchema(
      id: 19,
      name: r'totalRecords',
      type: IsarType.long,
    ),
    r'updatedAt': PropertySchema(
      id: 20,
      name: r'updatedAt',
      type: IsarType.dateTime,
    )
  },
  estimateSize: _milkReportDataIsarEstimateSize,
  serialize: _milkReportDataIsarSerialize,
  deserialize: _milkReportDataIsarDeserialize,
  deserializeProp: _milkReportDataIsarDeserializeProp,
  idName: r'id',
  indexes: {
    r'reportType': IndexSchema(
      id: 3559997651334899995,
      name: r'reportType',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'reportType',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'businessId': IndexSchema(
      id: 2228048290814354584,
      name: r'businessId',
      unique: true,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'businessId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    )
  },
  links: {},
  embeddedSchemas: {},
  getId: _milkReportDataIsarGetId,
  getLinks: _milkReportDataIsarGetLinks,
  attach: _milkReportDataIsarAttach,
  version: '3.1.0+1',
);

int _milkReportDataIsarEstimateSize(
  MilkReportDataIsar object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.businessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.filterCriteria;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.milkDates;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  {
    final value = object.milkTimeColors;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  {
    final list = object.milkTimeLabels;
    if (list != null) {
      bytesCount += 3 + list.length * 3;
      {
        for (var i = 0; i < list.length; i++) {
          final value = list[i];
          bytesCount += value.length * 3;
        }
      }
    }
  }
  {
    final value = object.milkTimeValues;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  {
    final value = object.milkValues;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  {
    final value = object.reportType;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.title;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.topProducerId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.topProducerName;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _milkReportDataIsarSerialize(
  MilkReportDataIsar object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeDouble(offsets[0], object.averageMilkPerCow);
  writer.writeDouble(offsets[1], object.averageMilkPerDay);
  writer.writeString(offsets[2], object.businessId);
  writer.writeDateTime(offsets[3], object.createdAt);
  writer.writeDateTime(offsets[4], object.endDate);
  writer.writeString(offsets[5], object.filterCriteria);
  writer.writeDateTime(offsets[6], object.generatedAt);
  writer.writeDateTimeList(offsets[7], object.milkDates);
  writer.writeLongList(offsets[8], object.milkTimeColors);
  writer.writeStringList(offsets[9], object.milkTimeLabels);
  writer.writeDoubleList(offsets[10], object.milkTimeValues);
  writer.writeDoubleList(offsets[11], object.milkValues);
  writer.writeString(offsets[12], object.reportType);
  writer.writeDateTime(offsets[13], object.startDate);
  writer.writeString(offsets[14], object.title);
  writer.writeDouble(offsets[15], object.topProducerAmount);
  writer.writeString(offsets[16], object.topProducerId);
  writer.writeString(offsets[17], object.topProducerName);
  writer.writeDouble(offsets[18], object.totalMilk);
  writer.writeLong(offsets[19], object.totalRecords);
  writer.writeDateTime(offsets[20], object.updatedAt);
}

MilkReportDataIsar _milkReportDataIsarDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = MilkReportDataIsar(
    endDate: reader.readDateTimeOrNull(offsets[4]),
    startDate: reader.readDateTimeOrNull(offsets[13]),
  );
  object.averageMilkPerCow = reader.readDoubleOrNull(offsets[0]);
  object.averageMilkPerDay = reader.readDoubleOrNull(offsets[1]);
  object.businessId = reader.readStringOrNull(offsets[2]);
  object.createdAt = reader.readDateTimeOrNull(offsets[3]);
  object.filterCriteria = reader.readStringOrNull(offsets[5]);
  object.generatedAt = reader.readDateTimeOrNull(offsets[6]);
  object.id = id;
  object.milkDates = reader.readDateTimeList(offsets[7]);
  object.milkTimeColors = reader.readLongList(offsets[8]);
  object.milkTimeLabels = reader.readStringList(offsets[9]);
  object.milkTimeValues = reader.readDoubleList(offsets[10]);
  object.milkValues = reader.readDoubleList(offsets[11]);
  object.reportType = reader.readStringOrNull(offsets[12]);
  object.title = reader.readStringOrNull(offsets[14]);
  object.topProducerAmount = reader.readDoubleOrNull(offsets[15]);
  object.topProducerId = reader.readStringOrNull(offsets[16]);
  object.topProducerName = reader.readStringOrNull(offsets[17]);
  object.totalMilk = reader.readDoubleOrNull(offsets[18]);
  object.totalRecords = reader.readLongOrNull(offsets[19]);
  object.updatedAt = reader.readDateTimeOrNull(offsets[20]);
  return object;
}

P _milkReportDataIsarDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readDoubleOrNull(offset)) as P;
    case 1:
      return (reader.readDoubleOrNull(offset)) as P;
    case 2:
      return (reader.readStringOrNull(offset)) as P;
    case 3:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 4:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 5:
      return (reader.readStringOrNull(offset)) as P;
    case 6:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 7:
      return (reader.readDateTimeList(offset)) as P;
    case 8:
      return (reader.readLongList(offset)) as P;
    case 9:
      return (reader.readStringList(offset)) as P;
    case 10:
      return (reader.readDoubleList(offset)) as P;
    case 11:
      return (reader.readDoubleList(offset)) as P;
    case 12:
      return (reader.readStringOrNull(offset)) as P;
    case 13:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 14:
      return (reader.readStringOrNull(offset)) as P;
    case 15:
      return (reader.readDoubleOrNull(offset)) as P;
    case 16:
      return (reader.readStringOrNull(offset)) as P;
    case 17:
      return (reader.readStringOrNull(offset)) as P;
    case 18:
      return (reader.readDoubleOrNull(offset)) as P;
    case 19:
      return (reader.readLongOrNull(offset)) as P;
    case 20:
      return (reader.readDateTimeOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _milkReportDataIsarGetId(MilkReportDataIsar object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _milkReportDataIsarGetLinks(
    MilkReportDataIsar object) {
  return [];
}

void _milkReportDataIsarAttach(
    IsarCollection<dynamic> col, Id id, MilkReportDataIsar object) {
  object.id = id;
}

extension MilkReportDataIsarByIndex on IsarCollection<MilkReportDataIsar> {
  Future<MilkReportDataIsar?> getByBusinessId(String? businessId) {
    return getByIndex(r'businessId', [businessId]);
  }

  MilkReportDataIsar? getByBusinessIdSync(String? businessId) {
    return getByIndexSync(r'businessId', [businessId]);
  }

  Future<bool> deleteByBusinessId(String? businessId) {
    return deleteByIndex(r'businessId', [businessId]);
  }

  bool deleteByBusinessIdSync(String? businessId) {
    return deleteByIndexSync(r'businessId', [businessId]);
  }

  Future<List<MilkReportDataIsar?>> getAllByBusinessId(
      List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndex(r'businessId', values);
  }

  List<MilkReportDataIsar?> getAllByBusinessIdSync(
      List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndexSync(r'businessId', values);
  }

  Future<int> deleteAllByBusinessId(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndex(r'businessId', values);
  }

  int deleteAllByBusinessIdSync(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndexSync(r'businessId', values);
  }

  Future<Id> putByBusinessId(MilkReportDataIsar object) {
    return putByIndex(r'businessId', object);
  }

  Id putByBusinessIdSync(MilkReportDataIsar object, {bool saveLinks = true}) {
    return putByIndexSync(r'businessId', object, saveLinks: saveLinks);
  }

  Future<List<Id>> putAllByBusinessId(List<MilkReportDataIsar> objects) {
    return putAllByIndex(r'businessId', objects);
  }

  List<Id> putAllByBusinessIdSync(List<MilkReportDataIsar> objects,
      {bool saveLinks = true}) {
    return putAllByIndexSync(r'businessId', objects, saveLinks: saveLinks);
  }
}

extension MilkReportDataIsarQueryWhereSort
    on QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QWhere> {
  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension MilkReportDataIsarQueryWhere
    on QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QWhereClause> {
  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterWhereClause>
      idEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterWhereClause>
      idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterWhereClause>
      idGreaterThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterWhereClause>
      idLessThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterWhereClause>
      idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterWhereClause>
      reportTypeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'reportType',
        value: [null],
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterWhereClause>
      reportTypeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'reportType',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterWhereClause>
      reportTypeEqualTo(String? reportType) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'reportType',
        value: [reportType],
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterWhereClause>
      reportTypeNotEqualTo(String? reportType) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'reportType',
              lower: [],
              upper: [reportType],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'reportType',
              lower: [reportType],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'reportType',
              lower: [reportType],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'reportType',
              lower: [],
              upper: [reportType],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterWhereClause>
      businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [null],
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterWhereClause>
      businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'businessId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterWhereClause>
      businessIdEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [businessId],
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterWhereClause>
      businessIdNotEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ));
      }
    });
  }
}

extension MilkReportDataIsarQueryFilter
    on QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QFilterCondition> {
  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      averageMilkPerCowIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'averageMilkPerCow',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      averageMilkPerCowIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'averageMilkPerCow',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      averageMilkPerCowEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'averageMilkPerCow',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      averageMilkPerCowGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'averageMilkPerCow',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      averageMilkPerCowLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'averageMilkPerCow',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      averageMilkPerCowBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'averageMilkPerCow',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      averageMilkPerDayIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'averageMilkPerDay',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      averageMilkPerDayIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'averageMilkPerDay',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      averageMilkPerDayEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'averageMilkPerDay',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      averageMilkPerDayGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'averageMilkPerDay',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      averageMilkPerDayLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'averageMilkPerDay',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      averageMilkPerDayBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'averageMilkPerDay',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      businessIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      businessIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      businessIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      businessIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'businessId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      businessIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      businessIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      businessIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      businessIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'businessId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      businessIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      businessIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      createdAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      createdAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      createdAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      createdAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      createdAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      createdAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'createdAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      endDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'endDate',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      endDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'endDate',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      endDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'endDate',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      endDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'endDate',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      endDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'endDate',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      endDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'endDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      filterCriteriaIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'filterCriteria',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      filterCriteriaIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'filterCriteria',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      filterCriteriaEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      filterCriteriaGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      filterCriteriaLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      filterCriteriaBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'filterCriteria',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      filterCriteriaStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      filterCriteriaEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      filterCriteriaContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      filterCriteriaMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'filterCriteria',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      filterCriteriaIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'filterCriteria',
        value: '',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      filterCriteriaIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'filterCriteria',
        value: '',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      generatedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'generatedAt',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      generatedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'generatedAt',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      generatedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'generatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      generatedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'generatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      generatedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'generatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      generatedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'generatedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      idEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkDatesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'milkDates',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkDatesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'milkDates',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkDatesElementEqualTo(DateTime value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'milkDates',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkDatesElementGreaterThan(
    DateTime value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'milkDates',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkDatesElementLessThan(
    DateTime value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'milkDates',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkDatesElementBetween(
    DateTime lower,
    DateTime upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'milkDates',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkDatesLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'milkDates',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkDatesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'milkDates',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkDatesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'milkDates',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkDatesLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'milkDates',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkDatesLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'milkDates',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkDatesLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'milkDates',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeColorsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'milkTimeColors',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeColorsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'milkTimeColors',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeColorsElementEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'milkTimeColors',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeColorsElementGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'milkTimeColors',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeColorsElementLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'milkTimeColors',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeColorsElementBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'milkTimeColors',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeColorsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'milkTimeColors',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeColorsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'milkTimeColors',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeColorsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'milkTimeColors',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeColorsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'milkTimeColors',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeColorsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'milkTimeColors',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeColorsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'milkTimeColors',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeLabelsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'milkTimeLabels',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeLabelsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'milkTimeLabels',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeLabelsElementEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'milkTimeLabels',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeLabelsElementGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'milkTimeLabels',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeLabelsElementLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'milkTimeLabels',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeLabelsElementBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'milkTimeLabels',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeLabelsElementStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'milkTimeLabels',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeLabelsElementEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'milkTimeLabels',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeLabelsElementContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'milkTimeLabels',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeLabelsElementMatches(String pattern,
          {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'milkTimeLabels',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeLabelsElementIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'milkTimeLabels',
        value: '',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeLabelsElementIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'milkTimeLabels',
        value: '',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeLabelsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'milkTimeLabels',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeLabelsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'milkTimeLabels',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeLabelsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'milkTimeLabels',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeLabelsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'milkTimeLabels',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeLabelsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'milkTimeLabels',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeLabelsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'milkTimeLabels',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeValuesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'milkTimeValues',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeValuesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'milkTimeValues',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeValuesElementEqualTo(
    double value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'milkTimeValues',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeValuesElementGreaterThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'milkTimeValues',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeValuesElementLessThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'milkTimeValues',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeValuesElementBetween(
    double lower,
    double upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'milkTimeValues',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeValuesLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'milkTimeValues',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeValuesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'milkTimeValues',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeValuesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'milkTimeValues',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeValuesLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'milkTimeValues',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeValuesLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'milkTimeValues',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkTimeValuesLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'milkTimeValues',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkValuesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'milkValues',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkValuesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'milkValues',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkValuesElementEqualTo(
    double value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'milkValues',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkValuesElementGreaterThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'milkValues',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkValuesElementLessThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'milkValues',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkValuesElementBetween(
    double lower,
    double upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'milkValues',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkValuesLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'milkValues',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkValuesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'milkValues',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkValuesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'milkValues',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkValuesLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'milkValues',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkValuesLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'milkValues',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      milkValuesLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'milkValues',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      reportTypeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'reportType',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      reportTypeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'reportType',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      reportTypeEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      reportTypeGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      reportTypeLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      reportTypeBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'reportType',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      reportTypeStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      reportTypeEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      reportTypeContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      reportTypeMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'reportType',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      reportTypeIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'reportType',
        value: '',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      reportTypeIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'reportType',
        value: '',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      startDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'startDate',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      startDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'startDate',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      startDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'startDate',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      startDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'startDate',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      startDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'startDate',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      startDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'startDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      titleIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'title',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      titleIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'title',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      titleEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      titleGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      titleLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      titleBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'title',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      titleStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      titleEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      titleContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      titleMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'title',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      titleIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'title',
        value: '',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      titleIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'title',
        value: '',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      topProducerAmountIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'topProducerAmount',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      topProducerAmountIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'topProducerAmount',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      topProducerAmountEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'topProducerAmount',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      topProducerAmountGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'topProducerAmount',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      topProducerAmountLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'topProducerAmount',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      topProducerAmountBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'topProducerAmount',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      topProducerIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'topProducerId',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      topProducerIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'topProducerId',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      topProducerIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'topProducerId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      topProducerIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'topProducerId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      topProducerIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'topProducerId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      topProducerIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'topProducerId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      topProducerIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'topProducerId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      topProducerIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'topProducerId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      topProducerIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'topProducerId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      topProducerIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'topProducerId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      topProducerIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'topProducerId',
        value: '',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      topProducerIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'topProducerId',
        value: '',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      topProducerNameIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'topProducerName',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      topProducerNameIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'topProducerName',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      topProducerNameEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'topProducerName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      topProducerNameGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'topProducerName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      topProducerNameLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'topProducerName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      topProducerNameBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'topProducerName',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      topProducerNameStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'topProducerName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      topProducerNameEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'topProducerName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      topProducerNameContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'topProducerName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      topProducerNameMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'topProducerName',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      topProducerNameIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'topProducerName',
        value: '',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      topProducerNameIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'topProducerName',
        value: '',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      totalMilkIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'totalMilk',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      totalMilkIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'totalMilk',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      totalMilkEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'totalMilk',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      totalMilkGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'totalMilk',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      totalMilkLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'totalMilk',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      totalMilkBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'totalMilk',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      totalRecordsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'totalRecords',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      totalRecordsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'totalRecords',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      totalRecordsEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'totalRecords',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      totalRecordsGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'totalRecords',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      totalRecordsLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'totalRecords',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      totalRecordsBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'totalRecords',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      updatedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      updatedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      updatedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      updatedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      updatedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterFilterCondition>
      updatedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'updatedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension MilkReportDataIsarQueryObject
    on QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QFilterCondition> {}

extension MilkReportDataIsarQueryLinks
    on QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QFilterCondition> {}

extension MilkReportDataIsarQuerySortBy
    on QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QSortBy> {
  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      sortByAverageMilkPerCow() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'averageMilkPerCow', Sort.asc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      sortByAverageMilkPerCowDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'averageMilkPerCow', Sort.desc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      sortByAverageMilkPerDay() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'averageMilkPerDay', Sort.asc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      sortByAverageMilkPerDayDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'averageMilkPerDay', Sort.desc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      sortByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      sortByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      sortByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      sortByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      sortByEndDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'endDate', Sort.asc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      sortByEndDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'endDate', Sort.desc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      sortByFilterCriteria() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'filterCriteria', Sort.asc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      sortByFilterCriteriaDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'filterCriteria', Sort.desc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      sortByGeneratedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'generatedAt', Sort.asc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      sortByGeneratedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'generatedAt', Sort.desc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      sortByReportType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reportType', Sort.asc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      sortByReportTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reportType', Sort.desc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      sortByStartDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startDate', Sort.asc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      sortByStartDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startDate', Sort.desc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      sortByTitle() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'title', Sort.asc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      sortByTitleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'title', Sort.desc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      sortByTopProducerAmount() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'topProducerAmount', Sort.asc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      sortByTopProducerAmountDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'topProducerAmount', Sort.desc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      sortByTopProducerId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'topProducerId', Sort.asc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      sortByTopProducerIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'topProducerId', Sort.desc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      sortByTopProducerName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'topProducerName', Sort.asc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      sortByTopProducerNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'topProducerName', Sort.desc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      sortByTotalMilk() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalMilk', Sort.asc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      sortByTotalMilkDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalMilk', Sort.desc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      sortByTotalRecords() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalRecords', Sort.asc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      sortByTotalRecordsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalRecords', Sort.desc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      sortByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      sortByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }
}

extension MilkReportDataIsarQuerySortThenBy
    on QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QSortThenBy> {
  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      thenByAverageMilkPerCow() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'averageMilkPerCow', Sort.asc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      thenByAverageMilkPerCowDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'averageMilkPerCow', Sort.desc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      thenByAverageMilkPerDay() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'averageMilkPerDay', Sort.asc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      thenByAverageMilkPerDayDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'averageMilkPerDay', Sort.desc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      thenByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      thenByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      thenByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      thenByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      thenByEndDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'endDate', Sort.asc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      thenByEndDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'endDate', Sort.desc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      thenByFilterCriteria() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'filterCriteria', Sort.asc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      thenByFilterCriteriaDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'filterCriteria', Sort.desc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      thenByGeneratedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'generatedAt', Sort.asc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      thenByGeneratedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'generatedAt', Sort.desc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      thenByReportType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reportType', Sort.asc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      thenByReportTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reportType', Sort.desc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      thenByStartDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startDate', Sort.asc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      thenByStartDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startDate', Sort.desc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      thenByTitle() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'title', Sort.asc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      thenByTitleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'title', Sort.desc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      thenByTopProducerAmount() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'topProducerAmount', Sort.asc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      thenByTopProducerAmountDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'topProducerAmount', Sort.desc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      thenByTopProducerId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'topProducerId', Sort.asc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      thenByTopProducerIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'topProducerId', Sort.desc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      thenByTopProducerName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'topProducerName', Sort.asc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      thenByTopProducerNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'topProducerName', Sort.desc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      thenByTotalMilk() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalMilk', Sort.asc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      thenByTotalMilkDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalMilk', Sort.desc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      thenByTotalRecords() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalRecords', Sort.asc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      thenByTotalRecordsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalRecords', Sort.desc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      thenByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QAfterSortBy>
      thenByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }
}

extension MilkReportDataIsarQueryWhereDistinct
    on QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QDistinct> {
  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QDistinct>
      distinctByAverageMilkPerCow() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'averageMilkPerCow');
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QDistinct>
      distinctByAverageMilkPerDay() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'averageMilkPerDay');
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QDistinct>
      distinctByBusinessId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'businessId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QDistinct>
      distinctByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'createdAt');
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QDistinct>
      distinctByEndDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'endDate');
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QDistinct>
      distinctByFilterCriteria({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'filterCriteria',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QDistinct>
      distinctByGeneratedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'generatedAt');
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QDistinct>
      distinctByMilkDates() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'milkDates');
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QDistinct>
      distinctByMilkTimeColors() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'milkTimeColors');
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QDistinct>
      distinctByMilkTimeLabels() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'milkTimeLabels');
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QDistinct>
      distinctByMilkTimeValues() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'milkTimeValues');
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QDistinct>
      distinctByMilkValues() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'milkValues');
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QDistinct>
      distinctByReportType({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'reportType', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QDistinct>
      distinctByStartDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'startDate');
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QDistinct>
      distinctByTitle({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'title', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QDistinct>
      distinctByTopProducerAmount() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'topProducerAmount');
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QDistinct>
      distinctByTopProducerId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'topProducerId',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QDistinct>
      distinctByTopProducerName({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'topProducerName',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QDistinct>
      distinctByTotalMilk() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'totalMilk');
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QDistinct>
      distinctByTotalRecords() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'totalRecords');
    });
  }

  QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QDistinct>
      distinctByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'updatedAt');
    });
  }
}

extension MilkReportDataIsarQueryProperty
    on QueryBuilder<MilkReportDataIsar, MilkReportDataIsar, QQueryProperty> {
  QueryBuilder<MilkReportDataIsar, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<MilkReportDataIsar, double?, QQueryOperations>
      averageMilkPerCowProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'averageMilkPerCow');
    });
  }

  QueryBuilder<MilkReportDataIsar, double?, QQueryOperations>
      averageMilkPerDayProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'averageMilkPerDay');
    });
  }

  QueryBuilder<MilkReportDataIsar, String?, QQueryOperations>
      businessIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'businessId');
    });
  }

  QueryBuilder<MilkReportDataIsar, DateTime?, QQueryOperations>
      createdAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'createdAt');
    });
  }

  QueryBuilder<MilkReportDataIsar, DateTime?, QQueryOperations>
      endDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'endDate');
    });
  }

  QueryBuilder<MilkReportDataIsar, String?, QQueryOperations>
      filterCriteriaProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'filterCriteria');
    });
  }

  QueryBuilder<MilkReportDataIsar, DateTime?, QQueryOperations>
      generatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'generatedAt');
    });
  }

  QueryBuilder<MilkReportDataIsar, List<DateTime>?, QQueryOperations>
      milkDatesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'milkDates');
    });
  }

  QueryBuilder<MilkReportDataIsar, List<int>?, QQueryOperations>
      milkTimeColorsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'milkTimeColors');
    });
  }

  QueryBuilder<MilkReportDataIsar, List<String>?, QQueryOperations>
      milkTimeLabelsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'milkTimeLabels');
    });
  }

  QueryBuilder<MilkReportDataIsar, List<double>?, QQueryOperations>
      milkTimeValuesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'milkTimeValues');
    });
  }

  QueryBuilder<MilkReportDataIsar, List<double>?, QQueryOperations>
      milkValuesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'milkValues');
    });
  }

  QueryBuilder<MilkReportDataIsar, String?, QQueryOperations>
      reportTypeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'reportType');
    });
  }

  QueryBuilder<MilkReportDataIsar, DateTime?, QQueryOperations>
      startDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'startDate');
    });
  }

  QueryBuilder<MilkReportDataIsar, String?, QQueryOperations> titleProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'title');
    });
  }

  QueryBuilder<MilkReportDataIsar, double?, QQueryOperations>
      topProducerAmountProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'topProducerAmount');
    });
  }

  QueryBuilder<MilkReportDataIsar, String?, QQueryOperations>
      topProducerIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'topProducerId');
    });
  }

  QueryBuilder<MilkReportDataIsar, String?, QQueryOperations>
      topProducerNameProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'topProducerName');
    });
  }

  QueryBuilder<MilkReportDataIsar, double?, QQueryOperations>
      totalMilkProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'totalMilk');
    });
  }

  QueryBuilder<MilkReportDataIsar, int?, QQueryOperations>
      totalRecordsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'totalRecords');
    });
  }

  QueryBuilder<MilkReportDataIsar, DateTime?, QQueryOperations>
      updatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'updatedAt');
    });
  }
}
