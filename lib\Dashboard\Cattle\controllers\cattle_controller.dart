import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:isar/isar.dart';
import 'dart:async';
import '../models/cattle_isar.dart';

import '../../Farm Setup/models/animal_type_isar.dart';
import '../../Farm Setup/models/breed_category_isar.dart';
import '../../Farm Setup/models/currency_settings_isar.dart';
import '../services/cattle_repository.dart';
import '../services/cattle_analytics_service.dart';
import '../../Farm Setup/services/farm_setup_repository.dart';
import '../../widgets/filters/filter_data_service.dart';
import '../../widgets/filters/filters.dart';
import '../../widgets/filters/filter_widgets.dart';


/// Controller state enum
enum ControllerState { initial, loading, loaded, error, empty }

class CattleController extends ChangeNotifier {
  final CattleRepository _cattleRepository = GetIt.instance<CattleRepository>();
  final FarmSetupRepository _farmSetupRepository = GetIt.instance<FarmSetupRepository>();
  final Isar _isar = GetIt.instance<Isar>();

  // State management
  ControllerState _state = ControllerState.loading;
  String? _errorMessage;

  // Stream subscriptions for real-time updates - Separated for filtered/unfiltered data
  StreamSubscription<List<CattleIsar>>? _unfilteredStreamSubscription;
  StreamSubscription<List<CattleIsar>>? _filteredStreamSubscription;

  // Data - Critical separation: filtered data for UI, unfiltered for analytics
  List<CattleIsar> _unfilteredCattle = []; // Complete dataset for analytics calculations
  List<CattleIsar> _filteredCattle = []; // Filtered dataset for UI display
  List<AnimalTypeIsar> _animalTypes = [];
  List<BreedCategoryIsar> _breeds = [];
  CurrencySettingsIsar? _currencySettings;
  bool _hasActiveFilters = false; // Track if filters are currently applied

  // Lookup maps for O(1) access instead of O(n) searches
  Map<String, AnimalTypeIsar> _animalTypeMap = {};
  Map<String, BreedCategoryIsar> _breedMap = {};

  // Analytics result from the dedicated service - ALWAYS calculated on unfiltered data
  CattleAnalyticsResult _analyticsResult = CattleAnalyticsResult.empty;


  // Getters
  ControllerState get state => _state;
  String? get errorMessage => _errorMessage;

  /// Returns the filtered cattle list for UI display
  /// This is what the CattleRecordsTab should show
  List<CattleIsar> get cattle => List.unmodifiable(_filteredCattle);

  /// Returns the complete unfiltered cattle list for analytics
  /// This ensures analytics are always calculated on the full dataset
  List<CattleIsar> get unfilteredCattle => List.unmodifiable(_unfilteredCattle);

  List<AnimalTypeIsar> get animalTypes => List.unmodifiable(_animalTypes);
  List<BreedCategoryIsar> get breeds => List.unmodifiable(_breeds);
  String get currencySymbol => _currencySettings?.currencySymbol ?? '\$';
  String get businessId => 'default-farm-id'; // TODO: Get from farm settings

  // Main analytics object - single source of truth
  CattleAnalyticsResult get analytics => _analyticsResult;

  // Convenience getters for backward compatibility with UI
  int get totalCattle => _analyticsResult.totalCattle;
  int get maleCattle => _analyticsResult.maleCount;
  int get femaleCattle => _analyticsResult.femaleCount;
  double get averageAge => _analyticsResult.averageAge;
  int get activeCattleCount => _analyticsResult.activeCattle;
  int get soldCattleCount => _analyticsResult.soldCattle;
  Map<String, int> get cattleByGender => _analyticsResult.genderDistribution;
  Map<String, int> get cattleByType => _analyticsResult.statusDistribution;
  Map<String, int> get ageDistribution => _analyticsResult.ageDistribution;
  Map<String, int> get breedDistribution => _analyticsResult.breedDistribution;
  int get youngStockCount => _analyticsResult.ageDistribution['Young'] ?? 0;
  int get matureStockCount => _analyticsResult.ageDistribution['Mature'] ?? 0;
  int get seniorStockCount => _analyticsResult.ageDistribution['Senior'] ?? 0;
  int get calfCount => _analyticsResult.ageDistribution['Calf'] ?? 0;
  double get calfPercentage => totalCattle > 0 ? (calfCount / totalCattle) * 100 : 0.0;
  double get youngStockPercentage => totalCattle > 0 ? (youngStockCount / totalCattle) * 100 : 0.0;
  double get matureStockPercentage => totalCattle > 0 ? (matureStockCount / totalCattle) * 100 : 0.0;
  double get seniorStockPercentage => totalCattle > 0 ? (seniorStockCount / totalCattle) * 100 : 0.0;
  double get herdValue => _analyticsResult.totalValue;
  double get averageValue => totalCattle > 0 ? herdValue / totalCattle : 0.0;
  double get activeCattleValue => _analyticsResult.estimatedCurrentValue;
  // TODO: Implement soldCattleValue based on actual sale prices
  // This requires adding a salePrice field to CattleIsar model and calculating total sales revenue
  // For now, return 0.0 as we don't have sale price data yet
  double get soldCattleValue => 0.0; // Placeholder - needs salePrice field in model

  // Lookup methods with O(1) performance
  String? getAnimalTypeName(String? id) => _animalTypeMap[id]?.name;
  String? getBreedName(String? id) => _breedMap[id]?.name;
  AnimalTypeIsar? getAnimalType(String? id) => _animalTypeMap[id];
  BreedCategoryIsar? getBreed(String? id) => _breedMap[id];



  // Constructor
  CattleController() {
    _initializeStreamListener();
  }

  // Initialize and load data
  Future<void> loadData() async {
    try {
      _setState(ControllerState.loading);

      // Load supporting data - cattle data comes from Isar watch() automatically
      await _loadSupportingData();

      // Note: Cattle data and state management is now handled by _handleStreamUpdate
      // via the Isar watch() subscription, which fires immediately on initialization
    } catch (e, stackTrace) {
      debugPrint('Error loading cattle data: $e\n$stackTrace');
      _setError('Failed to load cattle data: ${e.toString()}');
    }
  }

  /// Load supporting data
  Future<void> _loadSupportingData() async {
    await Future.wait([
      _loadAnimalTypes(),
      _loadBreeds(),
      _loadCurrencySettings(),
    ]);
  }



  Future<void> refresh() async {
    try {
      // Don't set loading state here - let the screen handle it
      // Reload fresh data
      await Future.wait([
        _loadAnimalTypes(),
        _loadBreeds(),
        _loadCurrencySettings(),
      ]);

      // Clear filter cache to ensure fresh data
      FilterDataService.instance.clearCache();

      // Force analytics recalculation on unfiltered data
      if (_unfilteredCattle.isNotEmpty) {
        _calculateAnalytics();
      }

      // Notify listeners of the refresh
      notifyListeners();
    } catch (e, stackTrace) {
      debugPrint('Error refreshing cattle data: $e\n$stackTrace');
      throw Exception('Failed to refresh cattle data: ${e.toString()}');
    }
  }

  /// Initialize stream listeners for real-time updates using Isar's native watch
  /// Critical: Separate streams for unfiltered (analytics) and filtered (UI) data
  void _initializeStreamListener() {
    // Primary stream: Unfiltered data for analytics calculations
    // This stream NEVER changes and always provides the complete dataset
    _unfilteredStreamSubscription = _isar.cattleIsars.where()
        .watch(fireImmediately: true)
        .listen((unfilteredList) {
      _handleUnfilteredDataUpdate(unfilteredList);
    });

    // Initially, filtered data equals unfiltered data (no filters applied)
    _filteredCattle = _unfilteredCattle;
    _hasActiveFilters = false;
  }

  /// Apply filters at the database level for ultimate scalability
  /// Critical Fix: This method now creates a separate filtered stream without affecting analytics
  void applyFilters(FilterController? filters) async {
    // Cancel existing filtered subscription (but keep unfiltered stream for analytics)
    _filteredStreamSubscription?.cancel();

    // Check if filters are active
    final hasFilters = filters != null &&
        (filters.searchQuery.isNotEmpty ||
         filters.globalFilters.isNotEmpty ||
         filters.startDate != null ||
         filters.endDate != null);

    _hasActiveFilters = hasFilters;

    if (hasFilters) {
      // Build filtered query for UI display
      final filteredQuery = _buildFilteredQuery(filters);

      // Create separate stream for filtered data
      _filteredStreamSubscription = filteredQuery.watch(fireImmediately: true)
          .listen((filteredList) {
        _handleFilteredDataUpdate(filteredList);
      });
    } else {
      // No filters: filtered data equals unfiltered data
      _filteredCattle = List.from(_unfilteredCattle);
      _setState(ControllerState.loaded);
      notifyListeners();
    }
  }



  /// Handle unfiltered data updates - Used for analytics calculations
  /// Critical: This method ONLY updates analytics and unfiltered data
  void _handleUnfilteredDataUpdate(List<CattleIsar> unfilteredList) async {
    try {
      // Update the complete unfiltered dataset
      _unfilteredCattle = unfilteredList;

      // ALWAYS calculate analytics on the complete unfiltered dataset
      // This ensures analytics remain accurate regardless of applied filters
      if (_unfilteredCattle.isEmpty) {
        _analyticsResult = CattleAnalyticsResult.empty;
      } else {
        _calculateAnalytics();
      }

      // If no filters are active, update filtered data to match unfiltered
      if (!_hasActiveFilters) {
        _filteredCattle = List.from(_unfilteredCattle);
      }

      _setState(ControllerState.loaded);
      FilterDataService.instance.clearCache();
      notifyListeners();
    } catch (e) {
      debugPrint('Error handling unfiltered data update: $e');
      _setState(ControllerState.error);
      _errorMessage = 'Failed to update unfiltered data: $e';
      notifyListeners();
    }
  }

  /// Handle filtered data updates - Used for UI display only
  /// Critical: This method NEVER affects analytics calculations
  void _handleFilteredDataUpdate(List<CattleIsar> filteredList) async {
    try {
      // Update only the filtered dataset for UI display
      _filteredCattle = filteredList;

      // Do NOT recalculate analytics here - they're handled by unfiltered stream
      // This ensures analytics always reflect the complete dataset

      FilterDataService.instance.clearCache();
      notifyListeners();
    } catch (e) {
      debugPrint('Error handling filtered data update: $e');
      // Don't change state to error for filtered data issues
      notifyListeners();
    }
  }



  Future<void> _loadAnimalTypes() async {
    try {
      final animalTypesData = await _farmSetupRepository.getAllAnimalTypes();
      _animalTypes = animalTypesData;
      // Create lookup map for O(1) access
      _animalTypeMap = {
        for (var type in _animalTypes)
          if (type.businessId != null) type.businessId!: type
      };
    } catch (e) {
      debugPrint('Error loading animal types: $e');
      throw Exception('Failed to load animal types');
    }
  }

  Future<void> _loadBreeds() async {
    try {
      final breedsData = await _farmSetupRepository.getAllBreedCategories();
      _breeds = breedsData;
      // Create lookup map for O(1) access
      _breedMap = {
        for (var breed in _breeds)
          if (breed.businessId != null) breed.businessId!: breed
      };
    } catch (e) {
      debugPrint('Error loading breeds: $e');
      throw Exception('Failed to load breeds');
    }
  }

  Future<void> _loadCurrencySettings() async {
    try {
      final currencyData = await _farmSetupRepository.getCurrencySettings();
      _currencySettings = currencyData;
    } catch (e) {
      debugPrint('Error loading currency settings: $e');
      // Don't throw exception for currency settings, use default
      _currencySettings = null;
    }
  }

  /// Build filtered Isar query based on FilterController state
  /// This method dynamically constructs database queries for optimal performance
  /// Complete implementation supporting all filter types for ultimate scalability
  dynamic _buildFilteredQuery(FilterController filters) {
    // Start with base query and build the chain using dynamic typing
    dynamic currentQuery = _isar.cattleIsars.where();

    // Apply search filter at database level - searches name, tagId, and businessId
    if (filters.searchQuery.isNotEmpty) {
      final searchTerm = filters.searchQuery.toLowerCase();
      currentQuery = currentQuery.filter().group((q) => q
          .nameContains(searchTerm, caseSensitive: false)
          .or()
          .tagIdContains(searchTerm, caseSensitive: false)
          .or()
          .businessIdContains(searchTerm, caseSensitive: false));
    }

    // Apply date range filters at database level
    if (filters.startDate != null) {
      currentQuery = currentQuery.filter().createdAtGreaterThan(filters.startDate!);
    }
    if (filters.endDate != null) {
      // Add one day to make end date inclusive
      final inclusiveEndDate = filters.endDate!.add(const Duration(days: 1));
      currentQuery = currentQuery.filter().createdAtLessThan(inclusiveEndDate);
    }

    // Apply global filters at database level
    final globalFilters = filters.globalFilters;

    // Animal Type filter - using stable filter key constants
    if (globalFilters.containsKey(AppFilterWidget.animalTypeKey) &&
        globalFilters[AppFilterWidget.animalTypeKey] != null &&
        globalFilters[AppFilterWidget.animalTypeKey] != 'All') {
      currentQuery = currentQuery.filter().animalTypeIdEqualTo(globalFilters[AppFilterWidget.animalTypeKey]);
    }

    // Breed filter - using stable filter key constants
    if (globalFilters.containsKey(AppFilterWidget.breedKey) &&
        globalFilters[AppFilterWidget.breedKey] != null &&
        globalFilters[AppFilterWidget.breedKey] != 'All') {
      currentQuery = currentQuery.filter().breedIdEqualTo(globalFilters[AppFilterWidget.breedKey]);
    }

    // Gender filter - using stable filter key constants
    if (globalFilters.containsKey(AppFilterWidget.genderKey) &&
        globalFilters[AppFilterWidget.genderKey] != null &&
        globalFilters[AppFilterWidget.genderKey] != 'All') {
      currentQuery = currentQuery.filter().genderEqualTo(globalFilters[AppFilterWidget.genderKey]);
    }

    // Cattle-specific filter (individual cattle selection) - using stable filter key constants
    if (globalFilters.containsKey(AppFilterWidget.cattleKey) &&
        globalFilters[AppFilterWidget.cattleKey] != null &&
        globalFilters[AppFilterWidget.cattleKey] != 'All') {
      currentQuery = currentQuery.filter().businessIdEqualTo(globalFilters[AppFilterWidget.cattleKey]);
    }

    // Note: Age group filtering is handled client-side in the UI layer
    // because it requires complex date calculations that can't be efficiently
    // translated to database queries

    // Apply sorting at database level for optimal performance
    if (filters.sortBy != null) {
      switch (filters.sortBy) {
        case 'name':
          currentQuery = filters.isAscending ? currentQuery.sortByName() : currentQuery.sortByNameDesc();
          break;
        case 'createdAt':
          currentQuery = filters.isAscending ? currentQuery.sortByCreatedAt() : currentQuery.sortByCreatedAtDesc();
          break;
        case 'tagId':
          currentQuery = filters.isAscending ? currentQuery.sortByTagId() : currentQuery.sortByTagIdDesc();
          break;
        case 'gender':
          currentQuery = filters.isAscending ? currentQuery.sortByGender() : currentQuery.sortByGenderDesc();
          break;
        case 'purchaseDate':
          currentQuery = filters.isAscending ? currentQuery.sortByPurchaseDate() : currentQuery.sortByPurchaseDateDesc();
          break;
        default:
          currentQuery = currentQuery.sortByCreatedAt(); // Default sort by creation date
      }
    } else {
      currentQuery = currentQuery.sortByCreatedAt(); // Default sort when no sort specified
    }

    return currentQuery;
  }

  /// Calculate analytics using the dedicated service
  /// Critical: ALWAYS uses unfiltered data to ensure accurate analytics
  void _calculateAnalytics() {
    _analyticsResult = CattleAnalyticsService.calculate(
      _unfilteredCattle, // Use unfiltered data for accurate analytics
      _breedMap,
      _animalTypeMap,
    );
  }





  // CRUD Methods - Single Source of Truth pattern
  // These methods only update the database, stream handles UI updates

  /// Add new cattle - only updates database, stream handles UI update
  Future<void> addCattle(CattleIsar cattle) async {
    await _cattleRepository.saveCattle(cattle);
    // Stream will handle the UI update automatically
  }

  /// Update cattle - only updates database, stream handles UI update
  Future<void> updateCattle(CattleIsar updatedCattle) async {
    await _cattleRepository.saveCattle(updatedCattle);
    // Stream will handle the UI update automatically
  }

  /// Delete cattle - only updates database, stream handles UI update
  Future<void> deleteCattle(String businessId) async {
    // Find the cattle by businessId first, then delete by Isar ID
    final cattle = _unfilteredCattle.firstWhere(
      (c) => c.businessId == businessId,
      orElse: () => throw Exception('Cattle not found'),
    );
    await _cattleRepository.deleteCattle(cattle.id);
    // Stream will handle the UI update automatically
  }

  // Utility methods

  // State management helpers
  void _setState(ControllerState newState) {
    _state = newState;
    _errorMessage = null;
    notifyListeners();
  }

  void _setError(String error) {
    _state = ControllerState.error;
    _errorMessage = error;
    notifyListeners();
  }



  @override
  void dispose() {
    _unfilteredStreamSubscription?.cancel();
    _filteredStreamSubscription?.cancel();
    super.dispose();
  }
}
