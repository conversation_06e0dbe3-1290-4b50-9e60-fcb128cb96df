import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:isar/isar.dart';
import 'package:async/async.dart';
import '../models/transaction_isar.dart';
import '../models/category_isar.dart';
import '../services/transactions_repository.dart';
import '../services/transaction_analytics_service.dart';
import '../../Cattle/controllers/cattle_controller.dart'; // Import for ControllerState enum

/// Filter state object for decoupled filter management
/// Following the established FilterController pattern for consistency
class TransactionFilterState {
  final String? searchQuery;
  final DateTime? startDate;
  final DateTime? endDate;
  final String? category;
  final String? paymentMethod;
  final String? transactionType;

  const TransactionFilterState({
    this.searchQuery,
    this.startDate,
    this.endDate,
    this.category,
    this.paymentMethod,
    this.transactionType,
  });

  /// Check if any filters are active
  bool get hasActiveFilters =>
      (searchQuery?.isNotEmpty ?? false) ||
      startDate != null ||
      endDate != null ||
      (category?.isNotEmpty ?? false) ||
      (paymentMethod?.isNotEmpty ?? false) ||
      (transactionType?.isNotEmpty ?? false);

  /// Create a copy with updated values
  TransactionFilterState copyWith({
    String? searchQuery,
    DateTime? startDate,
    DateTime? endDate,
    String? category,
    String? paymentMethod,
    String? transactionType,
  }) {
    return TransactionFilterState(
      searchQuery: searchQuery ?? this.searchQuery,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      category: category ?? this.category,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      transactionType: transactionType ?? this.transactionType,
    );
  }

  /// Clear all filters
  static const TransactionFilterState empty = TransactionFilterState();
}

/// Reactive controller for the main transactions screen using Dual-Stream Pattern
/// Following the cattle module template: separate unfiltered and filtered streams
/// Unfiltered stream feeds analytics, filtered stream feeds UI
class TransactionController extends ChangeNotifier {
  // Repositories
  final TransactionsRepository _transactionsRepository = GetIt.instance<TransactionsRepository>();
  final Isar _isar = GetIt.instance<Isar>();

  // State management
  ControllerState _state = ControllerState.loading;
  String? _errorMessage;

  // Stream subscriptions for real-time updates - Separated for filtered/unfiltered data
  StreamSubscription? _unfilteredStreamSubscription;
  StreamSubscription? _filteredStreamSubscription;

  // Data - Critical separation: filtered data for UI, unfiltered for analytics
  List<TransactionIsar> _unfilteredTransactions = []; // Complete dataset for analytics calculations
  List<CategoryIsar> _unfilteredCategories = []; // Complete categories dataset

  List<TransactionIsar> _filteredTransactions = []; // Filtered dataset for UI display
  bool _hasActiveFilters = false; // Track if filters are currently applied

  // Analytics result from the dedicated service - ALWAYS calculated on unfiltered data
  TransactionAnalyticsResult _analyticsResult = TransactionAnalyticsResult.empty;

  // Lookup maps for O(1) access
  Map<String, CategoryIsar> _categoryMap = {};

  // Filter state management - decoupled from UI
  TransactionFilterState _currentFilters = TransactionFilterState.empty;
  // Getters
  ControllerState get state => _state;
  String? get errorMessage => _errorMessage;

  /// Returns the filtered transactions for UI display
  /// This is what the TransactionRecordsTab should show
  List<TransactionIsar> get transactions => List.unmodifiable(_filteredTransactions);

  /// Returns the complete unfiltered transactions for analytics
  /// This ensures analytics are always calculated on the full dataset
  List<TransactionIsar> get unfilteredTransactions => List.unmodifiable(_unfilteredTransactions);

  /// Returns the complete unfiltered categories for analytics
  List<CategoryIsar> get unfilteredCategories => List.unmodifiable(_unfilteredCategories);

  // Main analytics object - single source of truth
  TransactionAnalyticsResult get analytics => _analyticsResult;

  // Lookup maps for O(1) access
  Map<String, CategoryIsar> get categoryMap => Map.unmodifiable(_categoryMap);

  // Filter state access
  TransactionFilterState get currentFilters => _currentFilters;

  // Convenience getters for backward compatibility with UI
  int get totalTransactions => _analyticsResult.totalTransactions;
  int get incomeTransactions => _analyticsResult.incomeTransactions;
  int get expenseTransactions => _analyticsResult.expenseTransactions;
  double get totalIncome => _analyticsResult.totalIncome;
  double get totalExpenses => _analyticsResult.totalExpenses;
  double get netBalance => _analyticsResult.netBalance;
  double get averageIncome => _analyticsResult.averageIncome;
  double get averageExpense => _analyticsResult.averageExpense;
  double get averageTransactionAmount => _analyticsResult.averageTransactionAmount;
  Map<String, double> get categoryBreakdown => _analyticsResult.categoryBreakdown;
  Map<String, double> get paymentMethodBreakdown => _analyticsResult.paymentMethodBreakdown;
  String get topIncomeCategory => _analyticsResult.topIncomeCategory;
  String get topExpenseCategory => _analyticsResult.topExpenseCategory;
  String get mostUsedPaymentMethod => _analyticsResult.mostUsedPaymentMethod;
  double get incomeToExpenseRatio => _analyticsResult.incomeToExpenseRatio;
  double get savingsRate => _analyticsResult.savingsRate;
  bool get hasData => _unfilteredTransactions.isNotEmpty;

  // Constructor
  TransactionController() {
    _initializeStreamListeners();
  }

  /// Initialize stream listeners for real-time updates using Dual-Stream Pattern
  /// Critical: Separate streams for unfiltered (analytics) and filtered (UI) data
  void _initializeStreamListeners() {
    // Primary stream: Unfiltered data for analytics calculations
    // This stream NEVER changes and always provides the complete dataset
    final combinedStream = StreamZip([
      _transactionsRepository.watchAllTransactions(),
      _transactionsRepository.watchAllCategories(),
    ]);

    _unfilteredStreamSubscription = combinedStream.listen((data) {
      _handleUnfilteredDataUpdate(
        data[0] as List<TransactionIsar>,
        data[1] as List<CategoryIsar>,
      );
    });

    // Initially, filtered data equals unfiltered data (no filters applied)
    _filteredTransactions = _unfilteredTransactions;
    _hasActiveFilters = false;
  }

  /// Handle unfiltered data updates - Used for analytics calculations
  /// Critical: This method ONLY updates analytics and unfiltered data
  void _handleUnfilteredDataUpdate(
    List<TransactionIsar> unfilteredTransactions,
    List<CategoryIsar> unfilteredCategories,
  ) async {
    try {
      // Update the complete unfiltered datasets
      _unfilteredTransactions = unfilteredTransactions;
      _unfilteredCategories = unfilteredCategories;

      // Create lookup map for O(1) access
      _categoryMap = {
        for (var category in _unfilteredCategories)
          if (category.businessId != null) category.businessId!: category
      };

      // ALWAYS calculate analytics on the complete unfiltered dataset
      // This ensures analytics remain accurate regardless of applied filters
      if (_unfilteredTransactions.isEmpty) {
        _analyticsResult = TransactionAnalyticsResult.empty;
      } else {
        _calculateAnalytics();
      }

      // If no filters are active, update filtered data to match unfiltered
      if (!_hasActiveFilters) {
        _filteredTransactions = List.from(_unfilteredTransactions);
      }

      _setState(ControllerState.loaded);
      notifyListeners();
    } catch (e) {
      debugPrint('Error handling unfiltered data update: $e');
      _setState(ControllerState.error);
      _errorMessage = 'Failed to update unfiltered data: $e';
      notifyListeners();
    }
  }

  /// Calculate analytics using the dedicated service
  /// Critical: ALWAYS uses unfiltered data to ensure accurate analytics
  void _calculateAnalytics() {
    _analyticsResult = TransactionAnalyticsService.calculate(
      _unfilteredTransactions, // Use unfiltered data for accurate analytics
      _unfilteredCategories,
    );
  }

  /// Apply filters using the FilterController pattern for decoupled filter management
  /// Critical Fix: This method now creates a separate filtered stream without affecting analytics
  void applyFilters(TransactionFilterState filterState) async {
    // Update current filter state
    _currentFilters = filterState;

    // Cancel existing filtered subscription (but keep unfiltered stream for analytics)
    _filteredStreamSubscription?.cancel();

    // Check if filters are active
    _hasActiveFilters = filterState.hasActiveFilters;

    if (_hasActiveFilters) {
      // Build filtered query for UI display
      final filteredQuery = _buildFilteredQuery(filterState);

      // Create separate stream for filtered data
      _filteredStreamSubscription = filteredQuery.watch(fireImmediately: true)
          .listen((filteredList) {
        _handleFilteredDataUpdate(filteredList);
      });
    } else {
      // No filters: filtered data equals unfiltered data
      _filteredTransactions = List.from(_unfilteredTransactions);
      notifyListeners();
    }
  }

  /// Convenience method for backward compatibility and simple filter updates
  void updateFilters({
    String? searchQuery,
    DateTime? startDate,
    DateTime? endDate,
    String? category,
    String? paymentMethod,
    String? transactionType,
  }) {
    final newFilters = _currentFilters.copyWith(
      searchQuery: searchQuery,
      startDate: startDate,
      endDate: endDate,
      category: category,
      paymentMethod: paymentMethod,
      transactionType: transactionType,
    );
    applyFilters(newFilters);
  }

  /// Clear all filters
  void clearFilters() {
    applyFilters(TransactionFilterState.empty);
  }

  /// Handle filtered data updates - Used for UI display only
  /// Critical: This method NEVER affects analytics calculations
  void _handleFilteredDataUpdate(List<TransactionIsar> filteredList) {
    try {
      // Update only the filtered dataset for UI display
      _filteredTransactions = filteredList;

      // Do NOT recalculate analytics here - they're handled by unfiltered stream
      // This ensures analytics always reflect the complete dataset

      notifyListeners();
    } catch (e) {
      debugPrint('Error handling filtered data update: $e');
      // Don't change state to error for filtered data issues
      notifyListeners();
    }
  }

  /// Build filtered Isar query based on FilterState object
  /// This method dynamically constructs database queries for optimal performance
  dynamic _buildFilteredQuery(TransactionFilterState filterState) {
    // Start with base query and build the chain using dynamic typing
    dynamic currentQuery = _isar.transactionIsars.where();

    // Apply search filter at database level
    if (filterState.searchQuery?.isNotEmpty ?? false) {
      final searchTerm = filterState.searchQuery!.toLowerCase();
      currentQuery = currentQuery.filter().group((q) => q
          .descriptionContains(searchTerm, caseSensitive: false)
          .or()
          .categoryContains(searchTerm, caseSensitive: false));
    }

    // Apply date range filters at database level
    if (filterState.startDate != null) {
      currentQuery = currentQuery.filter().dateGreaterThan(filterState.startDate!);
    }
    if (filterState.endDate != null) {
      // Add one day to make end date inclusive
      final inclusiveEndDate = filterState.endDate!.add(const Duration(days: 1));
      currentQuery = currentQuery.filter().dateLessThan(inclusiveEndDate);
    }

    // Apply category filter
    if (filterState.category?.isNotEmpty ?? false) {
      currentQuery = currentQuery.filter().categoryEqualTo(filterState.category);
    }

    // Apply payment method filter
    if (filterState.paymentMethod?.isNotEmpty ?? false) {
      currentQuery = currentQuery.filter().paymentMethodEqualTo(filterState.paymentMethod);
    }

    // Apply transaction type filter
    if (filterState.transactionType?.isNotEmpty ?? false) {
      currentQuery = currentQuery.filter().typeEqualTo(filterState.transactionType);
    }

    // Apply sorting at database level for optimal performance
    currentQuery = currentQuery.sortByDateDesc(); // Default sort by date (newest first)

    return currentQuery;
  }

  // CRUD Methods - Single Source of Truth pattern
  // These methods only update the database, stream handles UI updates
  // Exceptions bubble up naturally to be handled by higher-level error handlers

  /// Add new transaction - only updates database, stream handles UI update
  Future<void> addTransaction(TransactionIsar transaction) async {
    await _transactionsRepository.saveTransaction(transaction);
    // Stream will handle the UI update automatically
  }

  /// Update transaction - only updates database, stream handles UI update
  Future<void> updateTransaction(TransactionIsar updatedTransaction) async {
    await _transactionsRepository.saveTransaction(updatedTransaction);
    // Stream will handle the UI update automatically
  }

  /// Delete transaction - only updates database, stream handles UI update
  Future<void> deleteTransaction(int transactionId) async {
    await _transactionsRepository.deleteTransaction(transactionId);
    // Stream will handle the UI update automatically
  }

  /// Add new category - only updates database, stream handles UI update
  Future<void> addCategory(CategoryIsar category) async {
    await _transactionsRepository.saveCategory(category);
    // Stream will handle the UI update automatically
  }

  // Helper methods
  CategoryIsar? getCategory(String? categoryId) {
    if (categoryId == null) return null;
    return _categoryMap[categoryId];
  }

  String getCategoryName(String? categoryId) {
    final category = getCategory(categoryId);
    return category?.name ?? 'Unknown Category';
  }

  // State management helpers
  void _setState(ControllerState newState) {
    _state = newState;
    _errorMessage = null;
    notifyListeners();
  }

  void _setError(String error) {
    _state = ControllerState.error;
    _errorMessage = error;
    notifyListeners();
  }

  @override
  void dispose() {
    _unfilteredStreamSubscription?.cancel();
    _filteredStreamSubscription?.cancel();
    super.dispose();
  }
}
