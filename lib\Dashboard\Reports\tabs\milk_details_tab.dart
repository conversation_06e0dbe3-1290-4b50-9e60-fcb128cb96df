import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/milk_report_data_isar.dart';

class MilkDetailsTab extends StatefulWidget {
  final MilkReportDataIsar reportData;

  const MilkDetailsTab({
    Key? key,
    required this.reportData,
  }) : super(key: key);

  @override
  MilkDetailsTabState createState() => MilkDetailsTabState();
}

class MilkDetailsTabState extends State<MilkDetailsTab> {
  // Remove unused fields or use them in the implementation
  // String? _sortColumn;
  // bool _sortAscending = true;
  final currencyFormat = NumberFormat.currency(locale: 'en_US', symbol: '\$');
  final quantityFormat = NumberFormat('##0.00');

  @override
  Widget build(BuildContext context) {
    // Since milkRecords isn't directly available, we'll use the data available in MilkReportDataIsar
    if (widget.reportData.totalRecords == null || widget.reportData.totalRecords == 0) {
      return const Center(
        child: Text('No milk records found for the selected period'),
      );
    }

    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSummarySection(),
            const SizedBox(height: 24),
            const Text(
              'Detailed Records',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'This section would typically display a table of individual milk records. '
                  'However, the detailed records are not available in the current implementation of MilkReportDataIsar.\n\n'
                  'Summary: ${widget.reportData.totalRecords} records, ${widget.reportData.totalMilk?.toStringAsFixed(1) ?? "0.0"} total liters',
                  style: const TextStyle(fontSize: 16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummarySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Summary',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            DataTable(
              columns: widget.reportData.getTableColumns(),
              rows: widget.reportData.getTableRows(),
            ),
          ],
        ),
      ),
    );
  }
}
