/// Type-safe data models for weight insights
/// Replaces Map<String, dynamic> usage for compile-time safety

/// Analytics summary data for the analytics tab (module-wide)
class AnalyticsSummary {
  final int totalRecords;
  final int totalCattle;
  final double averageWeight;
  final double totalWeight;
  final DateTime? earliestRecord;
  final DateTime? latestRecord;

  const AnalyticsSummary({
    required this.totalRecords,
    required this.totalCattle,
    required this.averageWeight,
    required this.totalWeight,
    this.earliestRecord,
    this.latestRecord,
  });

  static const AnalyticsSummary empty = AnalyticsSummary(
    totalRecords: 0,
    totalCattle: 0,
    averageWeight: 0.0,
    totalWeight: 0.0,
  );
}

/// Individual cattle analytics summary for detail screens
class IndividualCattleAnalytics {
  final double currentWeight;
  final double averageWeight;
  final double weightGain;
  final double averageDailyGain;
  final int totalRecords;
  final String dateRange;
  final String weightTrend;

  const IndividualCattleAnalytics({
    required this.currentWeight,
    required this.averageWeight,
    required this.weightGain,
    required this.averageDailyGain,
    required this.totalRecords,
    required this.dateRange,
    required this.weightTrend,
  });

  static const IndividualCattleAnalytics empty = IndividualCattleAnalytics(
    currentWeight: 0.0,
    averageWeight: 0.0,
    weightGain: 0.0,
    averageDailyGain: 0.0,
    totalRecords: 0,
    dateRange: 'No data',
    weightTrend: 'Unknown',
  );
}

/// Performance insights data
class PerformanceInsights {
  final String topPerformerName;
  final String topPerformerId;
  final double topPerformerGainRate;
  final double averageDailyGain;
  final String growthTrend;
  final String growthTrendDescription;
  final int totalGainingCattle;
  final int totalLosingCattle;

  const PerformanceInsights({
    required this.topPerformerName,
    required this.topPerformerId,
    required this.topPerformerGainRate,
    required this.averageDailyGain,
    required this.growthTrend,
    required this.growthTrendDescription,
    required this.totalGainingCattle,
    required this.totalLosingCattle,
  });

  static const PerformanceInsights empty = PerformanceInsights(
    topPerformerName: 'No data',
    topPerformerId: '',
    topPerformerGainRate: 0.0,
    averageDailyGain: 0.0,
    growthTrend: 'Stable',
    growthTrendDescription: 'No trend data available',
    totalGainingCattle: 0,
    totalLosingCattle: 0,
  );
}

/// Health insights data
class HealthInsights {
  final double averageBodyCondition;
  final String consistencyRating;
  final String consistencyDescription;
  final int healthAlerts;
  final List<String> alertReasons;
  final int underweightCattle;
  final int overweightCattle;
  final int normalWeightCattle;

  const HealthInsights({
    required this.averageBodyCondition,
    required this.consistencyRating,
    required this.consistencyDescription,
    required this.healthAlerts,
    required this.alertReasons,
    required this.underweightCattle,
    required this.overweightCattle,
    required this.normalWeightCattle,
  });

  static const HealthInsights empty = HealthInsights(
    averageBodyCondition: 0.0,
    consistencyRating: 'Good',
    consistencyDescription: 'No data available',
    healthAlerts: 0,
    alertReasons: [],
    underweightCattle: 0,
    overweightCattle: 0,
    normalWeightCattle: 0,
  );
}

/// Trend insights data
class TrendInsights {
  final double monthlyChange;
  final String trendDirection;
  final String seasonalPattern;
  final double predictedNextMonth;

  const TrendInsights({
    required this.monthlyChange,
    required this.trendDirection,
    required this.seasonalPattern,
    required this.predictedNextMonth,
  });

  static const TrendInsights empty = TrendInsights(
    monthlyChange: 0.0,
    trendDirection: 'Stable',
    seasonalPattern: 'Analysis pending',
    predictedNextMonth: 0.0,
  );
}

/// Recommendation data
class WeightRecommendation {
  final String title;
  final String description;
  final String priority; // 'High', 'Medium', 'Low'
  final String category; // 'Nutrition', 'Health', 'Management', etc.
  final List<String> actionItems;

  const WeightRecommendation({
    required this.title,
    required this.description,
    required this.priority,
    required this.category,
    required this.actionItems,
  });
}

/// Complete insights data combining all insight types
class WeightInsightsData {
  final PerformanceInsights performance;
  final HealthInsights health;
  final TrendInsights trends;
  final List<WeightRecommendation> recommendations;
  final DateTime lastUpdated;

  const WeightInsightsData({
    required this.performance,
    required this.health,
    required this.trends,
    required this.recommendations,
    required this.lastUpdated,
  });

  static WeightInsightsData get empty => WeightInsightsData(
    performance: PerformanceInsights.empty,
    health: HealthInsights.empty,
    trends: TrendInsights.empty,
    recommendations: const [],
    lastUpdated: DateTime.now(),
  );
}
