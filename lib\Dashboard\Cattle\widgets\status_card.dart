import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'info_row.dart';

/// Enhanced StatusCard for displaying pregnancy and delivery status
class StatusCard extends StatelessWidget {
  // Core properties
  final String title;
  final IconData titleIcon;
  final Color baseColor;
  final Widget? trailing;
  final VoidCallback? onTap;

  // Status properties
  final String? statusText;
  final String? guidance;
  final IconData? statusIcon;

  // Pregnancy specific properties
  final bool isPregnant;
  final DateTime? dueDate;
  final DateTime? startDate;

  // Additional content
  final List<Widget>? additionalContent;
  final List<Widget>? footerContent;
  final Widget? customContent;

  // Action buttons
  final List<Widget>? actionButtons;

  const StatusCard({
    super.key,
    this.title = 'Status',
    this.titleIcon = Icons.info,
    this.baseColor = Colors.blue,
    this.trailing,
    this.onTap,
    this.statusText,
    this.guidance,
    this.statusIcon,
    this.isPregnant = false,
    this.dueDate,
    this.startDate,
    this.additionalContent,
    this.footerContent,
    this.customContent,
    this.actionButtons,
  });

  /// Factory constructor for pregnancy status card
  factory StatusCard.pregnancy({
    required bool isPregnant,
    DateTime? dueDate,
    DateTime? startDate,
    String title = 'Current Pregnancy',
    Color baseColor = Colors.purple,
    String? customStatusText,
    String? customGuidance,
    IconData? customStatusIcon,
    Widget? trailing,
    VoidCallback? onTap,
    List<Widget>? additionalContent,
    List<Widget>? actionButtons,
  }) {
    return StatusCard(
      title: title,
      titleIcon: isPregnant ? Icons.pregnant_woman : Icons.female,
      baseColor: baseColor,
      trailing: trailing,
      onTap: onTap,
      isPregnant: isPregnant,
      dueDate: dueDate,
      startDate: startDate,
      statusText: customStatusText,
      guidance: customGuidance,
      statusIcon: customStatusIcon,
      additionalContent: additionalContent,
      actionButtons: actionButtons,
    );
  }

  /// Factory constructor for delivery status card
  factory StatusCard.delivery({
    required bool isPregnant,
    DateTime? dueDate,
    DateTime? startDate,
    String title = 'Delivery Status',
    Color baseColor = const Color(0xFF5D1049), // Deep Burgundy - standout color
    String? customStatusText,
    String? customGuidance,
    IconData? customStatusIcon,
    Widget? trailing,
    VoidCallback? onTap,
    List<Widget>? additionalContent,
    List<Widget>? actionButtons,
  }) {
    return StatusCard(
      title: title,
      titleIcon: Icons.child_care,
      baseColor: baseColor,
      trailing: trailing,
      onTap: onTap,
      isPregnant: isPregnant,
      dueDate: dueDate,
      startDate: startDate,
      statusText: customStatusText,
      guidance: customGuidance,
      statusIcon: customStatusIcon,
      additionalContent: additionalContent,
      actionButtons: actionButtons,
    );
  }

  @override
  Widget build(BuildContext context) {
    if (customContent != null) {
      return Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: customContent,
      );
    }

    // For pregnancy status
    if (isPregnant && dueDate != null) {
      return _buildPregnantWithDateCard(context);
    } else if (isPregnant) {
      return _buildPregnantNoDateCard(context);
    } else {
      return _buildGenericStatusCard(context);
    }
  }

  Widget _buildCardHeader(BuildContext context,
      {String? headerTitle, IconData? headerIcon, Color? headerColor}) {
    final title = headerTitle ?? this.title;
    final icon = headerIcon ?? titleIcon;
    final color = headerColor ?? baseColor;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withAlpha(26),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 20,
                backgroundColor: color.withAlpha(51),
                child: Icon(
                  icon,
                  color: color,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          if (trailing != null) trailing!,
        ],
      ),
    );
  }

  Widget _buildGenericStatusCard(BuildContext context) {
    final displayStatusText =
        statusText ?? (isPregnant ? 'Pregnant' : 'Not Pregnant');
    final displayGuidance = guidance ??
        (isPregnant ? 'Monitor regularly' : 'Ready for breeding when in heat.');
    final displayStatusIcon =
        statusIcon ?? (isPregnant ? Icons.pregnant_woman : Icons.female);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCardHeader(context),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                InfoRow(
                  icon: displayStatusIcon,
                  label: 'Status',
                  value: displayStatusText,
                  color: baseColor,
                ),
                const SizedBox(height: 16),
                InfoRow(
                  icon: Icons.info,
                  label: 'Message',
                  value: displayGuidance,
                  color: baseColor,
                ),
                if (additionalContent != null) ...[
                  const SizedBox(height: 16),
                  ...additionalContent!,
                ],
                if (actionButtons != null && actionButtons!.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  ...actionButtons!,
                ],
                if (footerContent != null) ...[
                  const SizedBox(height: 16),
                  const Divider(),
                  const SizedBox(height: 8),
                  ...footerContent!,
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPregnantNoDateCard(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCardHeader(context),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                InfoRow(
                  icon: Icons.calendar_today,
                  label: 'Expected Date',
                  value: 'Not Set',
                  color: baseColor,
                  isHighlighted: true,
                ),
                const SizedBox(height: 16),
                InfoRow(
                  icon: Icons.pending_actions,
                  label: 'Action Required',
                  value: 'Set expected calving date.',
                  color: baseColor,
                ),
                if (additionalContent != null) ...[
                  const SizedBox(height: 16),
                  ...additionalContent!,
                ],
                if (actionButtons != null && actionButtons!.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  ...actionButtons!,
                ],
                if (footerContent != null) ...[
                  const SizedBox(height: 16),
                  const Divider(),
                  const SizedBox(height: 8),
                  ...footerContent!,
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPregnantWithDateCard(BuildContext context) {
    final now = DateTime.now();
    final daysUntilDelivery = dueDate!.difference(now).inDays;

    // Log the due date for debugging
    debugPrint('StatusCard: Due date: $dueDate, Days until delivery: $daysUntilDelivery');

    // Status-specific color and text
    Color statusColor;
    String displayStatusText;
    String displayGuidance;
    IconData displayStatusIcon;

    // Different colors for different information elements
    const Color startDateColor = Color(0xFF00796B); // Teal
    const Color dueDateColor = Color(0xFF7B1FA2); // Purple
    const Color guidanceColor = Color(0xFF0277BD); // Light Blue

    if (daysUntilDelivery > 30) {
      statusColor = const Color(0xFF006064); // Deep Cyan (Unique)
      displayStatusText = statusText ?? 'Normal Progress';
      displayGuidance = guidance ?? 'Regular check-ups recommended';
      displayStatusIcon = statusIcon ?? Icons.child_care;
    } else if (daysUntilDelivery > 7) {
      statusColor = const Color(0xFF0D47A1); // Darker Blue (Unique)
      displayStatusText = statusText ?? 'Due Soon';
      displayGuidance = guidance ?? 'Prepare for delivery. Monitor closely.';
      displayStatusIcon = statusIcon ?? Icons.medical_services;
    } else if (daysUntilDelivery >= 0) {
      statusColor = const Color(0xFF4A148C); // Deeper Purple (Unique)
      displayStatusText = statusText ?? 'Due Now';
      displayGuidance =
          guidance ?? 'Immediate attention required. Ready for delivery.';
      displayStatusIcon = statusIcon ?? Icons.warning_amber;
    } else {
      statusColor = const Color(0xFFB71C1C); // Deeper Red (Unique)
      displayStatusText = statusText ?? 'Overdue';
      displayGuidance = guidance ??
          'Past expected date. Veterinary consultation recommended.';
      displayStatusIcon = statusIcon ?? Icons.error_outline;
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCardHeader(context, headerColor: statusColor),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                if (startDate != null) ...[
                  InfoRow(
                    icon: Icons.date_range,
                    label: 'Start Date',
                    value: DateFormat('MMM dd, yyyy').format(startDate!),
                    color: startDateColor,
                  ),
                  const SizedBox(height: 8),
                ],
                InfoRow(
                  icon: Icons.event,
                  label: 'Due Date',
                  value: DateFormat('MMM dd, yyyy').format(dueDate!),
                  color: dueDateColor,
                  isHighlighted: false,
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 12),
                        decoration: BoxDecoration(
                          color: statusColor.withAlpha(40),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: statusColor.withAlpha(150),
                            width: 2,
                          ),
                        ),
                        child: Column(
                          children: [
                            Text(
                              displayStatusText.toUpperCase(),
                              style: TextStyle(
                                color: statusColor,
                                fontWeight: FontWeight.bold,
                                fontSize: 18,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            if (daysUntilDelivery >= 0) ...[
                              const SizedBox(height: 6),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.timer,
                                      color: statusColor, size: 16),
                                  const SizedBox(width: 4),
                                  Text(
                                    '$daysUntilDelivery DAYS REMAINING',
                                    style: TextStyle(
                                      color: statusColor,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 14,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            ] else ...[
                              const SizedBox(height: 6),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.warning,
                                      color: statusColor, size: 16),
                                  const SizedBox(width: 4),
                                  Text(
                                    '${daysUntilDelivery.abs()} DAYS OVERDUE',
                                    style: TextStyle(
                                      color: statusColor,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 14,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                InfoRow(
                  icon: displayStatusIcon,
                  label: 'Guidance',
                  value: displayGuidance,
                  color: guidanceColor,
                  isMultiline: true,
                ),
                if (additionalContent != null) ...[
                  const SizedBox(height: 16),
                  ...additionalContent!,
                ],
                if (actionButtons != null && actionButtons!.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  ...actionButtons!,
                ],
                if (footerContent != null) ...[
                  const SizedBox(height: 16),
                  const Divider(),
                  const SizedBox(height: 8),
                  ...footerContent!,
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}
