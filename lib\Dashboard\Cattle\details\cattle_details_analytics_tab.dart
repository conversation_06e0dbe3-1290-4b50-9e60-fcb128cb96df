import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../controllers/cattle_details_controller.dart';
import '../services/cattle_details_analytics_service.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_constants.dart';
import '../../../constants/app_layout.dart';
import '../../widgets/index.dart';

/// Data class for analytics info cards to provide type safety
class AnalyticsCardData {
  final String title;
  final String value;
  final String subtitle;
  final IconData icon;
  final Color color;
  final String? insight;

  const AnalyticsCardData({
    required this.title,
    required this.value,
    required this.subtitle,
    required this.icon,
    required this.color,
    this.insight,
  });
}

class CattleDetailsAnalyticsTab extends StatelessWidget {
  const CattleDetailsAnalyticsTab({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<CattleDetailsController>(
      builder: (context, controller, child) {
        final cattle = controller.cattle;
        final analytics = controller.individualAnalytics;

        if (cattle == null) {
          return const Center(child: Text('No cattle data available'));
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(kPaddingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildProductionMetrics(analytics.production),
              const SizedBox(height: kSpacingLarge),
              _buildBreedingMetrics(analytics.breeding),
              const SizedBox(height: kSpacingLarge),
              _buildHealthMetrics(analytics.health),
              const SizedBox(height: kSpacingLarge),
              _buildFinancialMetrics(analytics.financial),
            ],
          ),
        );
      },
    );
  }

  /// Build production metrics section
  Widget _buildProductionMetrics(ProductionMetrics production) {
    final cardData = _buildProductionCards(production);
    return _buildAnalyticsSection(
      title: 'Production Analytics',
      subtitle: 'Milk production performance and trends',
      icon: Icons.water_drop,
      headerColor: AppColors.cattleKpiSection,
      cardData: cardData,
    );
  }

  /// Build production metric cards with type safety
  List<AnalyticsCardData> _buildProductionCards(ProductionMetrics production) {
    return [
      AnalyticsCardData(
        title: 'Lifetime Yield',
        value: '${production.lifetimeMilkYield.toStringAsFixed(1)}L',
        subtitle: 'Total milk produced',
        icon: Icons.water_drop,
        color: AppColors.cattleKpiColors[0],
        insight: production.productionInsight,
      ),
      AnalyticsCardData(
        title: 'Daily Average',
        value: '${production.averageDailyYield.toStringAsFixed(1)}L',
        subtitle: 'Average per day',
        icon: Icons.trending_up,
        color: AppColors.cattleKpiColors[1],
        insight: production.dailyYieldInsight,
      ),
      AnalyticsCardData(
        title: 'Peak Daily',
        value: '${production.peakDailyYield.toStringAsFixed(1)}L',
        subtitle: 'Best single day',
        icon: Icons.star,
        color: AppColors.cattleKpiColors[2],
        insight: 'Peak performance record',
      ),
      AnalyticsCardData(
        title: 'Milking Days',
        value: '${production.totalMilkingDays}',
        subtitle: 'Total days milked',
        icon: Icons.calendar_today,
        color: AppColors.cattleKpiColors[3],
        insight: production.milkingDaysInsight,
      ),
      AnalyticsCardData(
        title: 'Current Lactation',
        value: '${production.currentLactationYield.toStringAsFixed(1)}L',
        subtitle: 'This lactation period',
        icon: Icons.timeline,
        color: AppColors.cattleKpiColors[4],
        insight: production.currentLactationInsight,
      ),
      AnalyticsCardData(
        title: 'Yield Trend',
        value: '${production.yieldTrend >= 0 ? '+' : ''}${production.yieldTrend.toStringAsFixed(1)}%',
        subtitle: 'Last 30 days vs previous',
        icon: production.yieldTrend >= 0 ? Icons.trending_up : Icons.trending_down,
        color: production.yieldTrend >= 0 ? AppColors.success : AppColors.error,
        insight: production.yieldTrendInsight,
      ),
    ];
  }

  /// Build breeding metrics section
  Widget _buildBreedingMetrics(BreedingMetrics breeding) {
    final cardData = _buildBreedingCards(breeding);
    return _buildAnalyticsSection(
      title: 'Breeding Analytics',
      subtitle: 'Reproductive performance and history',
      icon: Icons.favorite,
      headerColor: AppColors.cattleAgeDemographics,
      cardData: cardData,
    );
  }

  /// Build breeding metric cards with type safety
  List<AnalyticsCardData> _buildBreedingCards(BreedingMetrics breeding) {
    return [
      AnalyticsCardData(
        title: 'Total Calves',
        value: '${breeding.totalCalves}',
        subtitle: 'Offspring produced',
        icon: Icons.child_care,
        color: AppColors.cattleKpiColors[0],
        insight: breeding.calvesInsight,
      ),
      AnalyticsCardData(
        title: 'Calving Interval',
        value: breeding.averageCalvingInterval > 0
            ? '${breeding.averageCalvingInterval.toStringAsFixed(0)} days'
            : 'N/A',
        subtitle: 'Average between calvings',
        icon: Icons.schedule,
        color: AppColors.cattleKpiColors[1],
        insight: breeding.calvingIntervalInsight,
      ),
      AnalyticsCardData(
        title: 'Days Open',
        value: '${breeding.daysOpen}',
        subtitle: 'Since last calving',
        icon: Icons.timer,
        color: AppColors.cattleKpiColors[2],
        insight: breeding.daysOpenInsight,
      ),
      AnalyticsCardData(
        title: 'Conception Rate',
        value: '${breeding.conceptionRate.toStringAsFixed(1)}%',
        subtitle: 'Breeding success rate',
        icon: Icons.check_circle,
        color: AppColors.cattleKpiColors[3],
        insight: breeding.conceptionRateInsight,
      ),
      AnalyticsCardData(
        title: 'Breeding Attempts',
        value: '${breeding.totalBreedingAttempts}',
        subtitle: 'Total attempts',
        icon: Icons.repeat,
        color: AppColors.cattleKpiColors[4],
        insight: breeding.breedingAttemptsInsight,
      ),
      AnalyticsCardData(
        title: 'Current Status',
        value: breeding.isCurrentlyPregnant ? 'Pregnant' : 'Open',
        subtitle: 'Pregnancy status',
        icon: breeding.isCurrentlyPregnant ? Icons.pregnant_woman : Icons.circle_outlined,
        color: breeding.isCurrentlyPregnant ? AppColors.success : AppColors.cattleKpiColors[5],
        insight: breeding.pregnancyStatusInsight,
      ),
    ];
  }

  /// Build health metrics section
  Widget _buildHealthMetrics(HealthMetrics health) {
    final cardData = _buildHealthCards(health);
    return _buildAnalyticsSection(
      title: 'Health Analytics',
      subtitle: 'Health performance and medical history',
      icon: Icons.health_and_safety,
      headerColor: AppColors.cattleFinancialOverview,
      cardData: cardData,
    );
  }

  /// Build health metric cards with type safety
  List<AnalyticsCardData> _buildHealthCards(HealthMetrics health) {
    return [
      AnalyticsCardData(
        title: 'Health Score',
        value: '${health.healthScore}/100',
        subtitle: 'Overall health rating',
        icon: Icons.favorite,
        color: health.healthScoreColor,
        insight: health.healthScoreInsight,
      ),
      AnalyticsCardData(
        title: 'Total Treatments',
        value: '${health.totalTreatments}',
        subtitle: 'Medical interventions',
        icon: Icons.medical_services,
        color: AppColors.cattleKpiColors[1],
        insight: health.treatmentsInsight,
      ),
      AnalyticsCardData(
        title: 'Treatment Frequency',
        value: '${health.treatmentFrequency.toStringAsFixed(1)}/year',
        subtitle: 'Treatments per year',
        icon: Icons.schedule,
        color: AppColors.cattleKpiColors[2],
        insight: health.treatmentFrequencyInsight,
      ),
      AnalyticsCardData(
        title: 'Days Since Treatment',
        value: '${health.daysSinceLastTreatment}',
        subtitle: 'Last medical intervention',
        icon: Icons.timer,
        color: AppColors.cattleKpiColors[3],
        insight: health.daysSinceTreatmentInsight,
      ),
      AnalyticsCardData(
        title: 'Vaccinations',
        value: '${health.totalVaccinations}',
        subtitle: 'Total vaccines given',
        icon: Icons.vaccines,
        color: AppColors.cattleKpiColors[4],
        insight: health.vaccinationsInsight,
      ),
      AnalyticsCardData(
        title: 'Recovery Rate',
        value: '${health.recoveryRate.toStringAsFixed(1)}%',
        subtitle: 'Treatment success rate',
        icon: Icons.healing,
        color: AppColors.success,
        insight: health.recoveryRateInsight,
      ),
    ];
  }

  /// Build financial metrics section
  Widget _buildFinancialMetrics(FinancialMetrics financial) {
    final cardData = _buildFinancialCards(financial);
    return _buildAnalyticsSection(
      title: 'Financial Analytics',
      subtitle: 'Investment performance and profitability',
      icon: Icons.attach_money,
      headerColor: AppColors.cattleHerdComposition,
      cardData: cardData,
    );
  }

  /// Build financial metric cards with type safety
  List<AnalyticsCardData> _buildFinancialCards(FinancialMetrics financial) {
    return [
      AnalyticsCardData(
        title: 'Total Investment',
        value: financial.formattedTotalInvestment,
        subtitle: 'Initial + ongoing costs',
        icon: Icons.shopping_cart,
        color: AppColors.cattleKpiColors[0],
        insight: financial.investmentInsight,
      ),
      AnalyticsCardData(
        title: 'Current Value',
        value: financial.formattedCurrentValue,
        subtitle: 'Estimated market value',
        icon: Icons.trending_up,
        color: AppColors.cattleKpiColors[1],
        insight: financial.currentValueInsight,
      ),
      AnalyticsCardData(
        title: 'Total Revenue',
        value: financial.formattedTotalRevenue,
        subtitle: 'Income generated',
        icon: Icons.account_balance_wallet,
        color: AppColors.success,
        insight: financial.revenueInsight,
      ),
      AnalyticsCardData(
        title: 'Net Profit',
        value: financial.formattedNetProfit,
        subtitle: 'Revenue - costs',
        icon: financial.netProfit >= 0 ? Icons.trending_up : Icons.trending_down,
        color: financial.netProfit >= 0 ? AppColors.success : AppColors.error,
        insight: financial.profitInsight,
      ),
      AnalyticsCardData(
        title: 'ROI',
        value: '${financial.roi.toStringAsFixed(1)}%',
        subtitle: 'Return on investment',
        icon: Icons.percent,
        color: financial.roi >= 0 ? AppColors.success : AppColors.error,
        insight: financial.roiInsight,
      ),
      AnalyticsCardData(
        title: 'Monthly Cost',
        value: financial.formattedMonthlyCost,
        subtitle: 'Average monthly expense',
        icon: Icons.calendar_month,
        color: AppColors.cattleKpiColors[5],
        insight: financial.monthlyCostInsight,
      ),
    ];
  }

  /// Build analytics section with consistent styling
  Widget _buildAnalyticsSection({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color headerColor,
    required List<AnalyticsCardData> cardData,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        UniversalFormField.sectionHeader(
          title: title,
          icon: icon,
          color: headerColor,
          subtitle: subtitle,
          filled: true,
          padding: EdgeInsets.zero,
        ),
        const SizedBox(height: kSpacingMedium),
        ResponsiveGrid.cards(
          children: cardData.map((data) => _buildMetricCard(data)).toList(),
        ),
      ],
    );
  }

  /// Build individual metric card from typed data
  Widget _buildMetricCard(AnalyticsCardData data) {
    return UniversalInfoCard(
      title: data.title,
      value: data.value,
      subtitle: data.subtitle,
      icon: data.icon,
      color: data.color,
      insight: data.insight,
    );
  }

}
