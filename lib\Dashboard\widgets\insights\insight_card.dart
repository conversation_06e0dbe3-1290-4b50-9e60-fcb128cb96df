import 'package:flutter/material.dart';

/// Insight Card Widget
/// 
/// A reusable card widget for displaying insights and recommendations.
/// Provides consistent styling for insight components across modules.
class InsightCard extends StatelessWidget {
  final String title;
  final String description;
  final IconData? icon;
  final Color? color;
  final VoidCallback? onTap;
  final Widget? action;
  final InsightType type;

  const InsightCard({
    Key? key,
    required this.title,
    required this.description,
    this.icon,
    this.color,
    this.onTap,
    this.action,
    this.type = InsightType.info,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final insightColor = _getInsightColor(context);
    final insightIcon = _getInsightIcon();

    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Icon(
                    icon ?? insightIcon,
                    color: insightColor,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      title,
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: insightColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Description
              Text(
                description,
                style: theme.textTheme.bodyMedium,
              ),
              
              // Action
              if (action != null) ...[
                const SizedBox(height: 16),
                action!,
              ],
            ],
          ),
        ),
      ),
    );
  }

  Color _getInsightColor(BuildContext context) {
    if (color != null) return color!;
    
    switch (type) {
      case InsightType.success:
        return Colors.green;
      case InsightType.warning:
        return Colors.orange;
      case InsightType.error:
        return Colors.red;
      case InsightType.info:
      default:
        return Theme.of(context).primaryColor;
    }
  }

  IconData _getInsightIcon() {
    switch (type) {
      case InsightType.success:
        return Icons.check_circle_outline;
      case InsightType.warning:
        return Icons.warning_outlined;
      case InsightType.error:
        return Icons.error_outline;
      case InsightType.info:
      default:
        return Icons.lightbulb_outline;
    }
  }
}

/// Types of insights for different styling
enum InsightType {
  info,
  success,
  warning,
  error,
}
