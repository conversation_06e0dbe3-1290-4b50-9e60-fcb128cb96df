import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AppSettings {
  bool darkMode;
  bool notifications;
  String language;
  int dataRefreshInterval;
  
  AppSettings({
    this.darkMode = false,
    this.notifications = true,
    this.language = 'English',
    this.dataRefreshInterval = 15,
  });
  
  Map<String, dynamic> toJson() {
    return {
      'darkMode': darkMode,
      'notifications': notifications,
      'language': language,
      'dataRefreshInterval': dataRefreshInterval,
    };
  }
  
  factory AppSettings.fromJson(Map<String, dynamic> json) {
    return AppSettings(
      darkMode: json['darkMode'] ?? false,
      notifications: json['notifications'] ?? true,
      language: json['language'] ?? 'English',
      dataRefreshInterval: json['dataRefreshInterval'] ?? 15,
    );
  }
}

class SettingsService extends ChangeNotifier {
  static final SettingsService _instance = SettingsService._internal();
  
  factory SettingsService() => _instance;
  
  SettingsService._internal();
  
  late SharedPreferences _prefs;
  late AppSettings _settings;
  bool _initialized = false;
  
  // Getters
  bool get darkMode => _settings.darkMode;
  bool get notifications => _settings.notifications;
  String get language => _settings.language;
  int get dataRefreshInterval => _settings.dataRefreshInterval;
  bool get isInitialized => _initialized;
  
  // Initialize settings
  Future<void> init() async {
    if (_initialized) return;
    
    _prefs = await SharedPreferences.getInstance();
    _loadSettings();
    _initialized = true;
  }
  
  // Load settings from SharedPreferences
  void _loadSettings() {
    final darkMode = _prefs.getBool('darkMode') ?? false;
    final notifications = _prefs.getBool('notifications') ?? true;
    final language = _prefs.getString('language') ?? 'English';
    final dataRefreshInterval = _prefs.getInt('dataRefreshInterval') ?? 15;
    
    _settings = AppSettings(
      darkMode: darkMode,
      notifications: notifications,
      language: language,
      dataRefreshInterval: dataRefreshInterval,
    );
  }
  
  // Save settings to SharedPreferences
  Future<void> _saveSettings() async {
    await _prefs.setBool('darkMode', _settings.darkMode);
    await _prefs.setBool('notifications', _settings.notifications);
    await _prefs.setString('language', _settings.language);
    await _prefs.setInt('dataRefreshInterval', _settings.dataRefreshInterval);
    notifyListeners();
  }
  
  // Update dark mode setting
  Future<void> setDarkMode(bool value) async {
    _settings.darkMode = value;
    await _saveSettings();
  }
  
  // Update notifications setting
  Future<void> setNotifications(bool value) async {
    _settings.notifications = value;
    await _saveSettings();
  }
  
  // Update language setting
  Future<void> setLanguage(String value) async {
    _settings.language = value;
    await _saveSettings();
  }
  
  // Update data refresh interval setting
  Future<void> setDataRefreshInterval(int value) async {
    _settings.dataRefreshInterval = value;
    await _saveSettings();
  }
  
  // Clear cache
  Future<void> clearCache() async {
    // This is a placeholder for actual cache clearing logic
    // In a real app, you might clear specific SharedPreferences keys or database tables
    
    // For demonstration purposes, we'll just log the action
    debugPrint('Cache cleared');
    
    // Return a Future that completes after a short delay to simulate work
    return Future.delayed(const Duration(milliseconds: 500));
  }
}
