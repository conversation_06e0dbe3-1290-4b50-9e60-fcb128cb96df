import 'package:flutter/material.dart';
import '../controllers/events_controller.dart';
import '../models/event_isar.dart';
import '../../widgets/index.dart';
import '../dialogs/event_form_dialog.dart';
import '../../../constants/app_colors.dart';

/// Events Records Tab - Shows list of all events with filtering and search
/// Follows the cattle records tab pattern exactly
class EventsRecordsTab extends StatefulWidget {
  final EventsController controller;

  const EventsRecordsTab({
    super.key,
    required this.controller,
  });

  @override
  State<EventsRecordsTab> createState() => _EventsRecordsTabState();
}

class _EventsRecordsTabState extends State<EventsRecordsTab> {
  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: widget.controller,
      builder: (context, child) {
        return Column(
          children: [
            // Search Bar (simplified)
            Padding(
              padding: const EdgeInsets.all(16),
              child: TextField(
                decoration: const InputDecoration(
                  hintText: 'Search events...',
                  prefixIcon: Icon(Icons.search),
                  border: OutlineInputBorder(),
                ),
                onChanged: (value) {
                  // Handle search
                },
              ),
            ),

            // Records List
            Expanded(
              child: _buildRecordsList(context),
            ),
          ],
        );
      },
    );
  }

  Widget _buildRecordsList(BuildContext context) {
    final events = widget.controller.allEvents;

    if (events.isEmpty) {
      return const Center(
        child: Text('No Events Found'),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: events.length,
      itemBuilder: (context, index) {
        final event = events[index];
        return UniversalRecordCard(
          title: event.title ?? 'Untitled Event',
          subtitle: event.description ?? '',
          date: event.eventDate,
          onTap: () => _showEventDetails(context, event),
          onEdit: () => _editEvent(context, event),
          onDelete: () => _deleteEvent(context, event),
      },
    );
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'No date';
    return '${date.day}/${date.month}/${date.year}';
  }

  String _getStatusText(EventIsar event) {
    // Add status logic based on your event model
    return 'Active'; // Placeholder
  }

  void _showEventDetails(BuildContext context, EventIsar event) {
    // Navigate to event details
  }

  void _editEvent(BuildContext context, EventIsar event) {
    showDialog(
      context: context,
      builder: (context) => EventFormDialog(
        event: event,
        onSave: (updatedEvent) {
          widget.controller.updateEvent(updatedEvent);
        },
      ),
    );
  }

  void _deleteEvent(BuildContext context, EventIsar event) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Event'),
        content: Text('Are you sure you want to delete "${event.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              widget.controller.deleteEvent(event.id);
              Navigator.of(context).pop();
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog(BuildContext context) {
    // Show filter dialog
  }

  void _showSortDialog(BuildContext context) {
    // Show sort dialog
  }
}
