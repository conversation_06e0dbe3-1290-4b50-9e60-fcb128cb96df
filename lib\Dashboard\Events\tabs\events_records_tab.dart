import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../controllers/events_controller.dart';
import '../models/event_isar.dart';
import '../../widgets/universal_state_builder.dart';
import '../../widgets/universal_empty_state.dart';
import '../../widgets/universal_record_card.dart';
import '../../widgets/filters/universal_filter_bar.dart';
import '../dialogs/event_form_dialog.dart';

/// Events Records Tab - Shows filterable list of all events
/// Follows the universal records tab pattern with filtering and CRUD operations
class EventsRecordsTab extends StatelessWidget {
  const EventsRecordsTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<EventsController>(
      builder: (context, controller, child) {
        return UniversalStateBuilder(
          state: controller.state,
          errorMessage: controller.errorMessage,
          onRetry: () {
            // Stream handles refresh automatically
          },
          child: _buildRecordsContent(context, controller),
        );
      },
    );
  }

  Widget _buildRecordsContent(BuildContext context, EventsController controller) {
    return Column(
      children: [
        // Filter Bar
        UniversalFilterBar(
          searchQuery: controller.currentFilters.searchQuery,
          onSearchChanged: (query) {
            controller.applyFilters(controller.currentFilters.copyWith(searchQuery: query));
          },
          onFilterPressed: () => _showFilterDialog(context, controller),
          onSortPressed: () => _showSortDialog(context, controller),
          hasActiveFilters: controller.hasActiveFilters,
          onClearFilters: () {
            controller.clearFilters();
          },
        ),

        // Records List
        Expanded(
          child: controller.events.isEmpty
              ? const UniversalEmptyState(
                  icon: Icons.event_note,
                  title: 'No Events Found',
                  subtitle: 'Add your first event to get started',
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: controller.events.length,
                  itemBuilder: (context, index) {
                    final event = controller.events[index];
                    return _buildEventCard(context, event, controller);
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildEventCard(BuildContext context, EventIsar event, EventsController controller) {
    return UniversalRecordCard(
      title: event.title ?? 'Untitled Event',
      subtitle: event.description ?? '',
      date: event.scheduledDate,
      status: event.status ?? 'pending',
      statusColor: _getStatusColor(event.status),
      icon: _getEventIcon(event.eventType),
      onTap: () => _showEventDetails(context, event, controller),
      onEdit: () => _showEditEventDialog(context, event, controller),
      onDelete: () => _showDeleteConfirmation(context, event, controller),
      additionalInfo: [
        if (event.cattleId != null)
          'Cattle: ${controller.getCattleName(event.cattleId)}',
        if (event.priority != null)
          'Priority: ${event.priority}',
        if (event.location != null)
          'Location: ${event.location}',
      ],
    );
  }

  Color _getStatusColor(String? status) {
    switch (status?.toLowerCase()) {
      case 'completed':
        return Colors.green;
      case 'pending':
        return Colors.orange;
      case 'overdue':
        return Colors.red;
      case 'cancelled':
        return Colors.grey;
      default:
        return Colors.blue;
    }
  }

  IconData _getEventIcon(String? eventType) {
    switch (eventType?.toLowerCase()) {
      case 'vaccination':
        return Icons.vaccines;
      case 'breeding':
        return Icons.favorite;
      case 'health_check':
        return Icons.health_and_safety;
      case 'feeding':
        return Icons.restaurant;
      case 'milking':
        return Icons.water_drop;
      default:
        return Icons.event;
    }
  }

  void _showEventDetails(BuildContext context, EventIsar event, EventsController controller) {
    // Navigate to event details screen
    // Implementation depends on your navigation setup
  }

  void _showEditEventDialog(BuildContext context, EventIsar event, EventsController controller) {
    showDialog(
      context: context,
      builder: (context) => EventFormDialog(
        event: event,
        cattle: controller.cattle,
        eventTypes: controller.eventTypes,
        onSave: (updatedEvent) async {
          await controller.updateEvent(updatedEvent);
        },
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, EventIsar event, EventsController controller) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Event'),
        content: Text('Are you sure you want to delete "${event.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await controller.deleteEvent(event.id);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog(BuildContext context, EventsController controller) {
    // Show filter dialog - implementation depends on your filter requirements
  }

  void _showSortDialog(BuildContext context, EventsController controller) {
    // Show sort dialog - implementation depends on your sort requirements
  }
}
