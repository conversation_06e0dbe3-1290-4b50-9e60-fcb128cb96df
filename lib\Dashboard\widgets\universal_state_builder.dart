import 'package:flutter/material.dart';
import 'mixins/universal_screen_state.dart';
import 'state_indicators/universal_state_indicators.dart';

/// Universal State Builder Widget
/// 
/// A wrapper widget that handles different screen states consistently across the app.
/// This widget automatically shows loading, error, or empty states based on the provided state.
class UniversalStateBuilder extends StatelessWidget {
  final ScreenState state;
  final Widget child;
  final String? errorMessage;
  final VoidCallback? onRetry;
  final Widget? loadingWidget;
  final Widget? errorWidget;
  final Widget? emptyWidget;
  final Color? moduleColor;

  const UniversalStateBuilder({
    super.key,
    required this.state,
    required this.child,
    this.errorMessage,
    this.onRetry,
    this.loadingWidget,
    this.errorWidget,
    this.emptyWidget,
    this.moduleColor,
  });

  @override
  Widget build(BuildContext context) {
    switch (state) {
      case ScreenState.initial:
      case ScreenState.loading:
        return loadingWidget ?? 
          UniversalLoadingIndicator(color: moduleColor);
      
      case ScreenState.error:
        return errorWidget ?? 
          UniversalErrorIndicator(
            title: 'Error',
            message: errorMessage ?? 'An error occurred',
            onRetry: onRetry,
            color: moduleColor,
          );
      
      case ScreenState.empty:
        return emptyWidget ??
          const Center(
            child: Text(
              'No data available',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          );
      
      case ScreenState.loaded:
      case ScreenState.refreshing:
        return child;
    }
  }
}
