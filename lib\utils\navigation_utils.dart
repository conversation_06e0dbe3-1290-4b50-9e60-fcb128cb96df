import 'package:flutter/material.dart';
import '../Dashboard/Cattle/details/cattle_details_screen.dart';
import 'package:logging/logging.dart';
import 'package:get_it/get_it.dart';
import '../Dashboard/Cattle/services/cattle_repository.dart';
import '../Dashboard/Farm Setup/services/farm_setup_repository.dart';
import '../Dashboard/Farm Setup/models/animal_type_isar.dart';

class SmoothPageRoute<T> extends MaterialPageRoute<T> {
  SmoothPageRoute({
    required WidgetBuilder builder,
    RouteSettings? settings,
  }) : super(builder: builder, settings: settings);

  @override
  Widget buildTransitions(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(1.0, 0.0),
        end: Offset.zero,
      ).animate(
        CurvedAnimation(
          parent: animation,
          curve: Curves.easeInOut,
        ),
      ),
      child: child,
    );
  }
}

Future<void> navigateToCattleDetails(
  BuildContext context,
  String tagId,
) async {
  final logger = Logger('NavigationUtils');

  try {
    // Get handlers using GetIt
    final cattleRepository = GetIt.instance<CattleRepository>();
    final farmSetupRepository = GetIt.instance<FarmSetupRepository>();

    // Get the cattle details
    final cattle = await cattleRepository.getCattleByTagId(tagId);

    // Check if the context is still valid
    if (!context.mounted) return;

    if (cattle == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Cattle not found'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Get the breed data - we're not using this but keeping the code in case we need it later
    /*
    BreedCategoryIsar? breed;
    if (cattle.breedId != null) {
      final breeds = await farmSetupHandler.getAllBreedCategories();
      breed = breeds.firstWhere(
        (b) => b.businessId == cattle.breedId,
        orElse: () => BreedCategoryIsar(),
      );
    }
    */

    // Get the animal type data
    AnimalTypeIsar? animalType;
    if (cattle.animalTypeId != null) {
      final animalTypes = await farmSetupRepository.getAllAnimalTypes();
      animalType = animalTypes.firstWhere(
        (t) => t.businessId == cattle.animalTypeId,
        orElse: () => AnimalTypeIsar(),
      );
    }

    if (animalType == null) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Animal type not found'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    if (context.mounted) {
      Navigator.push(
        context,
        SmoothPageRoute(
          builder: (context) => CattleDetailsScreen(
            existingCattle: cattle,
            businessId: cattle.businessId ?? '',
            onCattleUpdated: (updatedCattle) async {
              try {
                // Update the database using the cattle handler
                await cattleRepository.updateCattle(updatedCattle);

                // Optionally refresh the previous screen's data when navigating back
                if (context.mounted) {
                  Navigator.of(context).pop();
                }
              } catch (e) {
                logger.severe('Error updating cattle: $e');
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Failed to update cattle: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
          ),
        ),
      );
    }
  } catch (e) {
    logger.severe('Error navigating to cattle details: $e');
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('An error occurred: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
