import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import '../models/farm_isar.dart';

class FarmFormDialog extends StatefulWidget {
  final FarmIsar? farm;

  const FarmFormDialog({Key? key, this.farm}) : super(key: key);

  @override
  State<FarmFormDialog> createState() => _FarmFormDialogState();
}

class _FarmFormDialogState extends State<FarmFormDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _locationController;
  late TextEditingController _addressController;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.farm?.name ?? '');
    _locationController = TextEditingController(text: widget.farm?.location ?? '');
    _addressController = TextEditingController(text: widget.farm?.address ?? '');
  }

  @override
  void dispose() {
    _nameController.dispose();
    _locationController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.farm == null ? 'Add New Farm' : 'Edit Farm'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(labelText: 'Farm Name'),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a farm name';
                  }
                  return null;
                },
              ),
              TextFormField(
                controller: _locationController,
                decoration: const InputDecoration(labelText: 'Location'),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a location';
                  }
                  return null;
                },
              ),
              TextFormField(
                controller: _addressController,
                decoration: const InputDecoration(labelText: 'Address'),
                maxLines: 3,
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              FarmIsar farm;
              
              if (widget.farm != null) {
                farm = FarmIsar()
                  ..id = widget.farm!.id
                  ..farmBusinessId = widget.farm!.farmBusinessId
                  ..name = _nameController.text
                  ..location = _locationController.text
                  ..address = _addressController.text
                  ..farmType = widget.farm!.farmType
                  ..cattleCount = widget.farm!.cattleCount
                  ..capacity = widget.farm!.capacity
                  ..ownerName = widget.farm!.ownerName
                  ..ownerContact = widget.farm!.ownerContact
                  ..ownerEmail = widget.farm!.ownerEmail
                  ..latitude = widget.farm!.latitude
                  ..longitude = widget.farm!.longitude
                  ..createdAt = widget.farm!.createdAt
                  ..updatedAt = DateTime.now();
              } else {
                farm = FarmIsar()
                  ..farmBusinessId = const Uuid().v4()
                  ..name = _nameController.text
                  ..location = _locationController.text
                  ..address = _addressController.text
                  ..createdAt = DateTime.now()
                  ..updatedAt = DateTime.now();
              }
              
              Navigator.pop(context, farm);
            }
          },
          child: const Text('Save'),
        ),
      ],
    );
  }
} 