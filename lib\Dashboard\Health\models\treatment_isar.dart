import 'package:isar/isar.dart';

part 'treatment_isar.g.dart';

@Collection()
class TreatmentIsar {
  Id id = Isar.autoIncrement;
  
  String? cattleId;
  String? treatment;
  String? condition;
  String? notes;
  DateTime? date;
  String? followUpDate;
  String? cost;
  String? veterinarian;
  String? outcome;
  String? businessId;
  DateTime? createdAt;
  DateTime? updatedAt;
  bool? isArchived;
  String? status;
  String? dosage;

  TreatmentIsar({
    this.cattleId,
    this.treatment,
    this.condition,
    this.notes,
    this.date,
    this.followUpDate,
    this.cost,
    this.veterinarian,
    this.outcome,
    this.businessId,
    this.createdAt,
    this.updatedAt,
    this.isArchived = false,
    this.status,
    this.dosage,
  });
} 