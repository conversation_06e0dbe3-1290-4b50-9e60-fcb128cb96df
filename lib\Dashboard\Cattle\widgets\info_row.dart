import 'package:flutter/material.dart';

/// A reusable widget for displaying a labeled piece of information with an icon
class InfoRow extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;
  final Color color;
  final bool isMultiline;
  final bool isStatus;
  final bool isHighlighted;
  final VoidCallback? onTap;
  final bool compact;

  const InfoRow({
    super.key,
    required this.icon,
    required this.label,
    required this.value,
    this.color = Colors.blue,
    this.isMultiline = false,
    this.isStatus = false,
    this.isHighlighted = false,
    this.onTap,
    this.compact = false, // Default to non-compact mode now
  });

  // Helper method to get color based on status
  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return Colors.purple;
      case 'confirmed':
        return Colors.green;
      case 'pending':
        return Colors.blue;
      case 'failed':
      case 'abortion':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  @override
  Widget build(BuildContext context) {
    final statusColor = isStatus ? _getStatusColor(value) : color;

    // For multiline content, use a vertical layout to allow full width
    if (isMultiline) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Label row
          Row(
            children: [
              Icon(
                icon,
                size: 20,
                color: statusColor.withAlpha(220),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  label,
                  style: const TextStyle(
                    color: Colors.black87,
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          // Value row - full width
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.black,
                fontSize: 14,
              ),
              textAlign: TextAlign.left,
            ),
          ),
        ],
      );
    }

    // Regular horizontal layout for non-multiline content
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Label section with icon
        Expanded(
          flex: 1,
          child: Row(
            children: [
              Icon(
                icon,
                size: 20,
                color: isHighlighted ? statusColor : statusColor.withAlpha(220),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  label,
                  style: const TextStyle(
                    color: Colors.black87,
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
        ),

        // Value section (right-aligned)
        Expanded(
          flex: 1,
          child: isStatus
              ? Align(
                  alignment: Alignment.centerRight,
                  child: InkWell(
                    onTap: onTap,
                    borderRadius: BorderRadius.circular(4),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 3),
                      decoration: BoxDecoration(
                        color: statusColor.withAlpha(40),
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(
                            color: statusColor.withAlpha(100), width: 1),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Flexible(
                            child: Text(
                              value,
                              style: TextStyle(
                                color: statusColor,
                                fontWeight: FontWeight.bold,
                                fontSize: 13,
                              ),
                              overflow: TextOverflow.visible,
                              maxLines: 3,
                            ),
                          ),
                          if (onTap != null) ...[
                            const SizedBox(width: 4),
                            Icon(
                              Icons.edit,
                              size: 12,
                              color: statusColor,
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
                )
              : isHighlighted
                  ? Container(
                      decoration: BoxDecoration(
                        color: color.withAlpha(26), // 0.1 * 255 = 26
                        borderRadius: BorderRadius.circular(4),
                      ),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 2),
                      child: Text(
                        value,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: color,
                        ),
                        textAlign: TextAlign.right,
                        maxLines: 3,
                        overflow: TextOverflow.visible,
                      ),
                    )
                  : Text(
                      value,
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        color: color != Colors.blue ? color : Colors.black,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.right,
                      maxLines: 3,
                      overflow: TextOverflow.visible,
                    ),
        ),
      ],
    );
  }
}
