import 'dart:convert';
import 'dart:math';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'package:uuid/uuid.dart';
import 'package:intl/intl.dart';
import '../models/milk_record_isar.dart';
import '../../../services/logging_service.dart';
import '../services/milk_repository.dart';
import 'package:get_it/get_it.dart';

class MilkService {
  static const String _milkRecordsKey = 'milk_records';
  static const String _cattleMilkRecordsKey = 'cattle_milk_records';
  static const String _lastSyncKey = 'last_milk_sync';
  final String _apiBaseUrl = 'https://your-api-endpoint.com/api';
  final LoggingService _logger = LoggingService();
  final MilkRepository _milkRepository = GetIt.instance<MilkRepository>();

  // General Milk Records Methods
  Future<List<MilkRecordIsar>> getMilkRecords() async {
    try {
      return await GetIt.instance<Isar>().milkRecordIsars.where().findAll();
    } catch (e) {
      _logger.error('Error getting milk records: $e');
      return [];
    }
  }

  Future<List<MilkRecordIsar>> getMilkRecordsForDate(DateTime date) async {
    try {
      final records = await getMilkRecords();
      final dateString = DateFormat('yyyy-MM-dd').format(date);
      
      return records.where((record) {
        if (record.date == null) return false;
        final recordDateString = DateFormat('yyyy-MM-dd').format(record.date!);
        return recordDateString == dateString;
      }).toList();
    } catch (e) {
      _logger.error('Error getting milk records for date: $e');
      return [];
    }
  }

  Future<void> addMilkRecord(MilkRecordIsar record) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final records = await getMilkRecords();
      
      // Check if a record with the same ID already exists
      final existingIndex = records.indexWhere((r) => r.id == record.id);
      if (existingIndex != -1) {
        // Update existing record
        records[existingIndex] = record;
      } else {
        // Add new record
        records.add(record);
      }
      
      await prefs.setString(_milkRecordsKey, jsonEncode(records.map((r) => r.toMap()).toList()));

      // Also add to cattle-specific records if not already there
      final cattleRecords = await getCattleMilkRecords(record.cattleTagId ?? '');
      
      // Check if a record for this date already exists
      final existingRecord = cattleRecords.where((r) => 
        r.date != null && DateFormat('yyyy-MM-dd').format(r.date!) == DateFormat('yyyy-MM-dd').format(record.date ?? DateTime.now())
      ).toList();
      
      if (existingRecord.isEmpty) {
        // Create new cattle milk record
        final cattleMilkRecord = MilkRecordIsar()
          ..businessId = const Uuid().v4()
          ..cattleTagId = record.cattleTagId
          ..cattleBusinessId = record.cattleBusinessId
          ..date = record.date
          ..morningAmount = record.morningAmount
          ..afternoonAmount = 0.0
          ..eveningAmount = record.eveningAmount
          ..totalAmount = (record.morningAmount ?? 0.0) + (record.eveningAmount ?? 0.0)
          ..notes = record.notes ?? ''
          ..fatContent = record.fatContent
          ..pricePerLiter = record.pricePerLiter
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now();
        
        // Save to database
        await _milkRepository.saveMilkRecord(cattleMilkRecord);
      } else {
        // Update existing record
        final existingCattleRecord = existingRecord.first;
        existingCattleRecord.morningAmount = record.morningAmount;
        existingCattleRecord.eveningAmount = record.eveningAmount;
        existingCattleRecord.totalAmount = (record.morningAmount ?? 0.0) + (record.eveningAmount ?? 0.0);
        existingCattleRecord.notes = record.notes ?? existingCattleRecord.notes;
        existingCattleRecord.fatContent = record.fatContent ?? existingCattleRecord.fatContent;
        existingCattleRecord.pricePerLiter = record.pricePerLiter ?? existingCattleRecord.pricePerLiter;
        existingCattleRecord.updatedAt = DateTime.now();
        
        // Save to database
        await _milkRepository.saveMilkRecord(existingCattleRecord);
      }
    } catch (e) {
      _logger.error('Error adding milk record: $e');
      throw Exception('Failed to add milk record: $e');
    }
  }

  Future<void> updateMilkRecord(MilkRecordIsar record) async {
    final prefs = await SharedPreferences.getInstance();
    final records = await getMilkRecords();
    final index = records.indexWhere((r) => r.id == record.id);
    
    if (index != -1) {
      records[index] = record;
      await prefs.setString(_milkRecordsKey, jsonEncode(records.map((r) => r.toMap()).toList()));
    } else {
      throw Exception('Record not found');
    }
  }

  Future<void> deleteMilkRecord(int id) async {
    final prefs = await SharedPreferences.getInstance();
    final records = await getMilkRecords();
    
    // Find the record to delete so we can get its cattleTagId and date
    final recordToDelete = records.firstWhere((record) => record.id == id, 
      orElse: () => MilkRecordIsar()
    );
    
    // Only proceed if we found the record
    if (recordToDelete.businessId != null && recordToDelete.businessId!.isNotEmpty) {
      // Remove from main records
      records.removeWhere((record) => record.id == id);
      await prefs.setString(_milkRecordsKey, jsonEncode(records.map((r) => r.toMap()).toList()));
      
      // Also remove from cattle-specific records for the same date
      if (recordToDelete.cattleTagId != null) {
        final cattleRecords = await getCattleMilkRecords(recordToDelete.cattleTagId!);
        final sameDate = cattleRecords.where((r) => 
          r.date != null && recordToDelete.date != null && 
          DateFormat('yyyy-MM-dd').format(r.date!) == DateFormat('yyyy-MM-dd').format(recordToDelete.date!)
        ).toList();
        
        if (sameDate.isNotEmpty) {
          await _milkRepository.deleteMilkRecord(sameDate.first.id);
        }
      }
    }
  }

  // Cattle-specific Milk Records Methods
  Future<List<MilkRecordIsar>> getCattleMilkRecords(String cattleTagId) async {
    try {
      // Get records from Isar database first
      final records = await GetIt.instance<Isar>().milkRecordIsars
          .filter()
          .cattleTagIdEqualTo(cattleTagId)
          .findAll();
      if (records.isNotEmpty) {
        return records;
      }
      
      // Fall back to SharedPreferences for backward compatibility
      final prefs = await SharedPreferences.getInstance();
      final recordsJson = prefs.getStringList('${_cattleMilkRecordsKey}_$cattleTagId');
      if (recordsJson == null || recordsJson.isEmpty) {
        // Check if there are any records in the main milk records that match this cattle
        final allMilkRecords = await getMilkRecords();
        final cattleMilkRecords = allMilkRecords
            .where((record) => record.cattleTagId == cattleTagId)
            .map((record) {
              final isar = MilkRecordIsar()
                ..businessId = const Uuid().v4()
                ..cattleTagId = record.cattleTagId
                ..cattleBusinessId = record.cattleBusinessId
                ..date = record.date
                ..morningAmount = record.morningAmount
                ..afternoonAmount = 0.0
                ..eveningAmount = record.eveningAmount
                ..totalAmount = (record.morningAmount ?? 0.0) + (record.eveningAmount ?? 0.0)
                ..notes = record.notes ?? ''
                ..fatContent = record.fatContent
                ..pricePerLiter = record.pricePerLiter
                ..createdAt = DateTime.now()
                ..updatedAt = DateTime.now();
              return isar;
            })
            .toList();
        
        if (cattleMilkRecords.isNotEmpty) {
          // Save these records to the Isar database
          for (final record in cattleMilkRecords) {
            await _milkRepository.saveMilkRecord(record);
          }
          return cattleMilkRecords;
        }
        
        return [];
      }

      final oldRecords = recordsJson
          .map((json) => jsonDecode(json) as Map<String, dynamic>)
          .map((map) {
            final isar = MilkRecordIsar()
              ..businessId = map['id'] as String? ?? const Uuid().v4()
              ..cattleTagId = map['tagId'] as String? ?? ''
              ..date = map['date'] != null ? DateTime.parse(map['date'] as String) : null
              ..morningAmount = (map['morningYield'] as num?)?.toDouble() ?? 0.0
              ..afternoonAmount = 0.0
              ..eveningAmount = (map['eveningYield'] as num?)?.toDouble() ?? 0.0
              ..totalAmount = ((map['morningYield'] as num?)?.toDouble() ?? 0.0) + 
                           ((map['eveningYield'] as num?)?.toDouble() ?? 0.0)
              ..notes = map['notes'] as String? ?? ''
              ..fatContent = (map['fatContent'] as num?)?.toDouble()
              ..createdAt = map['createdAt'] != null ? DateTime.parse(map['createdAt'] as String) : DateTime.now()
              ..updatedAt = map['updatedAt'] != null ? DateTime.parse(map['updatedAt'] as String) : DateTime.now();
            return isar;
          })
          .toList();
      
      // Save these records to the Isar database for future use
      for (final record in oldRecords) {
        await _milkRepository.saveMilkRecord(record);
      }
      
      return oldRecords;
    } catch (e) {
      _logger.error('Error getting cattle milk records: $e');
      return [];
    }
  }

  Future<void> addCattleMilkRecord(MilkRecordIsar record) async {
    try {
      // Save to Isar database
      await _milkRepository.saveMilkRecord(record);

      // Create a combined milk record for the main milk records list
      final milkRecord = MilkRecordIsar()
        ..businessId = const Uuid().v4()
        ..cattleTagId = record.cattleTagId
        ..cattleBusinessId = record.cattleBusinessId
        ..date = record.date ?? DateTime.now()
        ..morningAmount = record.morningAmount ?? 0.0
        ..eveningAmount = record.eveningAmount ?? 0.0
        ..afternoonAmount = record.afternoonAmount ?? 0.0
        ..totalAmount = (record.morningAmount ?? 0.0) + (record.afternoonAmount ?? 0.0) + (record.eveningAmount ?? 0.0)
        ..fatContent = record.fatContent
        ..notes = record.notes
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now();

      await addMilkRecord(milkRecord);
    } catch (e) {
      _logger.error('Error adding cattle milk record: $e');
      throw Exception('Failed to add cattle milk record: $e');
    }
  }

  Future<void> deleteCattleMilkRecord(String cattleTagId, int id) async {
    try {
      // Delete from Isar database
      await _milkRepository.deleteMilkRecord(id);
      
      // For backward compatibility, also remove from SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final recordsJsonList = prefs.getStringList('${_cattleMilkRecordsKey}_$cattleTagId');
      
      if (recordsJsonList != null && recordsJsonList.isNotEmpty) {
        final records = recordsJsonList
            .map((json) => jsonDecode(json) as Map<String, dynamic>)
            .toList();
        
        // Remove the record with the matching ID
        records.removeWhere((map) => map['id'] == id);
        
        // Save the updated list back to preferences
        final updatedRecordsJson = records.map((map) => jsonEncode(map)).toList();
        await prefs.setStringList('${_cattleMilkRecordsKey}_$cattleTagId', updatedRecordsJson);
      }
    } catch (e) {
      _logger.error('Error deleting cattle milk record: $e');
      throw Exception('Failed to delete cattle milk record: $e');
    }
  }

  // Production Summary Methods
  Future<Map<String, double>> getProductionSummary(String cattleTagId,
      {DateTime? startDate, DateTime? endDate}) async {
    final records = await getCattleMilkRecords(cattleTagId);

    if (records.isEmpty) {
      return {
        'morningAverage': 0,
        'eveningAverage': 0,
        'totalAverage': 0,
        'totalProduction': 0,
      };
    }

    final start = startDate ??
        records.map((r) => r.date).whereType<DateTime>().reduce((a, b) => a.isBefore(b) ? a : b);
    final end = endDate ?? DateTime.now();

    final filteredRecords = records
        .where((r) =>
            r.date != null &&
            r.date!.isAfter(start.subtract(const Duration(days: 1))) &&
            r.date!.isBefore(end.add(const Duration(days: 1))))
        .toList();

    if (filteredRecords.isEmpty) {
      return {
        'morningAverage': 0,
        'eveningAverage': 0,
        'totalAverage': 0,
        'totalProduction': 0,
      };
    }

    double totalMorning = 0;
    double totalEvening = 0;

    final uniqueDates = filteredRecords
        .where((r) => r.date != null)
        .map((r) => DateFormat('yyyy-MM-dd').format(r.date!))
        .toSet();
    final actualDays = uniqueDates.length;

    for (var record in filteredRecords) {
      totalMorning += record.morningAmount ?? 0.0;
      totalEvening += record.eveningAmount ?? 0.0;
    }

    final totalProduction = totalMorning + totalEvening;

    return {
      'morningAverage': totalMorning / actualDays,
      'eveningAverage': totalEvening / actualDays,
      'totalAverage': totalProduction / actualDays,
      'totalProduction': totalProduction,
    };
  }

  // Date Range Methods
  Future<List<MilkRecordIsar>> getMilkRecordsByDateRange(
      DateTime start, DateTime end,
      {String? cattleTagId}) async {
    final records = await getMilkRecords();
    return records.where((record) {
      final dateMatches = record.date != null &&
          record.date!.isAfter(start.subtract(const Duration(days: 1))) &&
          record.date!.isBefore(end.add(const Duration(days: 1)));
      if (cattleTagId != null) {
        return dateMatches && record.cattleTagId == cattleTagId;
      }
      return dateMatches;
    }).toList();
  }

  Future<List<MilkRecordIsar>> getMilkRecordsByTagId(String cattleTagId) async {
    final records = await getMilkRecords();
    return records.where((record) => record.cattleTagId == cattleTagId).toList();
  }

  Future<List<MilkRecordIsar>> getMilkRecordsForDateRange(
      DateTime startDate, DateTime endDate, {String? cattleTagId}) async {
    final records = await getMilkRecords();
    return records.where((record) {
      if (record.date == null || record.date!.isBefore(startDate) || record.date!.isAfter(endDate)) {
        return false;
      }
      if (cattleTagId != null && record.cattleTagId != cattleTagId) {
        return false;
      }
      return true;
    }).toList();
  }

  Future<List<MilkRecordIsar>> getCattleMilkRecordsByPeriod(
      String cattleTagId, DateTime start, DateTime end) async {
    final records = await getCattleMilkRecords(cattleTagId);
    return records
        .where((r) => r.date != null && r.date!.isAfter(start) && r.date!.isBefore(end))
        .toList();
  }

  Future<Map<String, List<MilkRecordIsar>>> getMilkRecordsByTagIdMap() async {
    final records = await getMilkRecords();
    final Map<String, List<MilkRecordIsar>> recordMap = {};
    for (var record in records) {
      final cattleTagId = record.cattleTagId;
      if (cattleTagId != null) {
        if (!recordMap.containsKey(cattleTagId)) {
          recordMap[cattleTagId] = [];
        }
        recordMap[cattleTagId]!.add(record);
      }
    }
    return recordMap;
  }

  // Production Analysis Methods
  Future<Map<DateTime, double>> getDailyProduction(DateTime start, DateTime end,
      {String? cattleTagId}) async {
    final records =
        await getMilkRecordsByDateRange(start, end, cattleTagId: cattleTagId);
    final Map<DateTime, double> dailyProduction = {};

    for (var record in records) {
      if (record.date != null) {
        final date =
            DateTime(record.date!.year, record.date!.month, record.date!.day);
        dailyProduction[date] = (dailyProduction[date] ?? 0) + (record.morningAmount ?? 0.0) + (record.eveningAmount ?? 0.0);
      }
    }

    return dailyProduction;
  }

  Future<Map<DateTime, double>> getWeeklyProduction(
      DateTime start, DateTime end,
      {String? cattleTagId}) async {
    final records =
        await getMilkRecordsByDateRange(start, end, cattleTagId: cattleTagId);
    final Map<DateTime, double> weeklyProduction = {};

    for (var record in records) {
      if (record.date != null) {
        final date =
            DateTime(record.date!.year, record.date!.month, record.date!.day);
        final weekStart = date.subtract(Duration(days: date.weekday - 1));
        weeklyProduction[weekStart] =
            (weeklyProduction[weekStart] ?? 0) + (record.morningAmount ?? 0.0) + (record.eveningAmount ?? 0.0);
      }
    }

    return weeklyProduction;
  }

  Future<Map<DateTime, double>> getMonthlyProduction(
      DateTime start, DateTime end,
      {String? cattleTagId}) async {
    final records =
        await getMilkRecordsByDateRange(start, end, cattleTagId: cattleTagId);
    final Map<DateTime, double> monthlyProduction = {};

    for (var record in records) {
      if (record.date != null) {
        final monthStart = DateTime(record.date!.year, record.date!.month, 1);
        monthlyProduction[monthStart] =
            (monthlyProduction[monthStart] ?? 0) + (record.morningAmount ?? 0.0) + (record.eveningAmount ?? 0.0);
      }
    }

    return monthlyProduction;
  }

  // Sync Methods
  Future<DateTime?> getLastSyncTime() async {
    final prefs = await SharedPreferences.getInstance();
    final lastSync = prefs.getString(_lastSyncKey);
    return lastSync != null ? DateTime.parse(lastSync) : null;
  }

  Future<void> setLastSyncTime(DateTime time) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_lastSyncKey, time.toIso8601String());
  }

  Future<bool> syncData() async {
    try {
      final lastSync = await getLastSyncTime();
      final localRecords = await getMilkRecords();

      final response = await http.post(
        Uri.parse('$_apiBaseUrl/milk-records/sync'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'lastSync': lastSync?.toIso8601String(),
          'records': localRecords.map((r) => r.toMap()).toList(),
        }),
      );

      if (response.statusCode == 200) {
        if (!response.body.startsWith('{')) {
          _logger.error(
              'Invalid response format: Expected JSON, got ${response.body.substring(0, min(100, response.body.length))}');
          return false;
        }

        final Map<String, dynamic> syncData = jsonDecode(response.body);
        if (!syncData.containsKey('records')) {
          _logger.error('Invalid response format: Missing records field');
          return false;
        }
        final List<dynamic> serverRecords = syncData['records'];

        final updatedRecords =
            serverRecords.map((json) => MilkRecordIsar.fromMap(json)).toList();

        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_milkRecordsKey,
            jsonEncode(updatedRecords.map((r) => r.toMap()).toList()));
        await setLastSyncTime(DateTime.now());

        _logger.info('Milk records synchronized successfully');
        return true;
      } else {
        _logger.error('Failed to sync milk records: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      _logger.error('Error syncing milk records: $e');
      return false;
    }
  }
}
