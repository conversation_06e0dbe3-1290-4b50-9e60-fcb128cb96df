// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'alert_settings_isar.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetAlertChannelIsarCollection on Isar {
  IsarCollection<AlertChannelIsar> get alertChannelIsars => this.collection();
}

const AlertChannelIsarSchema = CollectionSchema(
  name: r'AlertChannelIsar',
  id: -6657635921074338634,
  properties: {
    r'businessId': PropertySchema(
      id: 0,
      name: r'businessId',
      type: IsarType.string,
    ),
    r'config': PropertySchema(
      id: 1,
      name: r'config',
      type: IsarType.object,
      target: r'ConfigMap',
    ),
    r'createdAt': PropertySchema(
      id: 2,
      name: r'createdAt',
      type: IsarType.dateTime,
    ),
    r'enabled': PropertySchema(
      id: 3,
      name: r'enabled',
      type: IsarType.bool,
    ),
    r'farmBusinessId': PropertySchema(
      id: 4,
      name: r'farmBusinessId',
      type: IsarType.string,
    ),
    r'name': PropertySchema(
      id: 5,
      name: r'name',
      type: IsarType.string,
    ),
    r'updatedAt': PropertySchema(
      id: 6,
      name: r'updatedAt',
      type: IsarType.dateTime,
    )
  },
  estimateSize: _alertChannelIsarEstimateSize,
  serialize: _alertChannelIsarSerialize,
  deserialize: _alertChannelIsarDeserialize,
  deserializeProp: _alertChannelIsarDeserializeProp,
  idName: r'id',
  indexes: {
    r'businessId': IndexSchema(
      id: 2228048290814354584,
      name: r'businessId',
      unique: true,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'businessId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'name': IndexSchema(
      id: 879695947855722453,
      name: r'name',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'name',
          type: IndexType.hash,
          caseSensitive: false,
        )
      ],
    ),
    r'farmBusinessId': IndexSchema(
      id: -7111559275757090744,
      name: r'farmBusinessId',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'farmBusinessId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    )
  },
  links: {},
  embeddedSchemas: {r'ConfigMap': ConfigMapSchema},
  getId: _alertChannelIsarGetId,
  getLinks: _alertChannelIsarGetLinks,
  attach: _alertChannelIsarAttach,
  version: '3.1.0+1',
);

int _alertChannelIsarEstimateSize(
  AlertChannelIsar object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.businessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.config;
    if (value != null) {
      bytesCount += 3 +
          ConfigMapSchema.estimateSize(
              value, allOffsets[ConfigMap]!, allOffsets);
    }
  }
  {
    final value = object.farmBusinessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.name;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _alertChannelIsarSerialize(
  AlertChannelIsar object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeString(offsets[0], object.businessId);
  writer.writeObject<ConfigMap>(
    offsets[1],
    allOffsets,
    ConfigMapSchema.serialize,
    object.config,
  );
  writer.writeDateTime(offsets[2], object.createdAt);
  writer.writeBool(offsets[3], object.enabled);
  writer.writeString(offsets[4], object.farmBusinessId);
  writer.writeString(offsets[5], object.name);
  writer.writeDateTime(offsets[6], object.updatedAt);
}

AlertChannelIsar _alertChannelIsarDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = AlertChannelIsar();
  object.businessId = reader.readStringOrNull(offsets[0]);
  object.config = reader.readObjectOrNull<ConfigMap>(
    offsets[1],
    ConfigMapSchema.deserialize,
    allOffsets,
  );
  object.createdAt = reader.readDateTimeOrNull(offsets[2]);
  object.enabled = reader.readBool(offsets[3]);
  object.farmBusinessId = reader.readStringOrNull(offsets[4]);
  object.id = id;
  object.name = reader.readStringOrNull(offsets[5]);
  object.updatedAt = reader.readDateTimeOrNull(offsets[6]);
  return object;
}

P _alertChannelIsarDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readStringOrNull(offset)) as P;
    case 1:
      return (reader.readObjectOrNull<ConfigMap>(
        offset,
        ConfigMapSchema.deserialize,
        allOffsets,
      )) as P;
    case 2:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 3:
      return (reader.readBool(offset)) as P;
    case 4:
      return (reader.readStringOrNull(offset)) as P;
    case 5:
      return (reader.readStringOrNull(offset)) as P;
    case 6:
      return (reader.readDateTimeOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _alertChannelIsarGetId(AlertChannelIsar object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _alertChannelIsarGetLinks(AlertChannelIsar object) {
  return [];
}

void _alertChannelIsarAttach(
    IsarCollection<dynamic> col, Id id, AlertChannelIsar object) {
  object.id = id;
}

extension AlertChannelIsarByIndex on IsarCollection<AlertChannelIsar> {
  Future<AlertChannelIsar?> getByBusinessId(String? businessId) {
    return getByIndex(r'businessId', [businessId]);
  }

  AlertChannelIsar? getByBusinessIdSync(String? businessId) {
    return getByIndexSync(r'businessId', [businessId]);
  }

  Future<bool> deleteByBusinessId(String? businessId) {
    return deleteByIndex(r'businessId', [businessId]);
  }

  bool deleteByBusinessIdSync(String? businessId) {
    return deleteByIndexSync(r'businessId', [businessId]);
  }

  Future<List<AlertChannelIsar?>> getAllByBusinessId(
      List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndex(r'businessId', values);
  }

  List<AlertChannelIsar?> getAllByBusinessIdSync(
      List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndexSync(r'businessId', values);
  }

  Future<int> deleteAllByBusinessId(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndex(r'businessId', values);
  }

  int deleteAllByBusinessIdSync(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndexSync(r'businessId', values);
  }

  Future<Id> putByBusinessId(AlertChannelIsar object) {
    return putByIndex(r'businessId', object);
  }

  Id putByBusinessIdSync(AlertChannelIsar object, {bool saveLinks = true}) {
    return putByIndexSync(r'businessId', object, saveLinks: saveLinks);
  }

  Future<List<Id>> putAllByBusinessId(List<AlertChannelIsar> objects) {
    return putAllByIndex(r'businessId', objects);
  }

  List<Id> putAllByBusinessIdSync(List<AlertChannelIsar> objects,
      {bool saveLinks = true}) {
    return putAllByIndexSync(r'businessId', objects, saveLinks: saveLinks);
  }
}

extension AlertChannelIsarQueryWhereSort
    on QueryBuilder<AlertChannelIsar, AlertChannelIsar, QWhere> {
  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension AlertChannelIsarQueryWhere
    on QueryBuilder<AlertChannelIsar, AlertChannelIsar, QWhereClause> {
  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterWhereClause> idEqualTo(
      Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterWhereClause>
      idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterWhereClause>
      idGreaterThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterWhereClause>
      idLessThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterWhereClause> idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterWhereClause>
      businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [null],
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterWhereClause>
      businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'businessId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterWhereClause>
      businessIdEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [businessId],
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterWhereClause>
      businessIdNotEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterWhereClause>
      nameIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'name',
        value: [null],
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterWhereClause>
      nameIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'name',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterWhereClause>
      nameEqualTo(String? name) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'name',
        value: [name],
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterWhereClause>
      nameNotEqualTo(String? name) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'name',
              lower: [],
              upper: [name],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'name',
              lower: [name],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'name',
              lower: [name],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'name',
              lower: [],
              upper: [name],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterWhereClause>
      farmBusinessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'farmBusinessId',
        value: [null],
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterWhereClause>
      farmBusinessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'farmBusinessId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterWhereClause>
      farmBusinessIdEqualTo(String? farmBusinessId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'farmBusinessId',
        value: [farmBusinessId],
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterWhereClause>
      farmBusinessIdNotEqualTo(String? farmBusinessId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'farmBusinessId',
              lower: [],
              upper: [farmBusinessId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'farmBusinessId',
              lower: [farmBusinessId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'farmBusinessId',
              lower: [farmBusinessId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'farmBusinessId',
              lower: [],
              upper: [farmBusinessId],
              includeUpper: false,
            ));
      }
    });
  }
}

extension AlertChannelIsarQueryFilter
    on QueryBuilder<AlertChannelIsar, AlertChannelIsar, QFilterCondition> {
  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      businessIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      businessIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      businessIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      businessIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'businessId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      businessIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      businessIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      businessIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      businessIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'businessId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      businessIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      businessIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      configIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'config',
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      configIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'config',
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      createdAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      createdAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      createdAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      createdAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      createdAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      createdAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'createdAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      enabledEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'enabled',
        value: value,
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      farmBusinessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'farmBusinessId',
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      farmBusinessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'farmBusinessId',
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      farmBusinessIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      farmBusinessIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      farmBusinessIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      farmBusinessIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'farmBusinessId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      farmBusinessIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      farmBusinessIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      farmBusinessIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      farmBusinessIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'farmBusinessId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      farmBusinessIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'farmBusinessId',
        value: '',
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      farmBusinessIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'farmBusinessId',
        value: '',
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      idEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      nameIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'name',
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      nameIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'name',
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      nameEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      nameGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      nameLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      nameBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'name',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      nameStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      nameEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      nameContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      nameMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'name',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      nameIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'name',
        value: '',
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      nameIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'name',
        value: '',
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      updatedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      updatedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      updatedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      updatedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      updatedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      updatedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'updatedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension AlertChannelIsarQueryObject
    on QueryBuilder<AlertChannelIsar, AlertChannelIsar, QFilterCondition> {
  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterFilterCondition>
      config(FilterQuery<ConfigMap> q) {
    return QueryBuilder.apply(this, (query) {
      return query.object(q, r'config');
    });
  }
}

extension AlertChannelIsarQueryLinks
    on QueryBuilder<AlertChannelIsar, AlertChannelIsar, QFilterCondition> {}

extension AlertChannelIsarQuerySortBy
    on QueryBuilder<AlertChannelIsar, AlertChannelIsar, QSortBy> {
  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterSortBy>
      sortByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterSortBy>
      sortByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterSortBy>
      sortByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterSortBy>
      sortByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterSortBy>
      sortByEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'enabled', Sort.asc);
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterSortBy>
      sortByEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'enabled', Sort.desc);
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterSortBy>
      sortByFarmBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'farmBusinessId', Sort.asc);
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterSortBy>
      sortByFarmBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'farmBusinessId', Sort.desc);
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterSortBy> sortByName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.asc);
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterSortBy>
      sortByNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.desc);
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterSortBy>
      sortByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterSortBy>
      sortByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }
}

extension AlertChannelIsarQuerySortThenBy
    on QueryBuilder<AlertChannelIsar, AlertChannelIsar, QSortThenBy> {
  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterSortBy>
      thenByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterSortBy>
      thenByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterSortBy>
      thenByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterSortBy>
      thenByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterSortBy>
      thenByEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'enabled', Sort.asc);
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterSortBy>
      thenByEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'enabled', Sort.desc);
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterSortBy>
      thenByFarmBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'farmBusinessId', Sort.asc);
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterSortBy>
      thenByFarmBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'farmBusinessId', Sort.desc);
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterSortBy>
      thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterSortBy> thenByName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.asc);
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterSortBy>
      thenByNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.desc);
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterSortBy>
      thenByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QAfterSortBy>
      thenByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }
}

extension AlertChannelIsarQueryWhereDistinct
    on QueryBuilder<AlertChannelIsar, AlertChannelIsar, QDistinct> {
  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QDistinct>
      distinctByBusinessId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'businessId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QDistinct>
      distinctByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'createdAt');
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QDistinct>
      distinctByEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'enabled');
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QDistinct>
      distinctByFarmBusinessId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'farmBusinessId',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QDistinct> distinctByName(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'name', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<AlertChannelIsar, AlertChannelIsar, QDistinct>
      distinctByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'updatedAt');
    });
  }
}

extension AlertChannelIsarQueryProperty
    on QueryBuilder<AlertChannelIsar, AlertChannelIsar, QQueryProperty> {
  QueryBuilder<AlertChannelIsar, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<AlertChannelIsar, String?, QQueryOperations>
      businessIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'businessId');
    });
  }

  QueryBuilder<AlertChannelIsar, ConfigMap?, QQueryOperations>
      configProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'config');
    });
  }

  QueryBuilder<AlertChannelIsar, DateTime?, QQueryOperations>
      createdAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'createdAt');
    });
  }

  QueryBuilder<AlertChannelIsar, bool, QQueryOperations> enabledProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'enabled');
    });
  }

  QueryBuilder<AlertChannelIsar, String?, QQueryOperations>
      farmBusinessIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'farmBusinessId');
    });
  }

  QueryBuilder<AlertChannelIsar, String?, QQueryOperations> nameProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'name');
    });
  }

  QueryBuilder<AlertChannelIsar, DateTime?, QQueryOperations>
      updatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'updatedAt');
    });
  }
}

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetAlertTypeIsarCollection on Isar {
  IsarCollection<AlertTypeIsar> get alertTypeIsars => this.collection();
}

const AlertTypeIsarSchema = CollectionSchema(
  name: r'AlertTypeIsar',
  id: -108674655097273537,
  properties: {
    r'businessId': PropertySchema(
      id: 0,
      name: r'businessId',
      type: IsarType.string,
    ),
    r'config': PropertySchema(
      id: 1,
      name: r'config',
      type: IsarType.object,
      target: r'ConfigMap',
    ),
    r'createdAt': PropertySchema(
      id: 2,
      name: r'createdAt',
      type: IsarType.dateTime,
    ),
    r'description': PropertySchema(
      id: 3,
      name: r'description',
      type: IsarType.string,
    ),
    r'enabled': PropertySchema(
      id: 4,
      name: r'enabled',
      type: IsarType.bool,
    ),
    r'enabledChannels': PropertySchema(
      id: 5,
      name: r'enabledChannels',
      type: IsarType.stringList,
    ),
    r'enabledChannelsString': PropertySchema(
      id: 6,
      name: r'enabledChannelsString',
      type: IsarType.string,
    ),
    r'farmBusinessId': PropertySchema(
      id: 7,
      name: r'farmBusinessId',
      type: IsarType.string,
    ),
    r'name': PropertySchema(
      id: 8,
      name: r'name',
      type: IsarType.string,
    ),
    r'updatedAt': PropertySchema(
      id: 9,
      name: r'updatedAt',
      type: IsarType.dateTime,
    )
  },
  estimateSize: _alertTypeIsarEstimateSize,
  serialize: _alertTypeIsarSerialize,
  deserialize: _alertTypeIsarDeserialize,
  deserializeProp: _alertTypeIsarDeserializeProp,
  idName: r'id',
  indexes: {
    r'businessId': IndexSchema(
      id: 2228048290814354584,
      name: r'businessId',
      unique: true,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'businessId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'name': IndexSchema(
      id: 879695947855722453,
      name: r'name',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'name',
          type: IndexType.hash,
          caseSensitive: false,
        )
      ],
    ),
    r'farmBusinessId': IndexSchema(
      id: -7111559275757090744,
      name: r'farmBusinessId',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'farmBusinessId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    )
  },
  links: {},
  embeddedSchemas: {r'ConfigMap': ConfigMapSchema},
  getId: _alertTypeIsarGetId,
  getLinks: _alertTypeIsarGetLinks,
  attach: _alertTypeIsarAttach,
  version: '3.1.0+1',
);

int _alertTypeIsarEstimateSize(
  AlertTypeIsar object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.businessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.config;
    if (value != null) {
      bytesCount += 3 +
          ConfigMapSchema.estimateSize(
              value, allOffsets[ConfigMap]!, allOffsets);
    }
  }
  {
    final value = object.description;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  bytesCount += 3 + object.enabledChannels.length * 3;
  {
    for (var i = 0; i < object.enabledChannels.length; i++) {
      final value = object.enabledChannels[i];
      bytesCount += value.length * 3;
    }
  }
  {
    final value = object.enabledChannelsString;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.farmBusinessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.name;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _alertTypeIsarSerialize(
  AlertTypeIsar object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeString(offsets[0], object.businessId);
  writer.writeObject<ConfigMap>(
    offsets[1],
    allOffsets,
    ConfigMapSchema.serialize,
    object.config,
  );
  writer.writeDateTime(offsets[2], object.createdAt);
  writer.writeString(offsets[3], object.description);
  writer.writeBool(offsets[4], object.enabled);
  writer.writeStringList(offsets[5], object.enabledChannels);
  writer.writeString(offsets[6], object.enabledChannelsString);
  writer.writeString(offsets[7], object.farmBusinessId);
  writer.writeString(offsets[8], object.name);
  writer.writeDateTime(offsets[9], object.updatedAt);
}

AlertTypeIsar _alertTypeIsarDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = AlertTypeIsar();
  object.businessId = reader.readStringOrNull(offsets[0]);
  object.config = reader.readObjectOrNull<ConfigMap>(
    offsets[1],
    ConfigMapSchema.deserialize,
    allOffsets,
  );
  object.createdAt = reader.readDateTimeOrNull(offsets[2]);
  object.description = reader.readStringOrNull(offsets[3]);
  object.enabled = reader.readBool(offsets[4]);
  object.enabledChannels = reader.readStringList(offsets[5]) ?? [];
  object.enabledChannelsString = reader.readStringOrNull(offsets[6]);
  object.farmBusinessId = reader.readStringOrNull(offsets[7]);
  object.id = id;
  object.name = reader.readStringOrNull(offsets[8]);
  object.updatedAt = reader.readDateTimeOrNull(offsets[9]);
  return object;
}

P _alertTypeIsarDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readStringOrNull(offset)) as P;
    case 1:
      return (reader.readObjectOrNull<ConfigMap>(
        offset,
        ConfigMapSchema.deserialize,
        allOffsets,
      )) as P;
    case 2:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 3:
      return (reader.readStringOrNull(offset)) as P;
    case 4:
      return (reader.readBool(offset)) as P;
    case 5:
      return (reader.readStringList(offset) ?? []) as P;
    case 6:
      return (reader.readStringOrNull(offset)) as P;
    case 7:
      return (reader.readStringOrNull(offset)) as P;
    case 8:
      return (reader.readStringOrNull(offset)) as P;
    case 9:
      return (reader.readDateTimeOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _alertTypeIsarGetId(AlertTypeIsar object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _alertTypeIsarGetLinks(AlertTypeIsar object) {
  return [];
}

void _alertTypeIsarAttach(
    IsarCollection<dynamic> col, Id id, AlertTypeIsar object) {
  object.id = id;
}

extension AlertTypeIsarByIndex on IsarCollection<AlertTypeIsar> {
  Future<AlertTypeIsar?> getByBusinessId(String? businessId) {
    return getByIndex(r'businessId', [businessId]);
  }

  AlertTypeIsar? getByBusinessIdSync(String? businessId) {
    return getByIndexSync(r'businessId', [businessId]);
  }

  Future<bool> deleteByBusinessId(String? businessId) {
    return deleteByIndex(r'businessId', [businessId]);
  }

  bool deleteByBusinessIdSync(String? businessId) {
    return deleteByIndexSync(r'businessId', [businessId]);
  }

  Future<List<AlertTypeIsar?>> getAllByBusinessId(
      List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndex(r'businessId', values);
  }

  List<AlertTypeIsar?> getAllByBusinessIdSync(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndexSync(r'businessId', values);
  }

  Future<int> deleteAllByBusinessId(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndex(r'businessId', values);
  }

  int deleteAllByBusinessIdSync(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndexSync(r'businessId', values);
  }

  Future<Id> putByBusinessId(AlertTypeIsar object) {
    return putByIndex(r'businessId', object);
  }

  Id putByBusinessIdSync(AlertTypeIsar object, {bool saveLinks = true}) {
    return putByIndexSync(r'businessId', object, saveLinks: saveLinks);
  }

  Future<List<Id>> putAllByBusinessId(List<AlertTypeIsar> objects) {
    return putAllByIndex(r'businessId', objects);
  }

  List<Id> putAllByBusinessIdSync(List<AlertTypeIsar> objects,
      {bool saveLinks = true}) {
    return putAllByIndexSync(r'businessId', objects, saveLinks: saveLinks);
  }
}

extension AlertTypeIsarQueryWhereSort
    on QueryBuilder<AlertTypeIsar, AlertTypeIsar, QWhere> {
  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension AlertTypeIsarQueryWhere
    on QueryBuilder<AlertTypeIsar, AlertTypeIsar, QWhereClause> {
  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterWhereClause> idEqualTo(
      Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterWhereClause> idNotEqualTo(
      Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterWhereClause> idGreaterThan(
      Id id,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterWhereClause> idLessThan(
      Id id,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterWhereClause> idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterWhereClause>
      businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [null],
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterWhereClause>
      businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'businessId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterWhereClause>
      businessIdEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [businessId],
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterWhereClause>
      businessIdNotEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterWhereClause> nameIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'name',
        value: [null],
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterWhereClause>
      nameIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'name',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterWhereClause> nameEqualTo(
      String? name) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'name',
        value: [name],
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterWhereClause> nameNotEqualTo(
      String? name) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'name',
              lower: [],
              upper: [name],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'name',
              lower: [name],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'name',
              lower: [name],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'name',
              lower: [],
              upper: [name],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterWhereClause>
      farmBusinessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'farmBusinessId',
        value: [null],
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterWhereClause>
      farmBusinessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'farmBusinessId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterWhereClause>
      farmBusinessIdEqualTo(String? farmBusinessId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'farmBusinessId',
        value: [farmBusinessId],
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterWhereClause>
      farmBusinessIdNotEqualTo(String? farmBusinessId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'farmBusinessId',
              lower: [],
              upper: [farmBusinessId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'farmBusinessId',
              lower: [farmBusinessId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'farmBusinessId',
              lower: [farmBusinessId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'farmBusinessId',
              lower: [],
              upper: [farmBusinessId],
              includeUpper: false,
            ));
      }
    });
  }
}

extension AlertTypeIsarQueryFilter
    on QueryBuilder<AlertTypeIsar, AlertTypeIsar, QFilterCondition> {
  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      businessIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      businessIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      businessIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      businessIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'businessId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      businessIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      businessIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      businessIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      businessIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'businessId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      businessIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      businessIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      configIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'config',
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      configIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'config',
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      createdAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      createdAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      createdAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      createdAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      createdAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      createdAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'createdAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      descriptionIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'description',
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      descriptionIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'description',
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      descriptionEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'description',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      descriptionGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'description',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      descriptionLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'description',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      descriptionBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'description',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      descriptionStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'description',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      descriptionEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'description',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      descriptionContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'description',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      descriptionMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'description',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      descriptionIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'description',
        value: '',
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      descriptionIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'description',
        value: '',
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      enabledEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'enabled',
        value: value,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      enabledChannelsElementEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'enabledChannels',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      enabledChannelsElementGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'enabledChannels',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      enabledChannelsElementLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'enabledChannels',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      enabledChannelsElementBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'enabledChannels',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      enabledChannelsElementStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'enabledChannels',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      enabledChannelsElementEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'enabledChannels',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      enabledChannelsElementContains(String value,
          {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'enabledChannels',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      enabledChannelsElementMatches(String pattern,
          {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'enabledChannels',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      enabledChannelsElementIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'enabledChannels',
        value: '',
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      enabledChannelsElementIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'enabledChannels',
        value: '',
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      enabledChannelsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'enabledChannels',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      enabledChannelsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'enabledChannels',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      enabledChannelsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'enabledChannels',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      enabledChannelsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'enabledChannels',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      enabledChannelsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'enabledChannels',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      enabledChannelsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'enabledChannels',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      enabledChannelsStringIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'enabledChannelsString',
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      enabledChannelsStringIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'enabledChannelsString',
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      enabledChannelsStringEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'enabledChannelsString',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      enabledChannelsStringGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'enabledChannelsString',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      enabledChannelsStringLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'enabledChannelsString',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      enabledChannelsStringBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'enabledChannelsString',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      enabledChannelsStringStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'enabledChannelsString',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      enabledChannelsStringEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'enabledChannelsString',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      enabledChannelsStringContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'enabledChannelsString',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      enabledChannelsStringMatches(String pattern,
          {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'enabledChannelsString',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      enabledChannelsStringIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'enabledChannelsString',
        value: '',
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      enabledChannelsStringIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'enabledChannelsString',
        value: '',
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      farmBusinessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'farmBusinessId',
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      farmBusinessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'farmBusinessId',
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      farmBusinessIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      farmBusinessIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      farmBusinessIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      farmBusinessIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'farmBusinessId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      farmBusinessIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      farmBusinessIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      farmBusinessIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      farmBusinessIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'farmBusinessId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      farmBusinessIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'farmBusinessId',
        value: '',
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      farmBusinessIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'farmBusinessId',
        value: '',
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition> idEqualTo(
      Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition> idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition> idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      nameIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'name',
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      nameIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'name',
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition> nameEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      nameGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      nameLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition> nameBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'name',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      nameStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      nameEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      nameContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition> nameMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'name',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      nameIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'name',
        value: '',
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      nameIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'name',
        value: '',
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      updatedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      updatedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      updatedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      updatedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      updatedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition>
      updatedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'updatedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension AlertTypeIsarQueryObject
    on QueryBuilder<AlertTypeIsar, AlertTypeIsar, QFilterCondition> {
  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterFilterCondition> config(
      FilterQuery<ConfigMap> q) {
    return QueryBuilder.apply(this, (query) {
      return query.object(q, r'config');
    });
  }
}

extension AlertTypeIsarQueryLinks
    on QueryBuilder<AlertTypeIsar, AlertTypeIsar, QFilterCondition> {}

extension AlertTypeIsarQuerySortBy
    on QueryBuilder<AlertTypeIsar, AlertTypeIsar, QSortBy> {
  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterSortBy> sortByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterSortBy>
      sortByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterSortBy> sortByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterSortBy>
      sortByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterSortBy> sortByDescription() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'description', Sort.asc);
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterSortBy>
      sortByDescriptionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'description', Sort.desc);
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterSortBy> sortByEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'enabled', Sort.asc);
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterSortBy> sortByEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'enabled', Sort.desc);
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterSortBy>
      sortByEnabledChannelsString() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'enabledChannelsString', Sort.asc);
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterSortBy>
      sortByEnabledChannelsStringDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'enabledChannelsString', Sort.desc);
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterSortBy>
      sortByFarmBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'farmBusinessId', Sort.asc);
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterSortBy>
      sortByFarmBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'farmBusinessId', Sort.desc);
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterSortBy> sortByName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.asc);
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterSortBy> sortByNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.desc);
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterSortBy> sortByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterSortBy>
      sortByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }
}

extension AlertTypeIsarQuerySortThenBy
    on QueryBuilder<AlertTypeIsar, AlertTypeIsar, QSortThenBy> {
  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterSortBy> thenByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterSortBy>
      thenByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterSortBy> thenByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterSortBy>
      thenByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterSortBy> thenByDescription() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'description', Sort.asc);
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterSortBy>
      thenByDescriptionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'description', Sort.desc);
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterSortBy> thenByEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'enabled', Sort.asc);
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterSortBy> thenByEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'enabled', Sort.desc);
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterSortBy>
      thenByEnabledChannelsString() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'enabledChannelsString', Sort.asc);
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterSortBy>
      thenByEnabledChannelsStringDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'enabledChannelsString', Sort.desc);
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterSortBy>
      thenByFarmBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'farmBusinessId', Sort.asc);
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterSortBy>
      thenByFarmBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'farmBusinessId', Sort.desc);
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterSortBy> thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterSortBy> thenByName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.asc);
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterSortBy> thenByNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.desc);
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterSortBy> thenByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QAfterSortBy>
      thenByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }
}

extension AlertTypeIsarQueryWhereDistinct
    on QueryBuilder<AlertTypeIsar, AlertTypeIsar, QDistinct> {
  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QDistinct> distinctByBusinessId(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'businessId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QDistinct> distinctByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'createdAt');
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QDistinct> distinctByDescription(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'description', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QDistinct> distinctByEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'enabled');
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QDistinct>
      distinctByEnabledChannels() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'enabledChannels');
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QDistinct>
      distinctByEnabledChannelsString({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'enabledChannelsString',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QDistinct>
      distinctByFarmBusinessId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'farmBusinessId',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QDistinct> distinctByName(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'name', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<AlertTypeIsar, AlertTypeIsar, QDistinct> distinctByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'updatedAt');
    });
  }
}

extension AlertTypeIsarQueryProperty
    on QueryBuilder<AlertTypeIsar, AlertTypeIsar, QQueryProperty> {
  QueryBuilder<AlertTypeIsar, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<AlertTypeIsar, String?, QQueryOperations> businessIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'businessId');
    });
  }

  QueryBuilder<AlertTypeIsar, ConfigMap?, QQueryOperations> configProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'config');
    });
  }

  QueryBuilder<AlertTypeIsar, DateTime?, QQueryOperations> createdAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'createdAt');
    });
  }

  QueryBuilder<AlertTypeIsar, String?, QQueryOperations> descriptionProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'description');
    });
  }

  QueryBuilder<AlertTypeIsar, bool, QQueryOperations> enabledProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'enabled');
    });
  }

  QueryBuilder<AlertTypeIsar, List<String>, QQueryOperations>
      enabledChannelsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'enabledChannels');
    });
  }

  QueryBuilder<AlertTypeIsar, String?, QQueryOperations>
      enabledChannelsStringProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'enabledChannelsString');
    });
  }

  QueryBuilder<AlertTypeIsar, String?, QQueryOperations>
      farmBusinessIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'farmBusinessId');
    });
  }

  QueryBuilder<AlertTypeIsar, String?, QQueryOperations> nameProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'name');
    });
  }

  QueryBuilder<AlertTypeIsar, DateTime?, QQueryOperations> updatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'updatedAt');
    });
  }
}

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetAlertSettingsIsarCollection on Isar {
  IsarCollection<AlertSettingsIsar> get alertSettingsIsars => this.collection();
}

const AlertSettingsIsarSchema = CollectionSchema(
  name: r'AlertSettingsIsar',
  id: 5348586387688716131,
  properties: {
    r'allAlertsEnabled': PropertySchema(
      id: 0,
      name: r'allAlertsEnabled',
      type: IsarType.bool,
    ),
    r'businessId': PropertySchema(
      id: 1,
      name: r'businessId',
      type: IsarType.string,
    ),
    r'calvingAlerts': PropertySchema(
      id: 2,
      name: r'calvingAlerts',
      type: IsarType.bool,
    ),
    r'createdAt': PropertySchema(
      id: 3,
      name: r'createdAt',
      type: IsarType.dateTime,
    ),
    r'dewormingAlerts': PropertySchema(
      id: 4,
      name: r'dewormingAlerts',
      type: IsarType.bool,
    ),
    r'enabled': PropertySchema(
      id: 5,
      name: r'enabled',
      type: IsarType.bool,
    ),
    r'farmBusinessId': PropertySchema(
      id: 6,
      name: r'farmBusinessId',
      type: IsarType.string,
    ),
    r'healthCheckAlerts': PropertySchema(
      id: 7,
      name: r'healthCheckAlerts',
      type: IsarType.bool,
    ),
    r'heatDetectionAlerts': PropertySchema(
      id: 8,
      name: r'heatDetectionAlerts',
      type: IsarType.bool,
    ),
    r'inAppNotifications': PropertySchema(
      id: 9,
      name: r'inAppNotifications',
      type: IsarType.bool,
    ),
    r'milkProductionAlerts': PropertySchema(
      id: 10,
      name: r'milkProductionAlerts',
      type: IsarType.bool,
    ),
    r'name': PropertySchema(
      id: 11,
      name: r'name',
      type: IsarType.string,
    ),
    r'pregnancyCheckAlerts': PropertySchema(
      id: 12,
      name: r'pregnancyCheckAlerts',
      type: IsarType.bool,
    ),
    r'pushNotifications': PropertySchema(
      id: 13,
      name: r'pushNotifications',
      type: IsarType.bool,
    ),
    r'selectedAnimals': PropertySchema(
      id: 14,
      name: r'selectedAnimals',
      type: IsarType.stringList,
    ),
    r'selectedAnimalsString': PropertySchema(
      id: 15,
      name: r'selectedAnimalsString',
      type: IsarType.string,
    ),
    r'selectedCategories': PropertySchema(
      id: 16,
      name: r'selectedCategories',
      type: IsarType.stringList,
    ),
    r'selectedCategoriesString': PropertySchema(
      id: 17,
      name: r'selectedCategoriesString',
      type: IsarType.string,
    ),
    r'updatedAt': PropertySchema(
      id: 18,
      name: r'updatedAt',
      type: IsarType.dateTime,
    ),
    r'vaccinationAlerts': PropertySchema(
      id: 19,
      name: r'vaccinationAlerts',
      type: IsarType.bool,
    ),
    r'weightChangeAlerts': PropertySchema(
      id: 20,
      name: r'weightChangeAlerts',
      type: IsarType.bool,
    )
  },
  estimateSize: _alertSettingsIsarEstimateSize,
  serialize: _alertSettingsIsarSerialize,
  deserialize: _alertSettingsIsarDeserialize,
  deserializeProp: _alertSettingsIsarDeserializeProp,
  idName: r'id',
  indexes: {
    r'businessId': IndexSchema(
      id: 2228048290814354584,
      name: r'businessId',
      unique: true,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'businessId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'name': IndexSchema(
      id: 879695947855722453,
      name: r'name',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'name',
          type: IndexType.hash,
          caseSensitive: false,
        )
      ],
    ),
    r'farmBusinessId': IndexSchema(
      id: -7111559275757090744,
      name: r'farmBusinessId',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'farmBusinessId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    )
  },
  links: {},
  embeddedSchemas: {},
  getId: _alertSettingsIsarGetId,
  getLinks: _alertSettingsIsarGetLinks,
  attach: _alertSettingsIsarAttach,
  version: '3.1.0+1',
);

int _alertSettingsIsarEstimateSize(
  AlertSettingsIsar object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.businessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.farmBusinessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.name;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  bytesCount += 3 + object.selectedAnimals.length * 3;
  {
    for (var i = 0; i < object.selectedAnimals.length; i++) {
      final value = object.selectedAnimals[i];
      bytesCount += value.length * 3;
    }
  }
  {
    final value = object.selectedAnimalsString;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  bytesCount += 3 + object.selectedCategories.length * 3;
  {
    for (var i = 0; i < object.selectedCategories.length; i++) {
      final value = object.selectedCategories[i];
      bytesCount += value.length * 3;
    }
  }
  {
    final value = object.selectedCategoriesString;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _alertSettingsIsarSerialize(
  AlertSettingsIsar object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeBool(offsets[0], object.allAlertsEnabled);
  writer.writeString(offsets[1], object.businessId);
  writer.writeBool(offsets[2], object.calvingAlerts);
  writer.writeDateTime(offsets[3], object.createdAt);
  writer.writeBool(offsets[4], object.dewormingAlerts);
  writer.writeBool(offsets[5], object.enabled);
  writer.writeString(offsets[6], object.farmBusinessId);
  writer.writeBool(offsets[7], object.healthCheckAlerts);
  writer.writeBool(offsets[8], object.heatDetectionAlerts);
  writer.writeBool(offsets[9], object.inAppNotifications);
  writer.writeBool(offsets[10], object.milkProductionAlerts);
  writer.writeString(offsets[11], object.name);
  writer.writeBool(offsets[12], object.pregnancyCheckAlerts);
  writer.writeBool(offsets[13], object.pushNotifications);
  writer.writeStringList(offsets[14], object.selectedAnimals);
  writer.writeString(offsets[15], object.selectedAnimalsString);
  writer.writeStringList(offsets[16], object.selectedCategories);
  writer.writeString(offsets[17], object.selectedCategoriesString);
  writer.writeDateTime(offsets[18], object.updatedAt);
  writer.writeBool(offsets[19], object.vaccinationAlerts);
  writer.writeBool(offsets[20], object.weightChangeAlerts);
}

AlertSettingsIsar _alertSettingsIsarDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = AlertSettingsIsar();
  object.allAlertsEnabled = reader.readBool(offsets[0]);
  object.businessId = reader.readStringOrNull(offsets[1]);
  object.calvingAlerts = reader.readBool(offsets[2]);
  object.createdAt = reader.readDateTimeOrNull(offsets[3]);
  object.dewormingAlerts = reader.readBool(offsets[4]);
  object.enabled = reader.readBool(offsets[5]);
  object.farmBusinessId = reader.readStringOrNull(offsets[6]);
  object.healthCheckAlerts = reader.readBool(offsets[7]);
  object.heatDetectionAlerts = reader.readBool(offsets[8]);
  object.id = id;
  object.inAppNotifications = reader.readBool(offsets[9]);
  object.milkProductionAlerts = reader.readBool(offsets[10]);
  object.name = reader.readStringOrNull(offsets[11]);
  object.pregnancyCheckAlerts = reader.readBool(offsets[12]);
  object.pushNotifications = reader.readBool(offsets[13]);
  object.selectedAnimals = reader.readStringList(offsets[14]) ?? [];
  object.selectedAnimalsString = reader.readStringOrNull(offsets[15]);
  object.selectedCategories = reader.readStringList(offsets[16]) ?? [];
  object.selectedCategoriesString = reader.readStringOrNull(offsets[17]);
  object.updatedAt = reader.readDateTimeOrNull(offsets[18]);
  object.vaccinationAlerts = reader.readBool(offsets[19]);
  object.weightChangeAlerts = reader.readBool(offsets[20]);
  return object;
}

P _alertSettingsIsarDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readBool(offset)) as P;
    case 1:
      return (reader.readStringOrNull(offset)) as P;
    case 2:
      return (reader.readBool(offset)) as P;
    case 3:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 4:
      return (reader.readBool(offset)) as P;
    case 5:
      return (reader.readBool(offset)) as P;
    case 6:
      return (reader.readStringOrNull(offset)) as P;
    case 7:
      return (reader.readBool(offset)) as P;
    case 8:
      return (reader.readBool(offset)) as P;
    case 9:
      return (reader.readBool(offset)) as P;
    case 10:
      return (reader.readBool(offset)) as P;
    case 11:
      return (reader.readStringOrNull(offset)) as P;
    case 12:
      return (reader.readBool(offset)) as P;
    case 13:
      return (reader.readBool(offset)) as P;
    case 14:
      return (reader.readStringList(offset) ?? []) as P;
    case 15:
      return (reader.readStringOrNull(offset)) as P;
    case 16:
      return (reader.readStringList(offset) ?? []) as P;
    case 17:
      return (reader.readStringOrNull(offset)) as P;
    case 18:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 19:
      return (reader.readBool(offset)) as P;
    case 20:
      return (reader.readBool(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _alertSettingsIsarGetId(AlertSettingsIsar object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _alertSettingsIsarGetLinks(
    AlertSettingsIsar object) {
  return [];
}

void _alertSettingsIsarAttach(
    IsarCollection<dynamic> col, Id id, AlertSettingsIsar object) {
  object.id = id;
}

extension AlertSettingsIsarByIndex on IsarCollection<AlertSettingsIsar> {
  Future<AlertSettingsIsar?> getByBusinessId(String? businessId) {
    return getByIndex(r'businessId', [businessId]);
  }

  AlertSettingsIsar? getByBusinessIdSync(String? businessId) {
    return getByIndexSync(r'businessId', [businessId]);
  }

  Future<bool> deleteByBusinessId(String? businessId) {
    return deleteByIndex(r'businessId', [businessId]);
  }

  bool deleteByBusinessIdSync(String? businessId) {
    return deleteByIndexSync(r'businessId', [businessId]);
  }

  Future<List<AlertSettingsIsar?>> getAllByBusinessId(
      List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndex(r'businessId', values);
  }

  List<AlertSettingsIsar?> getAllByBusinessIdSync(
      List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndexSync(r'businessId', values);
  }

  Future<int> deleteAllByBusinessId(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndex(r'businessId', values);
  }

  int deleteAllByBusinessIdSync(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndexSync(r'businessId', values);
  }

  Future<Id> putByBusinessId(AlertSettingsIsar object) {
    return putByIndex(r'businessId', object);
  }

  Id putByBusinessIdSync(AlertSettingsIsar object, {bool saveLinks = true}) {
    return putByIndexSync(r'businessId', object, saveLinks: saveLinks);
  }

  Future<List<Id>> putAllByBusinessId(List<AlertSettingsIsar> objects) {
    return putAllByIndex(r'businessId', objects);
  }

  List<Id> putAllByBusinessIdSync(List<AlertSettingsIsar> objects,
      {bool saveLinks = true}) {
    return putAllByIndexSync(r'businessId', objects, saveLinks: saveLinks);
  }
}

extension AlertSettingsIsarQueryWhereSort
    on QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QWhere> {
  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension AlertSettingsIsarQueryWhere
    on QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QWhereClause> {
  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterWhereClause>
      idEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterWhereClause>
      idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterWhereClause>
      idGreaterThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterWhereClause>
      idLessThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterWhereClause>
      idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterWhereClause>
      businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [null],
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterWhereClause>
      businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'businessId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterWhereClause>
      businessIdEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [businessId],
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterWhereClause>
      businessIdNotEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterWhereClause>
      nameIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'name',
        value: [null],
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterWhereClause>
      nameIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'name',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterWhereClause>
      nameEqualTo(String? name) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'name',
        value: [name],
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterWhereClause>
      nameNotEqualTo(String? name) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'name',
              lower: [],
              upper: [name],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'name',
              lower: [name],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'name',
              lower: [name],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'name',
              lower: [],
              upper: [name],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterWhereClause>
      farmBusinessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'farmBusinessId',
        value: [null],
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterWhereClause>
      farmBusinessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'farmBusinessId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterWhereClause>
      farmBusinessIdEqualTo(String? farmBusinessId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'farmBusinessId',
        value: [farmBusinessId],
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterWhereClause>
      farmBusinessIdNotEqualTo(String? farmBusinessId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'farmBusinessId',
              lower: [],
              upper: [farmBusinessId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'farmBusinessId',
              lower: [farmBusinessId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'farmBusinessId',
              lower: [farmBusinessId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'farmBusinessId',
              lower: [],
              upper: [farmBusinessId],
              includeUpper: false,
            ));
      }
    });
  }
}

extension AlertSettingsIsarQueryFilter
    on QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QFilterCondition> {
  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      allAlertsEnabledEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'allAlertsEnabled',
        value: value,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      businessIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      businessIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      businessIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      businessIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'businessId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      businessIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      businessIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      businessIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      businessIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'businessId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      businessIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      businessIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      calvingAlertsEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'calvingAlerts',
        value: value,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      createdAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      createdAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      createdAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      createdAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      createdAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      createdAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'createdAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      dewormingAlertsEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'dewormingAlerts',
        value: value,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      enabledEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'enabled',
        value: value,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      farmBusinessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'farmBusinessId',
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      farmBusinessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'farmBusinessId',
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      farmBusinessIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      farmBusinessIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      farmBusinessIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      farmBusinessIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'farmBusinessId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      farmBusinessIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      farmBusinessIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      farmBusinessIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      farmBusinessIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'farmBusinessId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      farmBusinessIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'farmBusinessId',
        value: '',
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      farmBusinessIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'farmBusinessId',
        value: '',
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      healthCheckAlertsEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'healthCheckAlerts',
        value: value,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      heatDetectionAlertsEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'heatDetectionAlerts',
        value: value,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      idEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      inAppNotificationsEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'inAppNotifications',
        value: value,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      milkProductionAlertsEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'milkProductionAlerts',
        value: value,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      nameIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'name',
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      nameIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'name',
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      nameEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      nameGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      nameLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      nameBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'name',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      nameStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      nameEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      nameContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      nameMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'name',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      nameIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'name',
        value: '',
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      nameIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'name',
        value: '',
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      pregnancyCheckAlertsEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'pregnancyCheckAlerts',
        value: value,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      pushNotificationsEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'pushNotifications',
        value: value,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedAnimalsElementEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'selectedAnimals',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedAnimalsElementGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'selectedAnimals',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedAnimalsElementLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'selectedAnimals',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedAnimalsElementBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'selectedAnimals',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedAnimalsElementStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'selectedAnimals',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedAnimalsElementEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'selectedAnimals',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedAnimalsElementContains(String value,
          {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'selectedAnimals',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedAnimalsElementMatches(String pattern,
          {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'selectedAnimals',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedAnimalsElementIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'selectedAnimals',
        value: '',
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedAnimalsElementIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'selectedAnimals',
        value: '',
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedAnimalsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'selectedAnimals',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedAnimalsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'selectedAnimals',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedAnimalsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'selectedAnimals',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedAnimalsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'selectedAnimals',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedAnimalsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'selectedAnimals',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedAnimalsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'selectedAnimals',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedAnimalsStringIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'selectedAnimalsString',
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedAnimalsStringIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'selectedAnimalsString',
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedAnimalsStringEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'selectedAnimalsString',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedAnimalsStringGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'selectedAnimalsString',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedAnimalsStringLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'selectedAnimalsString',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedAnimalsStringBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'selectedAnimalsString',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedAnimalsStringStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'selectedAnimalsString',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedAnimalsStringEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'selectedAnimalsString',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedAnimalsStringContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'selectedAnimalsString',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedAnimalsStringMatches(String pattern,
          {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'selectedAnimalsString',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedAnimalsStringIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'selectedAnimalsString',
        value: '',
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedAnimalsStringIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'selectedAnimalsString',
        value: '',
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedCategoriesElementEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'selectedCategories',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedCategoriesElementGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'selectedCategories',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedCategoriesElementLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'selectedCategories',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedCategoriesElementBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'selectedCategories',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedCategoriesElementStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'selectedCategories',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedCategoriesElementEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'selectedCategories',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedCategoriesElementContains(String value,
          {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'selectedCategories',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedCategoriesElementMatches(String pattern,
          {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'selectedCategories',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedCategoriesElementIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'selectedCategories',
        value: '',
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedCategoriesElementIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'selectedCategories',
        value: '',
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedCategoriesLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'selectedCategories',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedCategoriesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'selectedCategories',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedCategoriesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'selectedCategories',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedCategoriesLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'selectedCategories',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedCategoriesLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'selectedCategories',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedCategoriesLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'selectedCategories',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedCategoriesStringIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'selectedCategoriesString',
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedCategoriesStringIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'selectedCategoriesString',
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedCategoriesStringEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'selectedCategoriesString',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedCategoriesStringGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'selectedCategoriesString',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedCategoriesStringLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'selectedCategoriesString',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedCategoriesStringBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'selectedCategoriesString',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedCategoriesStringStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'selectedCategoriesString',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedCategoriesStringEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'selectedCategoriesString',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedCategoriesStringContains(String value,
          {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'selectedCategoriesString',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedCategoriesStringMatches(String pattern,
          {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'selectedCategoriesString',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedCategoriesStringIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'selectedCategoriesString',
        value: '',
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      selectedCategoriesStringIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'selectedCategoriesString',
        value: '',
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      updatedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      updatedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      updatedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      updatedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      updatedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      updatedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'updatedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      vaccinationAlertsEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'vaccinationAlerts',
        value: value,
      ));
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterFilterCondition>
      weightChangeAlertsEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'weightChangeAlerts',
        value: value,
      ));
    });
  }
}

extension AlertSettingsIsarQueryObject
    on QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QFilterCondition> {}

extension AlertSettingsIsarQueryLinks
    on QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QFilterCondition> {}

extension AlertSettingsIsarQuerySortBy
    on QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QSortBy> {
  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      sortByAllAlertsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'allAlertsEnabled', Sort.asc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      sortByAllAlertsEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'allAlertsEnabled', Sort.desc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      sortByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      sortByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      sortByCalvingAlerts() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'calvingAlerts', Sort.asc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      sortByCalvingAlertsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'calvingAlerts', Sort.desc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      sortByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      sortByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      sortByDewormingAlerts() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dewormingAlerts', Sort.asc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      sortByDewormingAlertsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dewormingAlerts', Sort.desc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      sortByEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'enabled', Sort.asc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      sortByEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'enabled', Sort.desc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      sortByFarmBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'farmBusinessId', Sort.asc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      sortByFarmBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'farmBusinessId', Sort.desc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      sortByHealthCheckAlerts() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'healthCheckAlerts', Sort.asc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      sortByHealthCheckAlertsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'healthCheckAlerts', Sort.desc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      sortByHeatDetectionAlerts() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'heatDetectionAlerts', Sort.asc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      sortByHeatDetectionAlertsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'heatDetectionAlerts', Sort.desc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      sortByInAppNotifications() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'inAppNotifications', Sort.asc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      sortByInAppNotificationsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'inAppNotifications', Sort.desc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      sortByMilkProductionAlerts() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'milkProductionAlerts', Sort.asc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      sortByMilkProductionAlertsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'milkProductionAlerts', Sort.desc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      sortByName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.asc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      sortByNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.desc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      sortByPregnancyCheckAlerts() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pregnancyCheckAlerts', Sort.asc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      sortByPregnancyCheckAlertsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pregnancyCheckAlerts', Sort.desc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      sortByPushNotifications() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pushNotifications', Sort.asc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      sortByPushNotificationsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pushNotifications', Sort.desc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      sortBySelectedAnimalsString() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'selectedAnimalsString', Sort.asc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      sortBySelectedAnimalsStringDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'selectedAnimalsString', Sort.desc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      sortBySelectedCategoriesString() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'selectedCategoriesString', Sort.asc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      sortBySelectedCategoriesStringDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'selectedCategoriesString', Sort.desc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      sortByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      sortByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      sortByVaccinationAlerts() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'vaccinationAlerts', Sort.asc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      sortByVaccinationAlertsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'vaccinationAlerts', Sort.desc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      sortByWeightChangeAlerts() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weightChangeAlerts', Sort.asc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      sortByWeightChangeAlertsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weightChangeAlerts', Sort.desc);
    });
  }
}

extension AlertSettingsIsarQuerySortThenBy
    on QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QSortThenBy> {
  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      thenByAllAlertsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'allAlertsEnabled', Sort.asc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      thenByAllAlertsEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'allAlertsEnabled', Sort.desc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      thenByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      thenByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      thenByCalvingAlerts() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'calvingAlerts', Sort.asc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      thenByCalvingAlertsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'calvingAlerts', Sort.desc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      thenByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      thenByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      thenByDewormingAlerts() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dewormingAlerts', Sort.asc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      thenByDewormingAlertsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dewormingAlerts', Sort.desc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      thenByEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'enabled', Sort.asc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      thenByEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'enabled', Sort.desc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      thenByFarmBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'farmBusinessId', Sort.asc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      thenByFarmBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'farmBusinessId', Sort.desc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      thenByHealthCheckAlerts() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'healthCheckAlerts', Sort.asc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      thenByHealthCheckAlertsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'healthCheckAlerts', Sort.desc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      thenByHeatDetectionAlerts() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'heatDetectionAlerts', Sort.asc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      thenByHeatDetectionAlertsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'heatDetectionAlerts', Sort.desc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      thenByInAppNotifications() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'inAppNotifications', Sort.asc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      thenByInAppNotificationsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'inAppNotifications', Sort.desc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      thenByMilkProductionAlerts() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'milkProductionAlerts', Sort.asc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      thenByMilkProductionAlertsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'milkProductionAlerts', Sort.desc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      thenByName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.asc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      thenByNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.desc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      thenByPregnancyCheckAlerts() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pregnancyCheckAlerts', Sort.asc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      thenByPregnancyCheckAlertsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pregnancyCheckAlerts', Sort.desc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      thenByPushNotifications() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pushNotifications', Sort.asc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      thenByPushNotificationsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pushNotifications', Sort.desc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      thenBySelectedAnimalsString() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'selectedAnimalsString', Sort.asc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      thenBySelectedAnimalsStringDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'selectedAnimalsString', Sort.desc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      thenBySelectedCategoriesString() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'selectedCategoriesString', Sort.asc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      thenBySelectedCategoriesStringDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'selectedCategoriesString', Sort.desc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      thenByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      thenByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      thenByVaccinationAlerts() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'vaccinationAlerts', Sort.asc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      thenByVaccinationAlertsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'vaccinationAlerts', Sort.desc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      thenByWeightChangeAlerts() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weightChangeAlerts', Sort.asc);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QAfterSortBy>
      thenByWeightChangeAlertsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weightChangeAlerts', Sort.desc);
    });
  }
}

extension AlertSettingsIsarQueryWhereDistinct
    on QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QDistinct> {
  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QDistinct>
      distinctByAllAlertsEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'allAlertsEnabled');
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QDistinct>
      distinctByBusinessId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'businessId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QDistinct>
      distinctByCalvingAlerts() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'calvingAlerts');
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QDistinct>
      distinctByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'createdAt');
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QDistinct>
      distinctByDewormingAlerts() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'dewormingAlerts');
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QDistinct>
      distinctByEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'enabled');
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QDistinct>
      distinctByFarmBusinessId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'farmBusinessId',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QDistinct>
      distinctByHealthCheckAlerts() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'healthCheckAlerts');
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QDistinct>
      distinctByHeatDetectionAlerts() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'heatDetectionAlerts');
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QDistinct>
      distinctByInAppNotifications() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'inAppNotifications');
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QDistinct>
      distinctByMilkProductionAlerts() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'milkProductionAlerts');
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QDistinct> distinctByName(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'name', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QDistinct>
      distinctByPregnancyCheckAlerts() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'pregnancyCheckAlerts');
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QDistinct>
      distinctByPushNotifications() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'pushNotifications');
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QDistinct>
      distinctBySelectedAnimals() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'selectedAnimals');
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QDistinct>
      distinctBySelectedAnimalsString({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'selectedAnimalsString',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QDistinct>
      distinctBySelectedCategories() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'selectedCategories');
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QDistinct>
      distinctBySelectedCategoriesString({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'selectedCategoriesString',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QDistinct>
      distinctByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'updatedAt');
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QDistinct>
      distinctByVaccinationAlerts() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'vaccinationAlerts');
    });
  }

  QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QDistinct>
      distinctByWeightChangeAlerts() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'weightChangeAlerts');
    });
  }
}

extension AlertSettingsIsarQueryProperty
    on QueryBuilder<AlertSettingsIsar, AlertSettingsIsar, QQueryProperty> {
  QueryBuilder<AlertSettingsIsar, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<AlertSettingsIsar, bool, QQueryOperations>
      allAlertsEnabledProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'allAlertsEnabled');
    });
  }

  QueryBuilder<AlertSettingsIsar, String?, QQueryOperations>
      businessIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'businessId');
    });
  }

  QueryBuilder<AlertSettingsIsar, bool, QQueryOperations>
      calvingAlertsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'calvingAlerts');
    });
  }

  QueryBuilder<AlertSettingsIsar, DateTime?, QQueryOperations>
      createdAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'createdAt');
    });
  }

  QueryBuilder<AlertSettingsIsar, bool, QQueryOperations>
      dewormingAlertsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'dewormingAlerts');
    });
  }

  QueryBuilder<AlertSettingsIsar, bool, QQueryOperations> enabledProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'enabled');
    });
  }

  QueryBuilder<AlertSettingsIsar, String?, QQueryOperations>
      farmBusinessIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'farmBusinessId');
    });
  }

  QueryBuilder<AlertSettingsIsar, bool, QQueryOperations>
      healthCheckAlertsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'healthCheckAlerts');
    });
  }

  QueryBuilder<AlertSettingsIsar, bool, QQueryOperations>
      heatDetectionAlertsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'heatDetectionAlerts');
    });
  }

  QueryBuilder<AlertSettingsIsar, bool, QQueryOperations>
      inAppNotificationsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'inAppNotifications');
    });
  }

  QueryBuilder<AlertSettingsIsar, bool, QQueryOperations>
      milkProductionAlertsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'milkProductionAlerts');
    });
  }

  QueryBuilder<AlertSettingsIsar, String?, QQueryOperations> nameProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'name');
    });
  }

  QueryBuilder<AlertSettingsIsar, bool, QQueryOperations>
      pregnancyCheckAlertsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'pregnancyCheckAlerts');
    });
  }

  QueryBuilder<AlertSettingsIsar, bool, QQueryOperations>
      pushNotificationsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'pushNotifications');
    });
  }

  QueryBuilder<AlertSettingsIsar, List<String>, QQueryOperations>
      selectedAnimalsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'selectedAnimals');
    });
  }

  QueryBuilder<AlertSettingsIsar, String?, QQueryOperations>
      selectedAnimalsStringProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'selectedAnimalsString');
    });
  }

  QueryBuilder<AlertSettingsIsar, List<String>, QQueryOperations>
      selectedCategoriesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'selectedCategories');
    });
  }

  QueryBuilder<AlertSettingsIsar, String?, QQueryOperations>
      selectedCategoriesStringProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'selectedCategoriesString');
    });
  }

  QueryBuilder<AlertSettingsIsar, DateTime?, QQueryOperations>
      updatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'updatedAt');
    });
  }

  QueryBuilder<AlertSettingsIsar, bool, QQueryOperations>
      vaccinationAlertsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'vaccinationAlerts');
    });
  }

  QueryBuilder<AlertSettingsIsar, bool, QQueryOperations>
      weightChangeAlertsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'weightChangeAlerts');
    });
  }
}

// **************************************************************************
// IsarEmbeddedGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

const ConfigMapSchema = Schema(
  name: r'ConfigMap',
  id: -6226057937053321520,
  properties: {
    r'jsonData': PropertySchema(
      id: 0,
      name: r'jsonData',
      type: IsarType.string,
    )
  },
  estimateSize: _configMapEstimateSize,
  serialize: _configMapSerialize,
  deserialize: _configMapDeserialize,
  deserializeProp: _configMapDeserializeProp,
);

int _configMapEstimateSize(
  ConfigMap object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.jsonData;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _configMapSerialize(
  ConfigMap object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeString(offsets[0], object.jsonData);
}

ConfigMap _configMapDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = ConfigMap();
  object.jsonData = reader.readStringOrNull(offsets[0]);
  return object;
}

P _configMapDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readStringOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

extension ConfigMapQueryFilter
    on QueryBuilder<ConfigMap, ConfigMap, QFilterCondition> {
  QueryBuilder<ConfigMap, ConfigMap, QAfterFilterCondition> jsonDataIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'jsonData',
      ));
    });
  }

  QueryBuilder<ConfigMap, ConfigMap, QAfterFilterCondition>
      jsonDataIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'jsonData',
      ));
    });
  }

  QueryBuilder<ConfigMap, ConfigMap, QAfterFilterCondition> jsonDataEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'jsonData',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<ConfigMap, ConfigMap, QAfterFilterCondition> jsonDataGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'jsonData',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<ConfigMap, ConfigMap, QAfterFilterCondition> jsonDataLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'jsonData',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<ConfigMap, ConfigMap, QAfterFilterCondition> jsonDataBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'jsonData',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<ConfigMap, ConfigMap, QAfterFilterCondition> jsonDataStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'jsonData',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<ConfigMap, ConfigMap, QAfterFilterCondition> jsonDataEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'jsonData',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<ConfigMap, ConfigMap, QAfterFilterCondition> jsonDataContains(
      String value,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'jsonData',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<ConfigMap, ConfigMap, QAfterFilterCondition> jsonDataMatches(
      String pattern,
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'jsonData',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<ConfigMap, ConfigMap, QAfterFilterCondition> jsonDataIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'jsonData',
        value: '',
      ));
    });
  }

  QueryBuilder<ConfigMap, ConfigMap, QAfterFilterCondition>
      jsonDataIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'jsonData',
        value: '',
      ));
    });
  }
}

extension ConfigMapQueryObject
    on QueryBuilder<ConfigMap, ConfigMap, QFilterCondition> {}
