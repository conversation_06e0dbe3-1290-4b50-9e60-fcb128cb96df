import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get_it/get_it.dart';
import 'package:provider/provider.dart';
import 'Dashboard/dashboard_screen.dart';
import 'theme/app_theme.dart';
import 'services/logging_service.dart';
import 'core/dependency_injection.dart';
import 'core/default_data_seeder.dart';
import 'routes/app_routes.dart';
// import 'Dashboard/Reports/screens/transactions_report_screen.dart';
// import 'Dashboard/Reports/screens/milk_report_screen.dart';
// import 'Dashboard/Reports/screens/events_report_screen.dart';
// import 'Dashboard/Reports/screens/breeding_report_screen.dart';
// import 'Dashboard/Reports/screens/cattle_report_screen.dart';
import 'Dashboard/Cattle/screens/cattle_screen.dart';
import 'Dashboard/Cattle/controllers/cattle_controller.dart';
import 'Dashboard/Breeding/screens/breeding_screen.dart';
import 'Dashboard/Events/screens/events_screen.dart';
import 'Dashboard/Health/screens/health_screen.dart';
import 'Dashboard/Milk Records/screens/milk_screen.dart';
import 'Dashboard/Transactions/screens/transactions_screen.dart';
// import 'Dashboard/Farm Setup/screens/farm_setup_screen.dart';
// import 'Dashboard/Reports/screens/weight_report_screen.dart';
// import 'Dashboard/Reports/screens/pregnancies_report_screen.dart';
// import 'Dashboard/Reports/screens/health_report_screen.dart';
import 'Dashboard/Farm Setup/services/farm_setup_repository.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Logging Service
  final loggingService = LoggingService();
  loggingService.setupLogging();
  loggingService.logUncaughtExceptions();

  // Initialize dependencies using the new DI system
  bool dependenciesInitialized = false;

  try {
    // Initialize all dependencies (replaces IsarInitializer)
    loggingService.info("Initializing dependencies...");
    await DependencyInjection.initDependencies();
    dependenciesInitialized = true;
    loggingService.info("Dependencies initialization succeeded");

    if (dependenciesInitialized) {
      // Get services from GetIt (using new DI pattern)
      final getIt = GetIt.instance;

      // Seed default data using the new safe seeder from DI
      loggingService.info("Seeding default data...");
      final dataSeeder = getIt<DefaultDataSeeder>();
      await dataSeeder.seedInitialData();
      loggingService.info("Default data seeding completed");

      final farmSetupRepository = getIt<FarmSetupRepository>();

      // Log database information for debugging
      final animalTypes = await farmSetupRepository.getAllAnimalTypes();
      loggingService
          .info('Loaded ${animalTypes.length} animal types from database');

      final breedCategories = await farmSetupRepository.getAllBreedCategories();
      loggingService
          .info('Loaded ${breedCategories.length} breed categories from database');

      final farms = await farmSetupRepository.getAllFarms();
      if (farms.isNotEmpty) {
        loggingService.info('Loaded farm: ${farms.first.name}');
      } else {
        loggingService.info('No farm found in database');
      }


    }
  } catch (e) {
    loggingService.error("Error initializing application: $e");
    dependenciesInitialized = false;
  }

  runApp(CattleManagerApp(
    isarInitialized: dependenciesInitialized,
  ));
}

class CattleManagerApp extends StatefulWidget {
  final bool isarInitialized;

  const CattleManagerApp({
    super.key,
    required this.isarInitialized,
  });

  @override
  State<CattleManagerApp> createState() => _CattleManagerAppState();
}

class _CattleManagerAppState extends State<CattleManagerApp> {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Cattle Manager',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: AppTheme.primaryColor),
        useMaterial3: true,
        scaffoldBackgroundColor: AppTheme.scaffoldBackground,
        appBarTheme: AppTheme.appBarTheme,
        cardTheme: CardThemeData(
          elevation: 2,
          shape: RoundedRectangleBorder(borderRadius: AppTheme.cardRadius),
        ),
      ),
      home: widget.isarInitialized 
          ? const DashboardScreen()
          : const DatabaseErrorScreen(),
      routes: {
        // All screens now use the Provider pattern with automatic controller lifecycle management
        AppRoutes.cattle: (context) => ChangeNotifierProvider(
          create: (context) => CattleController()..loadData(),
          child: const CattleScreen(),
        ),
        AppRoutes.breeding: (context) => const BreedingScreen(), // Provider managed internally
        AppRoutes.health: (context) => const HealthScreen(), // Provider managed internally
        AppRoutes.milk: (context) => const MilkScreen(), // Provider managed internally
        AppRoutes.transactions: (context) => const TransactionsScreen(), // Provider managed internally
        // Note: EventsScreen doesn't have a route in AppRoutes yet
        // AppRoutes.settings: (context) => const FarmSetupScreen(),
        // AppRoutes.transactionsReport: (context) =>
        //     const TransactionsReportScreen(),
        // AppRoutes.milkReport: (context) => const MilkReportScreen(),
        // AppRoutes.cattleReport: (context) => const CattleReportScreen(),
        // AppRoutes.eventsReport: (context) => const EventsReportScreen(),
        // AppRoutes.breedingReport: (context) => const BreedingReportScreen(),
        // AppRoutes.pregnanciesReport: (context) =>
        //     const PregnanciesReportScreen(),
        // AppRoutes.weightReport: (context) => const WeightReportScreen(),
        // AppRoutes.healthReport: (context) => const HealthReportScreen(),
      },
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('en', ''),
      ],
    );
  }

  @override
  void dispose() {
    // Clean up dependencies when the app is closed
    // GetIt will handle cleanup automatically
    super.dispose();
  }
}

class DatabaseErrorScreen extends StatelessWidget {
  const DatabaseErrorScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 64,
              ),
              const SizedBox(height: 16),
              const Text(
                'Database Error',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                'The database failed to initialize. Please restart the app or contact support.',
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  // You could add a retry mechanism here if needed
                  Navigator.of(context).pushReplacement(
                    MaterialPageRoute(
                      builder: (context) => const CattleManagerApp(
                        isarInitialized: false,
                      ),
                    ),
                  );
                },
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
