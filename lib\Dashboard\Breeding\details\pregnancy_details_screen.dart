/*
// TODO: Breeding Module - Pregnancy Details Screen
// This file is commented out due to refactoring needs. It previously relied on deleted or moved files (DatabaseHelper and StreamService).
// Update to use the new repository pattern when integrating back into the project.

import 'package:flutter/material.dart';
import 'dart:async'; // Add this for StreamSubscription
import 'package:intl/intl.dart';
import '../../Cattle/models/cattle_isar.dart'; // Import CattleIsar
import '../../../constants/app_colors.dart';
import 'package:get_it/get_it.dart'; // Add this for GetIt

import '../dialogs/pregnancy_form_dialog.dart';
import '../dialogs/delivery_form_dialog.dart'; // Updated import path
import '../../Cattle/widgets/index.dart'; // Import reusable widgets
import '../models/breeding_record_isar.dart'; // Import BreedingRecordIsar
import '../models/pregnancy_record_isar.dart';
// Removed references to deleted StreamService.
import '../../../utils/message_utils.dart';

// Extension method to add firstWhereOrNull to List
extension ListExtensions<T> on List<T> {
  T? firstWhereOrNull(bool Function(T) test) {
    for (var element in this) {
      if (test(element)) return element;
    }
    return null;
  }
}

class PregnancyView extends StatefulWidget {
  final CattleIsar cattle;
  final Function(CattleIsar) onCattleUpdated;
  final DatabaseHelper databaseHelper = DatabaseHelper.instance;

  PregnancyView({
    super.key,
    required this.cattle,
    required this.onCattleUpdated,
  });

  @override
  State<PregnancyView> createState() => _PregnancyViewState();
}

class _PregnancyViewState extends State<PregnancyView> {
  final DatabaseHelper _databaseHelper = DatabaseHelper.instance;
  bool _isLoading = false;
  bool _isPregnant = false;
  DateTime? _dueDate;
  String? _pregnancyStatus;
  int _pregnancyDays = 0;
  double _progressPercentage = 0.0;
  List<Map<String, dynamic>> _milestones = [];
  BreedingRecordIsar? _currentBreedingRecord;
  List<CattleIsar> _allCattle = [];
  StreamSubscription? _breedingRecordSubscription;
  StreamSubscription? _pregnancyRecordSubscription;
  bool _isProcessingState = false;
  Timer? _stateDebounceTimer;
  PregnancyRecordIsar? _activePregnancyRecord;
  List<PregnancyRecordIsar> _pregnancyRecords = [];
  // Add this field at the class level near other field declarations
  final GlobalKey _pregnancyViewKey = GlobalKey();
  // Add this field at the class level near other field declarations
  bool _isShowingCompletedPregnancy = false;
  // Add these missing variables for proper error handling and loading state
  // Add a cache for gestation days to avoid repeated fetches
  int? _cachedGestationDays;

  // Helper method to safely set state if mounted
  void _safeSetState(VoidCallback fn) {
    if (mounted) {
      setState(fn);
    }
  }

  // Helper method to create user-friendly error messages
  String _getReadableErrorMessage(dynamic error) {
    final message = error.toString();

    if (message.contains('permission')) {
      return 'Permission denied. Please check app permissions in settings.';
    } else if (message.contains('file')) {
      return 'Error accessing file. The file may be corrupted or unavailable.';
    } else if (message.contains('database')) {
      return 'Database error. Please restart the app and try again.';
    } else if (message.contains('network')) {
      return 'Network error. Please check your connection and try again.';
    } else if (message.contains('ValidationException')) {
      return message.replaceAll('ValidationException: ', '');
    }

    return 'An unexpected error occurred. Please try again.';
  }

  // Method to ensure milestones are generated if the cattle is pregnant
  void _ensureMilestonesGenerated() {
    debugPrint(
        'Ensuring milestones are generated - isPregnant: $_isPregnant, milestones count: ${_milestones.length}');

    if (_isPregnant && _milestones.isEmpty) {
      debugPrint('Need to regenerate milestones');

      // Use cached gestation days if available, otherwise fetch them
      if (_cachedGestationDays != null) {
        _generateMilestonesWithCachedData();
      } else {
        // Get the animal type for this cattle to get the correct gestation period
        _databaseHelper.farmSetupHandler
            .getAllAnimalTypes()
            .then((animalTypes) {
          final animalType = animalTypes.firstWhere(
            (type) =>
                type.businessId == widget.cattle.animalTypeId,
            orElse: () => animalTypes.first,
          );

          _cachedGestationDays = animalType.defaultGestationDays ?? 283;
          _generateMilestonesWithCachedData();

          // Update the UI
          if (mounted) {
            setState(() {});
          }
        });
      }
    }
  }

  // Helper method to generate milestones using cached data
  void _generateMilestonesWithCachedData() {
    final gestationDays = _cachedGestationDays ?? 280;

    // Try to use the current breeding record if available
    if (_currentBreedingRecord != null) {
      try {
        debugPrint('Regenerating milestones from current breeding record');
        final breedingDate = _currentBreedingRecord!.date;
        _generateMilestones(breedingDate, gestationDays);
      } catch (e) {
        debugPrint('Error generating milestones from breeding record: $e');
        _generateMilestonesFromPregnancyData(gestationDays);
      }
    } else {
      // Fallback to using pregnancy data
      _generateMilestonesFromPregnancyData(gestationDays);
    }
  }

  @override
  void initState() {
    super.initState();
    debugPrint('🔄 PregnancyView initState for ${widget.cattle.tagId}');
    debugPrint('🔄 Initial cattle lastCalvingDate: ${widget.cattle.breedingStatus?.lastCalvingDate}');
    _loadAllCattle();
    _loadPregnancyRecords();
    _checkPregnancyStatus();
    _subscribeToBreedingRecordUpdates();

    // Add a post-frame callback to ensure milestones are generated after the first build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _ensureMilestonesGenerated();
    });
  }

  @override
  void didUpdateWidget(PregnancyView oldWidget) {
    super.didUpdateWidget(oldWidget);
    debugPrint('🔄 PregnancyView didUpdateWidget for ${widget.cattle.tagId}');
    debugPrint('🔄 Old cattle lastCalvingDate: ${oldWidget.cattle.breedingStatus?.lastCalvingDate}');
    debugPrint('🔄 New cattle lastCalvingDate: ${widget.cattle.breedingStatus?.lastCalvingDate}');

    // Reload data if cattle changed
    if (oldWidget.cattle.businessId != widget.cattle.businessId ||
        oldWidget.cattle.breedingStatus?.lastCalvingDate != widget.cattle.breedingStatus?.lastCalvingDate) {
      debugPrint('🔄 Cattle data changed, reloading pregnancy data');
      _loadPregnancyRecords();
      _checkPregnancyStatus();
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Initial load when the view is first created
    _checkPregnancyStatus().then((_) {
      // Ensure milestones are generated after checking pregnancy status
      _ensureMilestonesGenerated();
    });
  }

  @override
  void dispose() {
    _breedingRecordSubscription?.cancel();
    _pregnancyRecordSubscription?.cancel();
    _stateDebounceTimer?.cancel();
    super.dispose();
  }

  // Subscribe to breeding and pregnancy record updates
  void _subscribeToBreedingRecordUpdates() {
    Timer? debounceTimer;

    // Get the stream service from GetIt
    final streamService = GetIt.instance<StreamService>();

    // Subscribe to breeding records
    _breedingRecordSubscription =
        streamService.breedingStream.listen((event) async {
      // Get the event details
      final eventCattleId = event['cattleId'] as String?;
      final action = event['action'] as String?;
      final recordData = event['record'] as Map<String, dynamic>?;
      final recordId = event['recordId'] as String?;

      // Check if this event is relevant to our cattle
      if (eventCattleId == widget.cattle.tagId) {
        debugPrint(
            'Breeding record update received: $action for cattle $eventCattleId');

        // Cancel any pending debounce timer
        debounceTimer?.cancel();
        _stateDebounceTimer?.cancel();

        // Set a new debounce timer with shorter duration for more responsive UI
        debounceTimer = Timer(const Duration(milliseconds: 300), () async {
          if (!mounted || _isProcessingState) return;

          try {
            _isProcessingState = true;

            // Handle different action types more efficiently
            switch (action) {
              case 'add':
              case 'update':
                if (recordData != null) {
                  // Check if this is a BreedingRecordIsar by looking for required fields
                  if (recordData['date'] != null &&
                      recordData['type'] != null) {
                    final breedingRecord =
                        BreedingRecordIsar.fromMap(recordData);
                    final breedingStatus =
                        breedingRecord.status?.toLowerCase() ?? '';

                    // Only update the UI if this is a status change that affects pregnancy
                    if (breedingStatus == 'confirmed' ||
                        breedingStatus == 'completed') {
                      // Refresh pregnancy data since this may affect pregnancy state
                      if (mounted) {
                        await _checkPregnancyStatus();
                      }
                    }
                  }
                  // Check if this is a PregnancyRecordIsar
                  else if (recordData['cattleId'] == widget.cattle.tagId &&
                      recordData['status'] != null) {
                    final pregnancyRecord =
                        PregnancyRecordIsar.fromMap(recordData);

                    // Update local state directly
                    if (mounted) {
                      _safeSetState(() {
                        // Find if we already have this record
                        final index = _pregnancyRecords
                            .indexWhere((r) => r.id == pregnancyRecord.id);
                        if (index >= 0) {
                          // Update existing record
                          _pregnancyRecords[index] = pregnancyRecord;
                        } else {
                          // Add new record
                          _pregnancyRecords.add(pregnancyRecord);
                        }

                        // Re-sort records
                        _pregnancyRecords.sort((a, b) =>
                            (b.startDate ?? DateTime(0))
                                .compareTo(a.startDate ?? DateTime(0)));

                        // Update active pregnancy if needed
                        if (pregnancyRecord.status?.toLowerCase() ==
                            'confirmed') {
                          _activePregnancyRecord = pregnancyRecord;
                          _isPregnant = true;
                          _dueDate = pregnancyRecord.expectedCalvingDate;
                        } else if (_activePregnancyRecord?.id ==
                            pregnancyRecord.id) {
                          // If this record was the active one but is no longer confirmed
                          if (pregnancyRecord.status?.toLowerCase() !=
                              'confirmed') {
                            // Find a new active record
                            _activePregnancyRecord =
                                _pregnancyRecords.firstWhereOrNull((r) =>
                                    r.status?.toLowerCase() == 'confirmed');
                            _isPregnant = _activePregnancyRecord != null;
                            _dueDate =
                                _activePregnancyRecord?.expectedCalvingDate;
                          } else {
                            // Update the existing active record
                            _activePregnancyRecord = pregnancyRecord;
                          }
                        }
                      });

                      // Still need to update milestones and other calculated data
                      _ensureMilestonesGenerated();
                    }
                  }
                } else {
                  // If we don't have record data, refresh pregnancy status
                  if (mounted) {
                    await _checkPregnancyStatus();
                  }
                }
                break;

              case 'delete':
                // Immediately clear pregnancy state for real-time UI update
                if (mounted) {
                  _safeSetState(() {
                    _activePregnancyRecord = null;
                    _isPregnant = false;
                    _dueDate = null;
                    _milestones.clear();
                    _pregnancyRecords.clear();
                  });
                }

                // Handle delete action with recordId
                if (recordId != null) {
                  // Check if this was our active pregnancy record
                  final wasActiveRecord =
                      _activePregnancyRecord?.businessId == recordId;

                  if (mounted) {
                    _safeSetState(() {
                      // Remove from our records list
                      _pregnancyRecords
                          .removeWhere((r) => r.businessId == recordId);

                      debugPrint('Removed pregnancy record with ID: $recordId');

                      // If we deleted the active record, find a new one
                      if (wasActiveRecord) {
                        _activePregnancyRecord =
                            _pregnancyRecords.firstWhereOrNull(
                                (r) => r.status?.toLowerCase() == 'confirmed');
                        _isPregnant = _activePregnancyRecord != null;
                        _dueDate = _activePregnancyRecord?.expectedCalvingDate;

                        // Clear milestones if no longer pregnant
                        if (!_isPregnant) {
                          _milestones.clear();
                        }
                      }
                    });

                    // Refresh pregnancy status
                    await _checkPregnancyStatus();
                    return;
                  }
                }
                // Fallback to old implementation if recordId is null
                else if (recordData != null && recordData['id'] != null) {
                  final recordId = recordData['id'].toString();

                  // Check if this was our active pregnancy record
                  final wasActiveRecord =
                      _activePregnancyRecord?.id.toString() ==
                          recordId;

                  if (mounted) {
                    _safeSetState(() {
                      // Remove from our records list
                      _pregnancyRecords.removeWhere(
                          (r) => r.id.toString() == recordId);

                      // Update active pregnancy if needed
                      if (wasActiveRecord) {
                        _activePregnancyRecord =
                            _pregnancyRecords.firstWhereOrNull(
                                (r) => r.status?.toLowerCase() == 'confirmed');
                        _isPregnant = _activePregnancyRecord != null;
                        _dueDate = _activePregnancyRecord?.expectedCalvingDate;

                        // Clear milestones if no longer pregnant
                        if (!_isPregnant) {
                          _milestones.clear();
                        }
                      }
                    });

                    // Still need to update milestones and other calculated data
                    _ensureMilestonesGenerated();
                  }
                } else {
                  // If we don't have record data, immediately clear state and refresh
                  if (mounted) {
                    _safeSetState(() {
                      _activePregnancyRecord = null;
                      _isPregnant = false;
                      _dueDate = null;
                      _milestones.clear();
                    });
                    await _checkPregnancyStatus();
                  }
                }
                break;

              default:
                // For unknown actions, just refresh
                if (mounted) {
                  await _checkPregnancyStatus();
                }
                break;
            }
          } catch (e) {
            debugPrint('Error handling breeding record update: $e');
          } finally {
            _isProcessingState = false;
          }
        });
      }
    }, onError: (error) {
      debugPrint('Error in breeding record stream: $error');
    });

    // Subscribe to pregnancy records
    _pregnancyRecordSubscription =
        streamService.pregnancyStream.listen((event) async {
      // Skip if we're already processing state changes to prevent duplicate updates
      if (_isProcessingState) {
        debugPrint('Skipping stream update - already processing state changes');
        return;
      }

      // Get the event details
      final eventCattleId = event['cattleId'] as String?;
      final action = event['action'] as String?;

      // Check if this event is relevant to our cattle
      if (eventCattleId == widget.cattle.tagId) {
        debugPrint(
            'Pregnancy record update received: $action for cattle $eventCattleId');

        // Set processing flag to prevent duplicate updates
        _isProcessingState = true;

        try {
          // For any pregnancy record changes, reload from database to ensure consistency
          // This prevents temporary incorrect statistics counts
          await _loadPregnancyRecords();
          await _checkPregnancyStatus();
        } catch (e) {
          debugPrint('Error handling pregnancy record stream update: $e');
        } finally {
          _isProcessingState = false;
        }
      }
    }, onError: (error) {
      debugPrint('Error in pregnancy record stream: $error');
    });
  }



  // Check pregnancy status
  Future<void> _checkPregnancyStatus() async {
    if (!mounted) return;

    try {
      // First check if we have pregnancy records in our state
      if (_pregnancyRecords.isEmpty) {
        // Load pregnancy records from database if we don't have any
        await _loadPregnancyRecords();
      }

      // Find active pregnancy (one with status 'confirmed')
      _activePregnancyRecord = _pregnancyRecords.firstWhereOrNull(
          (record) => record.status?.toLowerCase() == 'confirmed');

      // Check if there's a mismatch between the cattle's breeding status and our local state
      bool cattleIsPregnant = widget.cattle.breedingStatus?.isPregnant ?? false;
      bool hasActivePregnancyRecord = _activePregnancyRecord != null;

      // If there's a mismatch, we need to update either the cattle or our local state
      if (cattleIsPregnant != hasActivePregnancyRecord) {
        if (hasActivePregnancyRecord) {
          // We have an active pregnancy record but cattle says not pregnant - update cattle
          final updatedCattle = widget.cattle.copyWith(
            breedingStatus: BreedingStatus()
              ..isPregnant = true
              ..status = 'Pregnant'
              ..expectedCalvingDate =
                  _activePregnancyRecord?.expectedCalvingDate,
          );
          await _databaseHelper.cattleHandler.updateCattle(updatedCattle);
          widget.onCattleUpdated(updatedCattle);
          debugPrint(
              'Updated cattle to pregnant based on active pregnancy record');
        } else {
          // Cattle says pregnant but we have no active pregnancy record - update cattle
          final updatedCattle = widget.cattle.copyWith(
            breedingStatus: BreedingStatus()
              ..isPregnant = false
              ..status = 'Open'
              ..expectedCalvingDate = null,
          );
          await _databaseHelper.cattleHandler.updateCattle(updatedCattle);
          widget.onCattleUpdated(updatedCattle);
          debugPrint(
              'Updated cattle to not pregnant based on lack of active pregnancy record');
        }
      }

      // Update pregnancy state
      _safeSetState(() {
        _isPregnant = hasActivePregnancyRecord;
        _dueDate = _activePregnancyRecord?.expectedCalvingDate;
      });

      // Calculate missing due date if needed
      if (_activePregnancyRecord != null &&
          _activePregnancyRecord!.expectedCalvingDate == null &&
          _activePregnancyRecord!.startDate != null) {
        await _calculateAndUpdateDueDate(_activePregnancyRecord!);
      }

      // Calculate pregnancy progress
      _updatePregnancyProgress();

      // Ensure milestones are generated
      _ensureMilestonesGenerated();

      // Find current milestone
      _identifyCurrentMilestone();
    } catch (e) {
      debugPrint('Error checking pregnancy status: $e');
      _safeSetState(() {});
    }
  }

  // Helper method to calculate pregnancy progress percentage
  void _updatePregnancyProgress() {
    if (!_isPregnant || _activePregnancyRecord == null) {
      _progressPercentage = 0.0;
      _pregnancyDays = 0;
      return;
    }

    final startDate = _activePregnancyRecord!.startDate;
    final dueDate = _activePregnancyRecord!.expectedCalvingDate ?? _dueDate;

    if (startDate == null || dueDate == null) {
      _progressPercentage = 0.0;
      _pregnancyDays = 0;
      return;
    }

    final now = DateTime.now();
    final totalDuration = dueDate.difference(startDate).inDays;

    if (totalDuration <= 0) {
      _progressPercentage = 0.0;
      _pregnancyDays = 0;
      return;
    }

    final elapsedDuration = now.difference(startDate).inDays;
    _pregnancyDays = elapsedDuration;

    // Calculate progress percentage (capped at 100%)
    _progressPercentage = (elapsedDuration / totalDuration) * 100;
    if (_progressPercentage > 100) _progressPercentage = 100;
    if (_progressPercentage < 0) _progressPercentage = 0;

    debugPrint(
        'Pregnancy progress: $_progressPercentage%, Days: $_pregnancyDays');
  }

  // Helper method to identify current milestone
  void _identifyCurrentMilestone() {
    if (_milestones.isEmpty) return;

    final now = DateTime.now();

    // Update the passed status for all milestones
    for (var milestone in _milestones) {
      final date = milestone['date'] as DateTime;
      milestone['isPassed'] = now.isAfter(date);
    }

    // Find the next upcoming milestone (first one that hasn't passed)
    _pregnancyStatus = _milestones
        .firstWhereOrNull((m) => !(m['isPassed'] as bool))?['title'] as String?;

    if (_pregnancyStatus == null && _milestones.isNotEmpty) {
      // If all milestones have passed, we're at the last one
      _pregnancyStatus = _milestones.last['title'] as String?;
    }

    debugPrint('Current pregnancy status: $_pregnancyStatus');
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    // Debug print to check milestones at build time
    debugPrint(
        'Building UI - isPregnant: $_isPregnant, milestones count: ${_milestones.length}');

    return Scaffold(
      body: RefreshIndicator(
        onRefresh: _checkPregnancyStatus,
        child: SingleChildScrollView(
          key: _pregnancyViewKey,
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Statistics Card - Use records from state instead of fetching
              _buildStatsCard(),
              const SizedBox(height: 16),

              // Pregnancy Status Card - Show when pregnant
              if (_isPregnant) ...[
                (() {
                  // Debug logs to help diagnose issues
                  debugPrint(
                      '_activePregnancyRecord: ${_activePregnancyRecord != null}');
                  if (_activePregnancyRecord != null) {
                    final status =
                        _activePregnancyRecord!.status?.toLowerCase() ?? '';
                    debugPrint('Pregnancy status: $status');
                    debugPrint('Is completed? ${status == 'completed'}');
                  }

                  // Check if the active pregnancy record is in 'Completed' status
                  final isCompleted = _isShowingCompletedPregnancy ||
                      (_activePregnancyRecord != null &&
                          (_activePregnancyRecord!.status?.toLowerCase() ==
                              'completed'));

                  debugPrint('Showing completed pregnancy UI? $isCompleted');

                  // Get start date from active pregnancy record or cattle's lastBreedingDate
                  DateTime? startDate;
                  if (_activePregnancyRecord != null &&
                      _activePregnancyRecord!.startDate != null) {
                    startDate = _activePregnancyRecord!.startDate;
                  } else if (widget.cattle.lastBreedingDate != null) {
                    startDate = widget.cattle.lastBreedingDate;
                  }

                  // For completed pregnancies, we use a different approach
                  if (isCompleted) {
                    // Get completion date if available
                    DateTime? completionDate;
                    if (_activePregnancyRecord != null &&
                        _activePregnancyRecord!.endDate != null) {
                      completionDate = _activePregnancyRecord!.endDate;
                    }

                    // Return a card with completion info
                    return Card(
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12)),
                      margin: const EdgeInsets.only(bottom: 16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Header with consistent styling
                          const CardHeader(
                            title: 'Completed Pregnancy',
                            color: Color(0xFF9C27B0), // Purple for completed
                            icon: Icons.check_circle,
                          ),
                          Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              children: [
                                InfoRow(
                                  icon: Icons.calendar_today,
                                  label: 'Breeding Date',
                                  value: startDate != null
                                      ? DateFormat('MMMM dd, yyyy')
                                          .format(startDate)
                                      : 'Not recorded',
                                  color: const Color(0xFF1976D2), // Blue
                                ),
                                const SizedBox(height: 12),
                                InfoRow(
                                  icon: Icons.event_available,
                                  label: 'Completion Date',
                                  value: completionDate != null
                                      ? DateFormat('MMMM dd, yyyy')
                                          .format(completionDate)
                                      : 'Not recorded',
                                  color: const Color(0xFF9C27B0), // Purple
                                  isHighlighted: true,
                                ),
                                if (_activePregnancyRecord!.notes?.isNotEmpty ==
                                    true) ...[
                                  const SizedBox(height: 16),
                                  const Divider(),
                                  const SizedBox(height: 8),
                                  InfoRow(
                                    icon: Icons.notes,
                                    label: 'Notes',
                                    value: _activePregnancyRecord!.notes ?? '',
                                    color:
                                        const Color(0xFF673AB7), // Deep Purple
                                    isMultiline: true,
                                  ),
                                ],
                              ],
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  // For ongoing pregnancies, use our StatusCard widget
                  // Get the expected calving date from the active pregnancy record if available
                  final expectedDate =
                      _activePregnancyRecord?.expectedCalvingDate ??
                          widget.cattle.breedingStatus?.expectedCalvingDate;

                  // Log the expected date for debugging
                  debugPrint(
                      'Pregnancy View: Expected calving date: $expectedDate');
                  debugPrint(
                      'Cattle breeding status: isPregnant=${widget.cattle.breedingStatus?.isPregnant}, expectedDate=${widget.cattle.breedingStatus?.expectedCalvingDate}');
                  debugPrint(
                      'Active pregnancy record: ${_activePregnancyRecord?.businessId}, expectedDate=${_activePregnancyRecord?.expectedCalvingDate}');

                  return StatusCard.pregnancy(
                    isPregnant: true,
                    dueDate: expectedDate,
                    startDate: startDate,
                    title: 'Current Pregnancy',
                    baseColor:
                        const Color(0xFF2E7D32), // Green for active pregnancy
                    trailing: IconButton(
                      icon: const Icon(Icons.info_outline,
                          color: Color(0xFF2E7D32)),
                      onPressed: () {
                        showDialog(
                          context: context,
                          builder: (context) => AlertDialog(
                            title: const Text('Pregnancy Status Information'),
                            content: const Text(
                                'This card shows the current pregnancy status and expected calving date for this cattle.'),
                            actions: [
                              TextButton(
                                onPressed: () => Navigator.of(context).pop(),
                                child: const Text('Close'),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                    actionButtons: _isCloseToDeliveryDate()
                        ? [
                            ElevatedButton.icon(
                              onPressed: _recordBirth,
                              icon: const Icon(Icons.child_care),
                              label: const Text('Record Birth'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: const Color(0xFF2E7D32),
                                foregroundColor: Colors.white,
                                minimumSize: const Size(double.infinity, 48),
                              ),
                            ),
                          ]
                        : null,
                  );
                })(),
                const SizedBox(height: 16),
              ],

              // Pregnancy Eligibility Card - Only show when not pregnant
              if (!_isPregnant) ...[
                _buildEligibilityCard(),
                const SizedBox(height: 16),
              ],

              // Add Milestone Card when pregnant
              if (_isPregnant && _milestones.isNotEmpty) ...[
                MilestoneCard(
                  milestones: _milestones,
                  title: 'Pregnancy Milestones',
                  emptyMessage: 'No milestones available',
                  baseColor: const Color(0xFF2E7D32), // Green
                  onInfoTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content:
                            Text('Shows important milestones during pregnancy'),
                        duration: Duration(seconds: 2),
                      ),
                    );
                  },
                ),
                const SizedBox(height: 16),
              ],

              // Add PregnancyHistoryCard - now using records from state
              _buildPregnancyHistoryCard(),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
      // Always show FAB, but show dialog if already pregnant
      floatingActionButton: FloatingActionButton(
        onPressed: _showPregnancyForm,
        backgroundColor: AppColors.primary,
        tooltip: 'Add Pregnancy Record',
        child: const Icon(Icons.add),
      ),
    );
  }

  // Optimized stats card that uses the state directly
  Widget _buildStatsCard() {
    // Calculate statistics directly from state
    int totalPregnancies = _pregnancyRecords.length;
    int activePregnancies = _pregnancyRecords
        .where((r) => r.status?.toLowerCase() == 'confirmed')
        .length;
    int completedPregnancies = _pregnancyRecords
        .where((r) => r.status?.toLowerCase() == 'completed')
        .length;
    int abortions = _pregnancyRecords
        .where((r) => r.status?.toLowerCase() == 'abortion')
        .length;

    // Remove unused variable
    // String keyString = 'pregnancy_stats_${widget.cattle.tagId}';

    // Use the factory method instead of manually creating StatsCard
    return StatsCard.pregnancyStats(
      totalPregnancies: totalPregnancies,
      completedPregnancies: completedPregnancies,
      abortions: abortions,
      confirmedPregnancies: activePregnancies,
      onInfoTap: () {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content:
                Text('Shows detailed information about pregnancy statistics'),
            duration: Duration(seconds: 2),
          ),
        );
      },
      onCardTap: () {
        // Optional: Navigate to detailed pregnancy records view
      },
    );
  }

  Widget _buildEligibilityCard() {
    debugPrint('🏗️ BUILDING Pregnancy Eligibility Card for ${widget.cattle.tagId}');
    debugPrint('🏗️ Current cattle lastCalvingDate: ${widget.cattle.breedingStatus?.lastCalvingDate}');
    debugPrint('🏗️ Current cattle status: ${widget.cattle.breedingStatus?.status}');
    debugPrint('🏗️ Current cattle isPregnant: ${widget.cattle.breedingStatus?.isPregnant}');
    debugPrint('🏗️ Local _isPregnant state: $_isPregnant');

    return FutureBuilder<List<Map<String, dynamic>>>(
      future: _databaseHelper.breedingHandler
          .getBreedingRecordsForCattle(widget.cattle.tagId ?? '')
          .then((records) => records.map((record) => record.toMap()).toList()),
      builder: (context, snapshot) {
        // Default to empty list if data is not available yet
        final breedingRecords = snapshot.data ?? [];

        return FutureBuilder(
          future: _databaseHelper.farmSetupHandler.getAllAnimalTypes(),
          builder: (context, animalTypeSnapshot) {
            final animalTypes = animalTypeSnapshot.data ?? [];
            final animalType = animalTypes.firstWhereOrNull(
              (type) => type.businessId == widget.cattle.animalTypeId,
            );

            return EligibilityCard.pregnancy(
              gender: widget.cattle.gender ?? '',
              dateOfBirth: widget.cattle.dateOfBirth,
              purchaseDate: widget.cattle.purchaseDate,
              cattleId: widget.cattle.tagId ?? '',
              animalTypeId: widget.cattle.animalTypeId ?? '',
              isPregnant: _isPregnant,
              lastCalvingDate: widget.cattle.breedingStatus?.lastCalvingDate,
              animalTypeEmptyPeriodDays: animalType?.defaultEmptyPeriodDays,
              breedingRecords: breedingRecords,
              onAddPressed: _showPregnancyForm,
          trailing: IconButton(
            icon: const Icon(Icons.info_outline, color: Color(0xFF6A1B9A)),
            onPressed: () {
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: const Text('Pregnancy Eligibility Information'),
                  content: const Text(
                      'This card shows the eligibility for pregnancy checks. The system checks for gender, age, and other factors.'),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('Close'),
                    ),
                  ],
                ),
              );
            },
          ),
        );
          },
        );
      },
    );
  }

  // Update the _showPregnancyForm method to check eligibility first
  void _showPregnancyForm() async {
    try {
      setState(() => _isLoading = true);

      // Get any existing pregnancy records for this cattle

      // Check if already pregnant using the eligibility card logic
      // Also checks for pending breeding records
      final eligibilityResult = await _checkPregnancyEligibility();

      // Set loading to false before showing dialog
      setState(() => _isLoading = false);

      if (!eligibilityResult.isEligible) {
        // Show a dialog explaining why pregnancy recording is not available
        if (mounted) {
          // Create the dialog widget before showing it
          final blockingDialog = AlertDialog(
            titlePadding: const EdgeInsets.all(16),
            contentPadding: const EdgeInsets.all(16),
            insetPadding:
                const EdgeInsets.symmetric(horizontal: 20, vertical: 24),
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            title: Row(
              children: [
                Icon(
                  eligibilityResult.statusIcon,
                  color: eligibilityResult.statusColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Flexible(
                  child: Text(
                    eligibilityResult.statusMessage,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Cattle: ${widget.cattle.name ?? 'Unknown'} (${widget.cattle.tagId ?? 'Unknown'})',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 12),
                const Text(
                  'You cannot add a pregnancy record because:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Text(eligibilityResult.reasonMessage),
                if (eligibilityResult.nextEligibleDateMessage != null) ...[
                  const SizedBox(height: 12),
                  Text(
                    eligibilityResult.nextEligibleDateMessage!,
                    style: const TextStyle(fontStyle: FontStyle.italic),
                  ),
                ],
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                style: TextButton.styleFrom(
                  foregroundColor: const Color(0xFF2E7D32),
                ),
                child: const Text('Close'),
              ),
            ],
          );

          // Show the dialog after it's been created
          showDialog(context: context, builder: (context) => blockingDialog);
        }
        return;
      }

      // Get the animal type for this cattle to get the correct gestation period
      final animalTypes =
          await _databaseHelper.farmSetupHandler.getAllAnimalTypes();
      animalTypes.firstWhere(
        (type) => type.id.toString() == widget.cattle.animalTypeId?.toString(),
        orElse: () => animalTypes.first,
      );

      // Get gestation period for this animal type to use when generating milestones
// Default to cattle gestation if not specified

      if (!mounted) return;

      // Preload data for the form dialog
      final allCattle = await _databaseHelper.cattleHandler.getAllCattle();
      final animalTypesMap = {
        for (var type in animalTypes) type.businessId ?? '': type
      };

      if (!mounted) return;

      // Show the pregnancy form dialog with preloaded data
      final result = await showDialog<PregnancyRecordIsar>(
        context: context,
        builder: (dialogContext) => PregnancyFormDialog(
          initialCattleId: widget.cattle.tagId,
          breedingRecordId: null,
          preloadedCattle: allCattle,
          preloadedAnimalTypes: animalTypesMap,
        ),
      );

      // If the user submitted the form
      if (result != null && mounted) {
        await _addPregnancyRecord(result);
      }
    } catch (e) {
      if (!mounted) return;
      setState(() => _isLoading = false);
      BreedingMessageUtils.showError(context, 'Error showing pregnancy form: $e');
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  // Update _checkPregnancyEligibility to use the static method from EligibilityCard
  // and also check for pending breeding records
  Future<EligibilityCheckResult> _checkPregnancyEligibility() async {
    // First check for pending breeding records
    final breedingRecords = await _databaseHelper.breedingHandler
        .getBreedingRecordsForCattle(widget.cattle.tagId ?? '')
        .then((records) => records.map((record) => record.toMap()).toList());

    // Check if there's a pending breeding record
    bool hasPendingBreeding = false;
    if (breedingRecords.isNotEmpty) {
      hasPendingBreeding = breedingRecords.any(
          (record) => record['status']?.toString().toLowerCase() == 'pending');
    }

    // If there's a pending breeding record, return not eligible
    if (hasPendingBreeding) {
      // Find the pending breeding record to get its date
      final pendingRecord = breedingRecords.firstWhere(
          (record) => record['status']?.toString().toLowerCase() == 'pending');
      final breedingDate = DateTime.parse(
          pendingRecord['date'] ?? DateTime.now().toIso8601String());
      final formattedDate = DateFormat('MMMM dd, yyyy').format(breedingDate);

      return EligibilityCheckResult(
        isEligible: false,
        statusMessage: 'Pending Breeding Record',
        reasonMessage:
            'Breeding attempt on $formattedDate is still pending confirmation',
        statusColor: const Color(0xFF2196F3), // Blue
        statusIcon: Icons.pending,
        nextEligibleDateMessage:
            'Please update the pending breeding record before adding a pregnancy record',
      );
    }

    // Get animal type settings for empty period
    final animalTypes = await _databaseHelper.farmSetupHandler.getAllAnimalTypes();
    final animalType = animalTypes.firstWhereOrNull(
      (type) => type.businessId == widget.cattle.animalTypeId,
    );

    // Otherwise, use the standard eligibility check
    debugPrint('🔍 Pregnancy view eligibility check for ${widget.cattle.tagId}:');
    debugPrint('🔍 lastCalvingDate: ${widget.cattle.breedingStatus?.lastCalvingDate}');
    debugPrint('🔍 isPregnant: $_isPregnant');
    debugPrint('🔍 status: ${widget.cattle.breedingStatus?.status}');
    debugPrint('🔍 animalTypeEmptyPeriodDays: ${animalType?.defaultEmptyPeriodDays}');

    return EligibilityCard.checkPregnancyEligibility(
      gender: widget.cattle.gender ?? '',
      cattleId: widget.cattle.tagId ?? '',
      isPregnant: _isPregnant,
      dateOfBirth: widget.cattle.dateOfBirth,
      animalTypeId: widget.cattle.animalTypeId ?? '',
      purchaseDate: widget.cattle.purchaseDate,
      lastCalvingDate: widget.cattle.breedingStatus?.lastCalvingDate,
      animalTypeEmptyPeriodDays: animalType?.defaultEmptyPeriodDays,
    );
  }

  void _generateMilestonesFromPregnancyData(int gestationDays) {
    _milestones = [];

    if (_activePregnancyRecord == null && _dueDate == null) {
      debugPrint(
          'Cannot generate milestones: no active pregnancy record or due date');
      return;
    }

    // Use the pregnancy start date from the active record, or fallback to calculating from due date
    DateTime? startDate;
    if (_activePregnancyRecord != null &&
        _activePregnancyRecord!.startDate != null) {
      startDate = _activePregnancyRecord!.startDate;
      if (startDate == null) {
        debugPrint(
            'Cannot determine pregnancy start date - invalid date format');
        // Fallback to 30 days ago if date parsing fails
        startDate = DateTime.now().subtract(const Duration(days: 30));
      }
    } else if (_dueDate != null) {
      // Calculate backwards from due date
      startDate = _dueDate!.subtract(Duration(days: gestationDays));
    }

    if (startDate == null) {
      debugPrint('Cannot determine pregnancy start date - no data available');
      return;
    }

    debugPrint(
        'Generating milestones from pregnancy data with start date: $startDate');

    // Generate milestones using the start date and gestation period
    _generateMilestones(startDate, gestationDays);
  }

  Future<void> _loadAllCattle() async {
    try {
      final allCattle = await _databaseHelper.cattleHandler.getAllCattle();
      if (mounted) {
        setState(() {
          _allCattle = allCattle.cast<CattleIsar>();
        });
      }
    } catch (e) {
      debugPrint('Error loading all cattle: $e');
    }
  }

  Future<void> _loadPregnancyRecords() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Get pregnancy records for this cattle
      final pregnancyRecords = await _databaseHelper.breedingHandler
          .getPregnancyRecordsForCattle(widget.cattle.tagId ?? '');

      // Filter out duplicate records by businessId
      final Map<String, PregnancyRecordIsar> uniqueRecords = {};
      for (final record in pregnancyRecords) {
        if (record.businessId != null) {
          uniqueRecords[record.businessId!] = record;
        } else {
          // Fallback to using id if businessId is null
          uniqueRecords[record.id.toString()] = record;
        }
      }

      // Convert back to list
      final filteredRecords = uniqueRecords.values.toList();

      // Sort by date, most recent first
      filteredRecords.sort((a, b) =>
          (b.startDate ?? DateTime(0)).compareTo(a.startDate ?? DateTime(0)));

      // Find the active pregnancy record (confirmed status)
      final activePregnancy = filteredRecords
          .where((record) => (record.status?.toLowerCase() == 'confirmed'))
          .lastOrNull;

      // Also try to find a completed pregnancy if no active one exists
      final completedPregnancy = activePregnancy == null
          ? filteredRecords
              .where((record) => record.status?.toLowerCase() == 'completed')
              .lastOrNull
          : null;

      // Check if we're showing a completed pregnancy
      final isShowingCompletedPregnancy =
          activePregnancy == null && completedPregnancy != null;

      if (mounted) {
        setState(() {
          _pregnancyRecords = filteredRecords;
          _activePregnancyRecord = activePregnancy;
          _isShowingCompletedPregnancy = isShowingCompletedPregnancy;
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading pregnancy records: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        BreedingMessageUtils.showError(context, _getReadableErrorMessage(e));
      }
    }
  }

  Future<void> _addPregnancyRecord(PregnancyRecordIsar pregnancyRecord) async {
    try {
      setState(() => _isLoading = true);
      _isProcessingState = true;

      // Add or update the pregnancy record directly
      await _databaseHelper.breedingHandler
          .managePregnancyRecord(pregnancyRecord);

      // Update the cattle's status
      final expectedCalvingDate = pregnancyRecord.expectedCalvingDate;

      final updatedCattle = widget.cattle.copyWith(
        breedingStatus: BreedingStatus()
          ..isPregnant = true
          ..status = 'Pregnant'
          ..expectedCalvingDate = expectedCalvingDate,
      );

      await _databaseHelper.cattleHandler.updateCattle(updatedCattle);

      // Notify parent of update
      widget.onCattleUpdated(updatedCattle);

      // Reload pregnancy records from database to ensure consistency
      // This prevents temporary incorrect statistics counts
      await _loadPregnancyRecords();

      // Check pregnancy status to update all other pregnancy-related state
      await _checkPregnancyStatus();

      if (mounted) {
        final message = BreedingMessageUtils.pregnancyRecordCreated();
        BreedingMessageUtils.showSuccess(context, message);
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        BreedingMessageUtils.showError(context, _getReadableErrorMessage(e));
      }
    } finally {
      _isProcessingState = false;
    }
  }

  Future<void> _editPregnancyRecord(Map<String, dynamic> record) async {
    try {
      // Convert the map to a PregnancyRecordIsar object for the dialog
      final pregnancyRecord = PregnancyRecordIsar.fromMap(record);

      // Show the pregnancy form dialog with the existing record
      final result = await showDialog<PregnancyRecordIsar>(
        context: context,
        builder: (context) => PregnancyFormDialog(
          record: pregnancyRecord,
          initialCattleId: widget.cattle.tagId,
        ),
      );

      // If the user submitted the form with changes
      if (result != null && mounted) {
        await _updatePregnancyRecord(result);
      }
    } catch (e) {
      if (mounted) {
        BreedingMessageUtils.showError(context, _getReadableErrorMessage(e));
      }
    }
  }

  Future<void> _updatePregnancyRecord(PregnancyRecordIsar pregnancyRecord) async {
    try {
      _safeSetState(() => _isLoading = true);
      _isProcessingState = true;

      // Get the original record for comparison
      final originalRecord =
          _pregnancyRecords.firstWhereOrNull((r) => r.id == pregnancyRecord.id);
      final originalStatus = originalRecord?.status?.toLowerCase() ?? '';
      final newStatus = pregnancyRecord.status?.toLowerCase() ?? '';

      // Update the pregnancy record in the database using the proper update method
      await _databaseHelper.breedingHandler
          .updatePregnancyRecord(pregnancyRecord, updateLinkedBreedingRecord: true);

      // Check if this is the active pregnancy record
      final isActiveRecord = _activePregnancyRecord != null &&
          pregnancyRecord.id == _activePregnancyRecord?.id;

      // Update cattle status if needed
      if (isActiveRecord ||
          (newStatus == 'confirmed' && originalStatus != 'confirmed')) {
        // Update the cattle's status
        DateTime? expectedCalvingDate = pregnancyRecord.expectedCalvingDate;

        final updatedCattle = widget.cattle.copyWith(
          breedingStatus: BreedingStatus()
            ..isPregnant = newStatus == 'confirmed'
            ..status = newStatus == 'confirmed' ? 'Pregnant' : 'Open'
            ..expectedCalvingDate = expectedCalvingDate,
        );

        await _databaseHelper.cattleHandler.updateCattle(updatedCattle);

        // Notify parent of update
        widget.onCattleUpdated(updatedCattle);
      }

      // Reload pregnancy records from database to ensure consistency
      // This prevents temporary incorrect statistics counts
      await _loadPregnancyRecords();

      // Check pregnancy status to update all other pregnancy-related state
      await _checkPregnancyStatus();

      if (mounted) {
        final message = BreedingMessageUtils.pregnancyRecordUpdated();
        BreedingMessageUtils.showSuccess(context, message);
      }
    } catch (e) {
      if (mounted) {
        _safeSetState(() => _isLoading = false);
        BreedingMessageUtils.showError(context, _getReadableErrorMessage(e));
      }
    } finally {
      _isProcessingState = false;
    }
  }

  Future<void> _deletePregnancyRecord(Map<String, dynamic> record) async {
    // Check for associated delivery records
    final deliveryRecords = await _databaseHelper.breedingHandler
        .getDeliveryRecordsForCattle(widget.cattle.tagId ?? '');
    final associatedDelivery = deliveryRecords.firstWhereOrNull(
      (dr) => dr['pregnancyId'] == record['id'],
    );

    // Check for associated breeding record
    final breedingRecords = await _databaseHelper.breedingHandler
        .getBreedingRecordsForCattle(widget.cattle.tagId ?? '');
    final associatedBreeding = breedingRecords.firstWhereOrNull(
      (br) => br.businessId == record['breedingRecordId'],
    );

    // Build specific records list for cascade warnings
    List<String> specificRecords = [];
    int deliveryCount = 0;
    int breedingCount = 0;

    if (associatedDelivery != null) {
      deliveryCount = 1;
      final deliveryId = associatedDelivery['id']?.toString();
      if (deliveryId != null) {
        specificRecords.add(deliveryId);
      }
    }

    if (associatedBreeding != null) {
      breedingCount = 1;
      final breedingId = associatedBreeding.businessId;
      if (breedingId != null) {
        specificRecords.add(breedingId);
      }
    }

    // Show standardized confirmation dialog
    if (!mounted) return;
    final cattleDisplayName = widget.cattle.name != null && widget.cattle.tagId != null
        ? '${widget.cattle.name} (${widget.cattle.tagId})'
        : widget.cattle.name ?? widget.cattle.tagId ?? 'Unknown';
    final confirm = await BreedingMessageUtils.showPregnancyDeleteConfirmation(
      context,
      cattleName: cattleDisplayName,
      recordId: record['id']?.toString(),
      deliveryRecords: deliveryCount,
      breedingRecords: breedingCount,
      specificRecords: specificRecords,
    );

    if (confirm == true && mounted) {
      try {
        setState(() => _isLoading = true);
        _isProcessingState = true;

        // Record ID for the pregnancy record to delete
        final pregnancyId = record['id'] as String?;

        // Store whether this was the active record for later state updates
        final wasActiveRecord = _activePregnancyRecord != null &&
            pregnancyId != null &&
            pregnancyId == _activePregnancyRecord?.id.toString();

        if (pregnancyId != null) {
          // Check if this is a formatted ID with the pattern {cattleId}-Pregnancy-{number}
          final idParts = pregnancyId.toString().split('-');
          final businessId =
              idParts.length >= 3 ? pregnancyId.toString() : null;

          // Delete using the appropriate ID
          await _databaseHelper.breedingHandler
              .deletePregnancyRecord(businessId ?? pregnancyId.toString());

          // Notify listeners of the deletion
          _databaseHelper.notifyRecordUpdate(
              'pregnancy', 'delete', widget.cattle.tagId ?? '', record);
        }

        // Update the cattle's status if needed
        if (wasActiveRecord) {
          // Find the next active pregnancy if any
          final nextActivePregnancy = _pregnancyRecords
              .where((r) =>
                  r.id.toString() != pregnancyId.toString() &&
                  r.status?.toLowerCase() == 'confirmed')
              .firstOrNull;

          if (nextActivePregnancy == null) {
            // No other active pregnancies, update cattle to not pregnant
            final updatedCattle = widget.cattle.copyWith(
              breedingStatus: BreedingStatus()
                ..isPregnant = false
                ..status = 'Open'
                ..expectedCalvingDate = null,
            );

            await _databaseHelper.cattleHandler.updateCattle(updatedCattle);
            widget.onCattleUpdated(updatedCattle);
          } else {
            // Update cattle with the next active pregnancy
            final updatedCattle = widget.cattle.copyWith(
              breedingStatus: BreedingStatus()
                ..isPregnant = true
                ..status = 'Pregnant'
                ..expectedCalvingDate = nextActivePregnancy.expectedCalvingDate,
            );

            await _databaseHelper.cattleHandler.updateCattle(updatedCattle);
            widget.onCattleUpdated(updatedCattle);
          }
        }

        // Reload pregnancy records from database to ensure consistency
        // This prevents temporary incorrect statistics counts
        await _loadPregnancyRecords();

        // Check pregnancy status to update all other pregnancy-related state
        await _checkPregnancyStatus();

        if (mounted) {
          final message = BreedingMessageUtils.pregnancyRecordDeleted(
            deliveryRecords: associatedDelivery != null ? 1 : 0,
          );
          BreedingMessageUtils.showSuccess(context, message);
        }
      } catch (e) {
        if (mounted) {
          setState(() => _isLoading = false);
          BreedingMessageUtils.showError(context, _getReadableErrorMessage(e));
        }
      } finally {
        _isProcessingState = false;
      }
    }
  }

  // Helper method to calculate and update the due date for a pregnancy record
  Future<void> _calculateAndUpdateDueDate(PregnancyRecordIsar record) async {
    if (!mounted || record.startDate == null) return;

    try {
      // Use cached gestation days if available
      int gestationDays;
      if (_cachedGestationDays != null) {
        gestationDays = _cachedGestationDays!;
      } else {
        // Get animal type for this cattle to calculate gestation period
        final animalTypes =
            await _databaseHelper.farmSetupHandler.getAllAnimalTypes();
        if (!mounted) return;

        final animalType = animalTypes.firstWhereOrNull(
              (type) =>
                  type.id.toString() == widget.cattle.animalTypeId?.toString(),
            ) ??
            animalTypes.firstWhereOrNull((type) => true);

        gestationDays = animalType?.defaultGestationDays ?? 280;
        _cachedGestationDays = gestationDays; // Cache for future use
      }

      // Calculate expected calving date
      final expectedCalvingDate =
          record.startDate!.add(Duration(days: gestationDays));

      // Update the pregnancy record with expected calving date
      final updatedRecord =
          record.copyWith(expectedCalvingDate: expectedCalvingDate);

      // Save to database
      await _databaseHelper.breedingHandler
          .updatePregnancyRecord(updatedRecord, updateLinkedBreedingRecord: true);

      // Update state
      _safeSetState(() {
        _activePregnancyRecord = updatedRecord;
        _dueDate = expectedCalvingDate;

        // Update record in list
        final index =
            _pregnancyRecords.indexWhere((r) => r.id == updatedRecord.id);
        if (index >= 0) {
          _pregnancyRecords[index] = updatedRecord;
        }
      });
    } catch (e) {
      debugPrint('Error calculating due date: $e');
    }
  }

  void _generateMilestones(DateTime? breedingDate, int gestationDays) {
    // Skip if we already have milestones or don't have required data
    if (_milestones.isNotEmpty) {
      debugPrint('Milestones already generated, skipping');
      return;
    }

    if (breedingDate == null) {
      debugPrint('Cannot generate milestones: breeding date is null');
      return;
    }

    debugPrint(
        'Generating milestones for breeding date: ${breedingDate.toString()}, gestation days: $gestationDays');

    _milestones = [];
    final now = DateTime.now();

    // Define distinct colors for milestones (no yellow or orange)
    const List<Color> milestoneColors = [
      Color(0xFF1976D2), // Blue
      Color(0xFF7B1FA2), // Purple
      Color(0xFF009688), // Teal (changed from Green)
      Color(0xFF673AB7), // Deep Purple
      Color(0xFF0097A7), // Cyan
    ];

    // Pregnancy confirmation milestone (3 months)
    final confirmationDate = breedingDate.add(const Duration(days: 90));
    _milestones.add({
      'title': 'Pregnancy Confirmation',
      'description': 'Confirm pregnancy via veterinary check',
      'date': confirmationDate,
      'icon': Icons.check_circle,
      'color': milestoneColors[0], // Blue
      'isPassed': now.isAfter(confirmationDate),
    });

    // Mid-pregnancy check milestone (5 months)
    final midCheckDate = breedingDate.add(const Duration(days: 150));
    _milestones.add({
      'title': 'Mid-Pregnancy Check',
      'description': 'Perform mid-pregnancy health assessment',
      'date': midCheckDate,
      'icon': Icons.medical_services,
      'color': milestoneColors[1], // Purple
      'isPassed': now.isAfter(midCheckDate),
    });

    // Dry-off period milestone (60 days before due date)
    final dryOffDate = breedingDate.add(Duration(days: gestationDays - 60));
    _milestones.add({
      'title': 'Dry-Off Period',
      'description': 'Stop milking 60 days before expected calving',
      'date': dryOffDate,
      'icon': Icons.no_drinks,
      'color': milestoneColors[2], // Teal
      'isPassed': now.isAfter(dryOffDate),
    });

    // Due date approaching milestone (7 days before due date)
    final approachingDate = breedingDate.add(Duration(days: gestationDays - 7));
    _milestones.add({
      'title': 'Due Date Approaching',
      'description': 'Prepare for calving within a week',
      'date': approachingDate,
      'icon': Icons.access_alarm,
      'color': milestoneColors[3], // Red
      'isPassed': now.isAfter(approachingDate),
    });

    // Due date milestone
    final dueDate = breedingDate.add(Duration(days: gestationDays));
    _milestones.add({
      'title': 'Expected Due Date',
      'description': 'Expected calving date',
      'date': dueDate,
      'icon': Icons.child_care,
      'color': milestoneColors[4], // Cyan
      'isPassed': now.isAfter(dueDate),
    });

    // Sort milestones by date
    _milestones.sort(
        (a, b) => (a['date'] as DateTime).compareTo(b['date'] as DateTime));

    debugPrint('Generated ${_milestones.length} milestones');
  }

  // Method to check if close to delivery date
  bool _isCloseToDeliveryDate() {
    if (_dueDate == null) return false;

    final now = DateTime.now();
    final daysUntilDue = _dueDate!.difference(now).inDays;

    // Allow recording birth within 30 days before due date or any time after
    return daysUntilDue <= 30;
  }

  // Method to record birth - re-adding the method that was referenced but missing
  Future<void> _recordBirth() async {
    // Show the delivery form dialog
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => DeliveryFormDialog(
        motherTagId: widget.cattle.tagId,
        existingCattle: _allCattle,
      ),
    );

    if (result != null && mounted) {
      try {
        _safeSetState(() => _isLoading = true);

        // Add the delivery record to the database
        if (result.containsKey('motherTagId') &&
            result['motherTagId'] == widget.cattle.tagId) {
          // Format the delivery record properly
          final deliveryRecord = {
            'motherTagId': result['motherTagId'],
            'date': result['deliveryDate'],
            'type': result['deliveryType'],
            'numberOfCalves': result['numberOfCalves'],
            'notes': result['notes'],
            'calfDetails': result['calfDetails'],
            'status': 'Completed',
            'pregnancyId': _activePregnancyRecord?.businessId,
          };

          // Add the delivery record
          await _databaseHelper.breedingHandler
              .addDeliveryRecordFromMap(deliveryRecord);

          // Create new calves if present in the result
          if (result.containsKey('newCalves') && result['newCalves'] is List) {
            for (final calfData in result['newCalves']) {
              if (calfData is Map<String, dynamic>) {
                await _databaseHelper.cattleHandler
                    .addCattle(CattleIsar.fromMap(calfData));
              }
            }
          }
        }

        // Update the pregnancy record status to Completed
        if (_activePregnancyRecord != null) {
          final updatedPregnancyRecord = PregnancyRecordIsar.fromMap(
              Map<String, dynamic>.from(_activePregnancyRecord!.toMap()));
          updatedPregnancyRecord.status = 'Completed';
          // Using endDate instead of completionDate since the getter isn't defined
          updatedPregnancyRecord.endDate = DateTime.now();

          final pregnancyRecord =
              PregnancyRecordIsar.fromMap(updatedPregnancyRecord.toMap());
          await _databaseHelper.breedingHandler
              .managePregnancyRecord(pregnancyRecord);

          // Also update the associated breeding record if it exists
          final breedingRecords = await _databaseHelper.breedingHandler
              .getBreedingRecordsForCattle(widget.cattle.tagId ?? '');
          final matchingRecord = breedingRecords.firstWhere(
            (br) =>
                br.businessId?.toString() ==
                updatedPregnancyRecord.breedingRecordId?.toString(),
            orElse: () => BreedingRecordIsar(),
          );

          if (matchingRecord.businessId != null) {
            // Update the breeding record status to Completed
            final updatedBreedingRecord = matchingRecord.toMap();
            updatedBreedingRecord['status'] = 'Completed';
            updatedBreedingRecord['notes'] =
                'Breeding completed with successful delivery on ${DateFormat('MMM dd, yyyy').format(DateTime.now())}';

            // Save the updated breeding record
            await _databaseHelper.breedingHandler
                .updateBreedingRecordFromMap(updatedBreedingRecord);

            // Notify record update with the map version
            _databaseHelper.notifyRecordUpdate('breeding', 'update',
                widget.cattle.tagId ?? '', updatedBreedingRecord);
          }
        }

        // Update the cattle's status
        final updatedCattle = widget.cattle.copyWith(
          breedingStatus: BreedingStatus()
            ..isPregnant = false
            ..status = 'Open'
            ..expectedCalvingDate = null
            ..lastCalvingDate = DateTime.now(), // Set the calving date to today
        );

        await _databaseHelper.cattleHandler.updateCattle(updatedCattle);

        // Notify parent of update
        widget.onCattleUpdated(updatedCattle);

        // Refresh the records
        await _loadPregnancyRecords();
        await _checkPregnancyStatus();

        if (mounted) {
          final message = BreedingMessageUtils.deliveryRecordCreated();
          BreedingMessageUtils.showSuccess(context, message);
        }
      } catch (e) {
        if (mounted) {
          BreedingMessageUtils.showError(context, 'Error recording birth: $e');
        }
      } finally {
        if (mounted) {
          _safeSetState(() => _isLoading = false);
        }
      }
    }
  }

  // Build the pregnancy history card
  Widget _buildPregnancyHistoryCard() {
    // Convert records to maps for the PregnancyHistoryCard
    if (_isLoading) {
      return Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: const Padding(
          padding: EdgeInsets.all(24.0),
          child: Center(child: CircularProgressIndicator()),
        ),
      );
    }

    // Convert records to maps for the PregnancyHistoryCard
    final records = _pregnancyRecords.map((record) {
      final map = record.toMap();
      // Add cattleName and cattleId to each record for display
      map['cattleName'] = widget.cattle.name ?? '';
      map['cattleId'] = widget.cattle.tagId ?? '';
      return map;
    }).toList();

    // Sort records by date (most recent first)
    records.sort((a, b) {
      final dateA =
          DateTime.tryParse(a['startDate']?.toString() ?? '') ?? DateTime(1900);
      final dateB =
          DateTime.tryParse(b['startDate']?.toString() ?? '') ?? DateTime(1900);
      return dateB.compareTo(dateA);
    });

    return PregnancyHistoryCard(
      records: records,
      title: 'Pregnancy History',
      emptyMessage: 'No pregnancy history available',
      onEdit: _editPregnancyRecord,
      onDelete: _deletePregnancyRecord,
      onStatusTap: _editPregnancyRecord,
    );
  }
}

DateTime? calculateExpectedCalvingDate(
    DateTime? breedingDate, int gestationDays) {
  if (breedingDate == null) return null;
  return breedingDate.add(Duration(days: gestationDays));
}

// Helper method to safely parse dates
DateTime? tryParseDate(String? dateStr) {
  if (dateStr == null) return null;
  try {
    return DateTime.parse(dateStr);
  } catch (e) {
    debugPrint('Error parsing date: $e');
    return null;
  }
}

// Helper method to safely parse dates with a default value
DateTime parseDateWithDefault(String? dateStr, DateTime defaultValue) {
  if (dateStr == null) return defaultValue;
  try {
    return DateTime.parse(dateStr);
  } catch (e) {
    debugPrint('Error parsing date: $e');
    return defaultValue;
  }
}

// Helper method to compare dates for sorting
int compareDates(
    Map<String, dynamic> a, Map<String, dynamic> b, String dateField) {
  final dateA = tryParseDate(a[dateField]?.toString());
  final dateB = tryParseDate(b[dateField]?.toString());

  if (dateA == null && dateB == null) return 0;
  if (dateA == null) return 1;
  if (dateB == null) return -1;

  return dateB.compareTo(dateA); // Sort by most recent first
}
*/
