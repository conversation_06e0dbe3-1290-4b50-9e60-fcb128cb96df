import 'package:logging/logging.dart';
import 'package:isar/isar.dart';
import 'package:flutter/foundation.dart';

import 'package:uuid/uuid.dart';

import '../models/breed_category_isar.dart';
import '../models/animal_type_isar.dart';
import '../models/farm_isar.dart';
import '../models/alert_settings_isar.dart';

import '../../../services/database/isar_service.dart';
import '../../../services/database/exceptions/database_exceptions.dart';
import '../../../Dashboard/Events/models/event_type_isar.dart';
import '../models/currency_settings_isar.dart';

import '../../../Dashboard/Cattle/models/cattle_isar.dart';
// import '../../Transactions/models/transaction_isar.dart';
import '../../widgets/filters/filter_data_service.dart';

// Legacy alias for compatibility
typedef FarmSetupHandler = FarmSetupRepository;

/// Repository for Farm Setup module database operations
/// Note: Extends ChangeNotifier for farm selection state management
/// Follows consistent patterns: proper logging, error handling, and dependency injection
class FarmSetupRepository extends ChangeNotifier {
  static final Logger _logger = Logger('FarmSetupRepository');
  final IsarService _isarService;

  // Public constructor with explicit dependency injection
  FarmSetupRepository(this._isarService);

  // Getter for Isar instance
  Isar get _isar => _isarService.isar;

  // Selected farm ID
  String? _selectedFarmId;

  //=== REACTIVE FARM SETUP STREAMS ===//

  /// Watches all farms with reactive updates
  Stream<List<FarmIsar>> watchAllFarms() {
    return _isar.farmIsars.where().watch(fireImmediately: true);
  }

  /// Watches all breed categories with reactive updates
  Stream<List<BreedCategoryIsar>> watchAllBreedCategories() {
    return _isar.breedCategoryIsars.where().watch(fireImmediately: true);
  }

  /// Watches all animal types with reactive updates
  Stream<List<AnimalTypeIsar>> watchAllAnimalTypes() {
    return _isar.animalTypeIsars.where().watch(fireImmediately: true);
  }

  /// Watches all alert settings with reactive updates
  Stream<List<AlertSettingsIsar>> watchAllAlertSettings() {
    return _isar.alertSettingsIsars.where().watch(fireImmediately: true);
  }

  /// Watches all currency settings with reactive updates
  Stream<List<CurrencySettingsIsar>> watchAllCurrencySettings() {
    return _isar.currencySettingsIsars.where().watch(fireImmediately: true);
  }

  //=== FARM METHODS ===//

  /// Get the active farm
  Future<FarmIsar?> getActiveFarm() async {
    try {
      if (_selectedFarmId != null) {
        return await _isar.farmIsars
            .filter()
            .farmBusinessIdEqualTo(_selectedFarmId!)
            .findFirst();
      }

      // If no selected farm, just return the first one
      return await _isar.farmIsars.where().findFirst();
    } catch (e) {
      _logger.severe('Error getting active farm: $e');
      throw DatabaseException('Failed to retrieve active farm', e.toString());
    }
  }

  /// Get all farms
  Future<List<FarmIsar>> getAllFarms() async {
    try {
      // Remove unnecessary await
      final isar = _isar;
      final farms = await isar.farmIsars.where().sortByName().findAll();

      return farms;
    } catch (e) {
      _logger.severe('Error getting all farms: $e');
      throw DatabaseException('Failed to get all farms', e.toString());
    }
  }

  /// Get selected farm ID
  Future<String> getSelectedFarmId() async {
    try {
      if (_selectedFarmId != null) {
        return _selectedFarmId!;
      }

      // If no selected farm ID, get the ID of the first farm
      final farm = await getActiveFarm();
      if (farm != null) {
        _selectedFarmId = farm.id.toString();
        return _selectedFarmId!;
      }

      return '0'; // Default ID if no farm exists
    } catch (e) {
      _logger.severe('Error getting selected farm ID: $e');
      throw DatabaseException(
          'Failed to retrieve selected farm ID', e.toString());
    }
  }

  /// Set active farm by ID
  Future<void> setActiveFarm(String farmId) async {
    try {
      // Store the selected farm ID
      _selectedFarmId = farmId;

      // Notify listeners that the active farm has changed
      notifyListeners();

      _logger.info('Active farm set to: $farmId');
    } catch (e) {
      _logger.severe('Error setting active farm: $e');
      throw DatabaseException('Failed to set active farm', e.toString());
    }
  }

  /// Add a new farm
  Future<FarmIsar> addFarm(FarmIsar farm) async {
    try {
      // Generate business ID if needed
      if (farm.farmBusinessId?.isEmpty ?? true) {
        farm.farmBusinessId = const Uuid().v4();
      }

      // Set audit fields
      final now = DateTime.now();
      farm.createdAt = now;
      farm.updatedAt = now;

      await _isar.writeTxn(() async {
        await _isar.farmIsars.put(farm);
      });

      _logger.info('Farm added successfully: ${farm.name}');
      notifyListeners();
      return farm;
    } catch (e) {
      _logger.severe('Error adding farm: $e');
      throw DatabaseException('Failed to add farm', e.toString());
    }
  }

  /// Update an existing farm
  Future<FarmIsar> updateFarm(FarmIsar farm) async {
    try {
      // Set audit fields
      farm.updatedAt = DateTime.now();

      await _isar.writeTxn(() async {
        await _isar.farmIsars.put(farm);
      });

      _logger.info('Farm updated successfully: ${farm.name}');
      notifyListeners();
      return farm;
    } catch (e) {
      _logger.severe('Error updating farm: $e');
      throw DatabaseException('Failed to update farm', e.toString());
    }
  }

  /// Delete a farm
  Future<void> deleteFarm(String farmBusinessId) async {
    try {
      await _isar.writeTxn(() async {
        await _isar.farmIsars
            .filter()
            .farmBusinessIdEqualTo(farmBusinessId)
            .deleteAll();
      });

      _logger.info('Farm deleted successfully: $farmBusinessId');
      notifyListeners();
    } catch (e) {
      _logger.severe('Error deleting farm: $e');
      throw DatabaseException('Failed to delete farm', e.toString());
    }
  }

  //=== BREED CATEGORIES ===//

  /// Get all breed categories
  Future<List<BreedCategoryIsar>> getAllBreedCategories() async {
    try {
      final breedCategories =
          await _isar.breedCategoryIsars.where().sortByName().findAll();

      return breedCategories;
    } catch (e) {
      _logger.severe('Error retrieving breed categories: $e');
      throw DatabaseException(
          'Failed to retrieve breed categories', e.toString());
    }
  }

  /// Get breed category by ID
  Future<BreedCategoryIsar?> getBreedCategoryById(String businessId) async {
    try {
      if (businessId.isEmpty) {
        throw ValidationException('Breed category ID is required');
      }

      return await _isar.breedCategoryIsars
          .filter()
          .businessIdEqualTo(businessId)
          .findFirst();
    } catch (e) {
      _logger.severe('Error retrieving breed category by ID: $businessId: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to retrieve breed category', e.toString());
    }
  }

  /// Get breed categories for a specific animal type
  Future<List<BreedCategoryIsar>> getBreedCategoriesForAnimalType(
      String animalTypeId) async {
    try {
      if (animalTypeId.isEmpty) {
        throw ValidationException('Animal type ID is required');
      }

      final breedCategories = await _isar.breedCategoryIsars
          .filter()
          .animalTypeIdEqualTo(animalTypeId)
          .sortByName()
          .findAll();

      return breedCategories;
    } catch (e) {
      _logger.severe(
          'Error retrieving breed categories for animal type: $animalTypeId: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException(
          'Failed to retrieve breed categories for animal type', e.toString());
    }
  }

  /// Add or update a breed category
  Future<void> addOrUpdateBreedCategory(BreedCategoryIsar category) async {
    try {
      await _validateBreedCategory(category);

      await _isar.writeTxn(() async {
        await _isar.breedCategoryIsars.put(category);
      });

      // Clear filter cache after breed category modification
      FilterDataService.instance.clearCache();

      _logger.info('Successfully saved breed category: ${category.businessId}');
    } catch (e) {
      _logger.severe('Error saving breed category: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to save breed category', e.toString());
    }
  }

  /// Delete a breed category
  Future<void> deleteBreedCategory(String businessId) async {
    try {
      if (businessId.isEmpty) {
        throw ValidationException('Breed category ID is required');
      }

      await _isar.writeTxn(() async {
        final category = await _isar.breedCategoryIsars
            .filter()
            .businessIdEqualTo(businessId)
            .findFirst();

        if (category == null) {
          throw RecordNotFoundException(
              'Breed category not found: $businessId');
        }

        // Check if category is in use by any cattle
        final cattleCount = await _isar
            .collection<CattleIsar>()
            .filter()
            .breedIdEqualTo(businessId)
            .count();

        if (cattleCount > 0) {
          throw ValidationException(
              'Cannot delete breed category that is in use by $cattleCount cattle'); // Removed braces
        }

        // For now, we'll assume the category isn't in use
        await _isar.breedCategoryIsars.delete(category.id);
      });

      // Clear filter cache after breed category deletion
      FilterDataService.instance.clearCache();

      _logger.info('Successfully deleted breed category: $businessId');
    } catch (e) {
      _logger.severe('Error deleting breed category: $businessId: $e');
      if (e is ValidationException || e is RecordNotFoundException) rethrow;
      throw DatabaseException('Failed to delete breed category', e.toString());
    }
  }

  //=== ANIMAL TYPES ===//

  /// Get all animal types
  Future<List<AnimalTypeIsar>> getAllAnimalTypes() async {
    try {
      final animalTypes =
          await _isar.animalTypeIsars.where().sortByName().findAll();

      return animalTypes;
    } catch (e) {
      _logger.severe('Error retrieving animal types: $e');
      throw DatabaseException('Failed to retrieve animal types', e.toString());
    }
  }

  /// Get animal type by ID
  Future<AnimalTypeIsar?> getAnimalTypeById(String businessId) async {
    try {
      if (businessId.isEmpty) {
        throw ValidationException('Animal type ID is required');
      }

      return await _isar.animalTypeIsars
          .filter()
          .businessIdEqualTo(businessId)
          .findFirst();
    } catch (e) {
      _logger.severe('Error retrieving animal type by ID: $businessId: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to retrieve animal type', e.toString());
    }
  }

  /// Add or update an animal type
  Future<void> addOrUpdateAnimalType(AnimalTypeIsar animalType) async {
    try {
      await _validateAnimalType(animalType);

      await _isar.writeTxn(() async {
        await _isar.animalTypeIsars.put(animalType);
      });

      // Clear filter cache after animal type modification
      FilterDataService.instance.clearCache();

      _logger.info('Successfully saved animal type: ${animalType.businessId}');
    } catch (e) {
      _logger.severe('Error saving animal type: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to save animal type', e.toString());
    }
  }

  /// Delete an animal type
  Future<void> deleteAnimalType(String businessId) async {
    try {
      if (businessId.isEmpty) {
        throw ValidationException('Animal type ID is required');
      }

      await _isar.writeTxn(() async {
        final animalType = await _isar.animalTypeIsars
            .filter()
            .businessIdEqualTo(businessId)
            .findFirst();

        if (animalType == null) {
          throw RecordNotFoundException('Animal type not found: $businessId');
        }

        // Check if animal type is in use by breed categories
        final breedCategoryCount = await _isar.breedCategoryIsars
            .filter()
            .animalTypeIdEqualTo(businessId)
            .count();

        if (breedCategoryCount > 0) {
          throw ValidationException(
              'Cannot delete animal type that has breed categories');
        }

        // Check if animal type is in use by cattle
        final cattleCount = await _isar
            .collection<CattleIsar>()
            .filter()
            .animalTypeIdEqualTo(businessId)
            .count();

        if (cattleCount > 0) {
          throw ValidationException(
              'Cannot delete animal type that is in use by $cattleCount cattle'); // Removed braces
        }

        // If not in use, proceed with deletion
        await _isar.animalTypeIsars.delete(animalType.id);
      });

      // Clear filter cache after animal type deletion
      FilterDataService.instance.clearCache();

      _logger.info('Successfully deleted animal type: $businessId');
    } catch (e) {
      _logger.severe('Error deleting animal type: $businessId: $e');
      if (e is ValidationException || e is RecordNotFoundException) rethrow;
      throw DatabaseException('Failed to delete animal type', e.toString());
    }
  }

  //=== PRIVATE HELPER METHODS ===//

  /// Validate breed category
  Future<void> _validateBreedCategory(BreedCategoryIsar category) async {
    if (category.name == null || category.name!.isEmpty) {
      throw ValidationException('Breed category name is required');
    }

    if (category.animalTypeId == null || category.animalTypeId!.isEmpty) {
      throw ValidationException('Animal type ID is required');
    }

    // Check if animal type exists
    final animalType = await _isar.animalTypeIsars
        .filter()
        .businessIdEqualTo(category.animalTypeId!)
        .findFirst();

    if (animalType == null) {
      throw ValidationException('Invalid animal type ID');
    }
  }

  /// Validate animal type
  Future<void> _validateAnimalType(AnimalTypeIsar animalType) async {
    if (animalType.name == null || animalType.name!.isEmpty) {
      throw ValidationException('Animal type name is required');
    }

    // Check for duplicate names
    final existing = await _isar.animalTypeIsars
        .filter()
        .nameEqualTo(animalType.name!)
        .and()
        .not()
        .businessIdEqualTo(animalType.businessId ?? '')
        .findFirst();

    if (existing != null) {
      throw ValidationException('Animal type with this name already exists');
    }
  }

  //=== FARM CONFIGURATION ===//

  /// Get farm configuration - replaced with a simple Map implementation
  Future<Map<String, dynamic>> getFarmConfig() async {
    try {
      _logger.info('Starting getFarmConfig method');

      // Debug: Print the current farm ID
      final farmId = _selectedFarmId ?? await getSelectedFarmId();
      _logger.info('Getting farm config for farm ID: $farmId');

      // Return default config
      final config = {
        'farmName': 'Default Farm',
        'location': 'Default Location',
        'createdAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
      };

      _logger.info('Farm location retrieved: ${config['location']}');
      return config;
    } catch (e) {
      _logger.severe('Error getting farm configuration: $e');
      throw DatabaseException(
          'Failed to retrieve farm configuration', e.toString());
    }
  }

  /// Save farm configuration - replaced with a simple logging implementation
  Future<void> saveFarmConfig(Map<String, dynamic> config) async {
    try {
      // Validate the config
      if (config['farmName'] == null || config['farmName'].isEmpty) {
        throw ValidationException('Farm name is required');
      }

      if (config['location'] == null || config['location'].isEmpty) {
        throw ValidationException('Farm location is required');
      }

      _logger.info('Successfully saved farm configuration');
    } catch (e) {
      _logger.severe('Error saving farm configuration: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException(
          'Failed to save farm configuration', e.toString());
    }
  }

  // Event Types methods
  Future<List<EventTypeIsar>> getEventTypes() async {
    try {
      final eventTypes = await _isar.eventTypeIsars.where().findAll();
      return eventTypes;
    } catch (e) {
      _logger.severe('Error getting event types: $e');
      throw DatabaseException('Failed to retrieve event types', e.toString());
    }
  }

  /// Save an event type
  Future<void> saveEventType(EventTypeIsar eventType) async {
    try {
      await _isar.writeTxn(() async {
        await _isar.eventTypeIsars.put(eventType);
      });
    } catch (e) {
      _logger.severe('Error saving event type: $e');
      throw DatabaseException('Failed to save event type', e.toString());
    }
  }

  /// Delete an event type
  Future<void> deleteEventType(int id) async {
    try {
      await _isar.writeTxn(() async {
        await _isar.eventTypeIsars.delete(id);
      });
    } catch (e) {
      _logger.severe('Error deleting event type: $e');
      throw DatabaseException('Failed to delete event type', e.toString());
    }
  }

  // Alert Settings methods
  Future<AlertSettingsIsar> getAlertSettings() async {
    try {
      // Get the current farm ID
      final currentFarmId = _selectedFarmId ?? await getSelectedFarmId();

      _logger.info('Getting alert settings for farm: $currentFarmId');

      // First try to find settings for the current farm
      final settings = await _isar.alertSettingsIsars
          .filter()
          .farmBusinessIdEqualTo(currentFarmId)
          .findFirst();

      if (settings != null) {
        _logger.info('Found existing alert settings for farm $currentFarmId');
        return settings;
      }

      // If not found, create default settings for this farm
      _logger.info('Creating new alert settings for farm $currentFarmId');
      final newSettings = AlertSettingsIsar.create()
        ..farmBusinessId = currentFarmId
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now();

      // Save the new settings
      await _isar.writeTxn(() async {
        await _isar.alertSettingsIsars.put(newSettings);
      });

      return newSettings;
    } catch (e) {
      _logger.severe('Error retrieving alert settings: $e');
      throw DatabaseException(
          'Failed to retrieve alert settings', e.toString());
    }
  }

  Future<void> saveAlertSettings(AlertSettingsIsar settings) async {
    try {
      if (settings.farmBusinessId == null || settings.farmBusinessId!.isEmpty) {
        // If farmBusinessId is missing, set it to the current farm
        final currentFarmId = _selectedFarmId ?? await getSelectedFarmId();
        settings.farmBusinessId = currentFarmId;
        _logger.info('Setting missing farmBusinessId to: $currentFarmId');
      }

      // Update timestamp
      settings.updatedAt = DateTime.now();

      // Save to database
      _logger
          .info('Saving alert settings for farm: ${settings.farmBusinessId}');
      await _isar.writeTxn(() async {
        await _isar.alertSettingsIsars.put(settings);
      });

      _logger.info('Alert settings saved successfully');
    } catch (e) {
      _logger.severe('Error saving alert settings: $e');
      throw DatabaseException('Failed to save alert settings', e.toString());
    }
  }

  //=== CURRENCY SETTINGS ===//

  /// Get currency settings
  Future<CurrencySettingsIsar?> getCurrencySettings() async {
    try {
      return await _isar.currencySettingsIsars.where().findFirst();
    } catch (e) {
      _logger.severe('Error retrieving currency settings: $e');
      throw DatabaseException('Failed to retrieve currency settings', e.toString());
    }
  }

  /// Save currency settings
  Future<void> saveCurrencySettings(CurrencySettingsIsar settings) async {
    try {
      await _isar.writeTxn(() async {
        await _isar.currencySettingsIsars.put(settings);
      });
      _logger.info('Currency settings saved successfully');
    } catch (e) {
      _logger.severe('Error saving currency settings: $e');
      throw DatabaseException('Failed to save currency settings', e.toString());
    }
  }
}
