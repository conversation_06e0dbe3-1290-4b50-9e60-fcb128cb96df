import 'package:flutter/material.dart';
import 'standard_form_dialog.dart';

/// Form field configuration base class
abstract class FormFieldConfig {
  final String key;
  final String label;
  final bool required;
  final String? hint;

  const FormFieldConfig({
    required this.key,
    required this.label,
    this.required = false,
    this.hint,
  });
}

/// Text field configuration
class TextFieldConfig extends FormFieldConfig {
  final String? initialValue;
  final int? maxLines;
  final TextInputType? keyboardType;
  final String? Function(String?)? validator;

  const TextFieldConfig({
    required super.key,
    required super.label,
    super.required = false,
    super.hint,
    this.initialValue,
    this.maxLines = 1,
    this.keyboardType,
    this.validator,
  });
}

/// Date field configuration
class DateFieldConfig extends FormFieldConfig {
  final DateTime? initialValue;
  final DateTime? firstDate;
  final DateTime? lastDate;

  const DateFieldConfig({
    required super.key,
    required super.label,
    super.required = false,
    super.hint,
    this.initialValue,
    this.firstDate,
    this.lastDate,
  });
}

/// Multiline field configuration
class MultilineFieldConfig extends FormFieldConfig {
  final String? initialValue;
  final int maxLines;

  const MultilineFieldConfig({
    required super.key,
    required super.label,
    super.required = false,
    super.hint,
    this.initialValue,
    this.maxLines = 3,
  });
}

/// Custom field configuration
class CustomFieldConfig extends FormFieldConfig {
  final Widget Function(BuildContext context) builder;

  const CustomFieldConfig({
    required super.key,
    required super.label,
    super.required = false,
    super.hint,
    required this.builder,
  });
}

/// Standard form builder widget
class StandardFormBuilder extends StatefulWidget {
  final List<FormFieldConfig> fields;
  final Map<String, dynamic> initialValues;
  final Function(Map<String, dynamic> values) onChanged;

  const StandardFormBuilder({
    super.key,
    required this.fields,
    this.initialValues = const {},
    required this.onChanged,
  });

  @override
  State<StandardFormBuilder> createState() => _StandardFormBuilderState();
}

class _StandardFormBuilderState extends State<StandardFormBuilder> {
  late Map<String, dynamic> _values;
  final Map<String, TextEditingController> _controllers = {};

  @override
  void initState() {
    super.initState();
    _values = Map.from(widget.initialValues);
    
    // Initialize controllers for text fields
    for (final field in widget.fields) {
      if (field is TextFieldConfig || field is MultilineFieldConfig) {
        _controllers[field.key] = TextEditingController(
          text: _values[field.key]?.toString() ?? '',
        );
      }
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  void _updateValue(String key, dynamic value) {
    setState(() {
      _values[key] = value;
    });
    widget.onChanged(_values);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: widget.fields.map((field) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 16.0),
          child: _buildField(field),
        );
      }).toList(),
    );
  }

  Widget _buildField(FormFieldConfig field) {
    switch (field.runtimeType) {
      case TextFieldConfig:
        return _buildTextField(field as TextFieldConfig);
      case DateFieldConfig:
        return _buildDateField(field as DateFieldConfig);
      case MultilineFieldConfig:
        return _buildMultilineField(field as MultilineFieldConfig);
      case CustomFieldConfig:
        return _buildCustomField(field as CustomFieldConfig);
      default:
        return Container();
    }
  }

  Widget _buildTextField(TextFieldConfig field) {
    return TextFormField(
      controller: _controllers[field.key],
      decoration: DialogThemes.textFieldDecoration.copyWith(
        labelText: field.label,
        hintText: field.hint,
      ),
      keyboardType: field.keyboardType,
      maxLines: field.maxLines,
      validator: field.validator,
      onChanged: (value) => _updateValue(field.key, value),
    );
  }

  Widget _buildDateField(DateFieldConfig field) {
    return InkWell(
      onTap: () async {
        final date = await showDatePicker(
          context: context,
          initialDate: field.initialValue ?? DateTime.now(),
          firstDate: field.firstDate ?? DateTime(2000),
          lastDate: field.lastDate ?? DateTime(2100),
        );
        if (date != null) {
          _updateValue(field.key, date);
        }
      },
      child: InputDecorator(
        decoration: DialogThemes.textFieldDecoration.copyWith(
          labelText: field.label,
          hintText: field.hint,
          suffixIcon: const Icon(Icons.calendar_today),
        ),
        child: Text(
          _values[field.key] != null
              ? _values[field.key].toString().split(' ')[0]
              : 'Select date',
        ),
      ),
    );
  }

  Widget _buildMultilineField(MultilineFieldConfig field) {
    return TextFormField(
      controller: _controllers[field.key],
      decoration: DialogThemes.textFieldDecoration.copyWith(
        labelText: field.label,
        hintText: field.hint,
      ),
      maxLines: field.maxLines,
      onChanged: (value) => _updateValue(field.key, value),
    );
  }

  Widget _buildCustomField(CustomFieldConfig field) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          field.label,
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        const SizedBox(height: 8),
        field.builder(context),
      ],
    );
  }
}
