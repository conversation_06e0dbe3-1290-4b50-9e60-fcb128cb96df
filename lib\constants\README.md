# Constants Directory

This directory contains constants and configuration classes used throughout the app.

## AppBar Configuration

The `app_bar.dart` file provides a consistent way to create AppBars throughout the app. There are only 2 AppBar types:

### Design Philosophy

- **Dashboard screens**: Use `withDrawer` for navigation access
- **All other screens**: Use `withBack` for navigation back

### Usage Examples

#### Dashboard AppBar (Only for main dashboard)
```dart
import 'package:flutter/material.dart';
import '../constants/app_bar.dart';

class MainDashboard extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarConfig.withDrawer(
        title: 'Cattle Manager',
        context: context,
        actions: [
          AppBarConfig.notificationsButton(
            onPressed: () {
              // Handle notifications
            },
          ),
        ],
      ),
      drawer: MyDrawer(),
      body: Container(
        // Your dashboard content
      ),
    );
  }
}
```

#### All Other Screens (Module screens, detail screens, etc.)
```dart
import 'package:flutter/material.dart';
import '../constants/app_bar.dart';

class CattleScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarConfig.withBack(
        title: 'Cattle Management',
        context: context,
        actions: [
          AppBarConfig.notificationsButton(
            onPressed: () {
              // Handle notifications
            },
          ),
        ],
      ),
      body: Container(
        // Your screen content
      ),
    );
  }
}
```

#### AppBar with Loading Indicator
```dart
import 'package:flutter/material.dart';
import '../constants/app_bar.dart';

class LoadingScreen extends StatelessWidget {
  final bool isSaving = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarConfig.withBack(
        title: 'Saving Data',
        context: context,
        actions: [
          isSaving
            ? AppBarConfig.loadingIndicator()
            : AppBarConfig.saveButton(
                onPressed: () {
                  // Handle save
                },
              ),
        ],
      ),
      body: Container(
        // Your screen content
      ),
    );
  }
}
```

## AppColors

The `AppColors` class provides a centralized location for all color definitions used throughout the app. Colors are organized by module for better maintainability and consistency.

### Color Organization Philosophy

Colors are organized by module headings. When working on a specific module, all colors used in that module should be placed under its section. This makes it easy to:
- Find module-specific colors
- Maintain consistency within modules
- Update color schemes per module
- Avoid color conflicts between modules

### Global App Colors

```dart
import '../constants/app_colors.dart';

// Primary app colors used throughout the application
Container(color: AppColors.primary)
Text('Title', style: TextStyle(color: AppColors.accent))
```

Available global colors:
- `AppColors.primary` - Primary green color
- `AppColors.accent` - Accent color
- `AppColors.background` - Background color
- `AppColors.error` - Error color
- `AppColors.success` - Success color
- `AppColors.fallback` - Fallback color for unknown data

### Module-Specific Colors

#### Cattle Module Colors
```dart
// Cattle module header
AppBar(backgroundColor: AppColors.cattleHeader)

// Cattle analytics colors
Container(color: AppColors.cattleKpiColors[0])
PieChart(color: AppColors.cattleGenderColors['Male'])
Chart(color: AppColors.cattleAgeDistributionColors['0-6 months'])
```

Available cattle colors:
- `AppColors.cattleHeader` - Cattle module header color
- `AppColors.cattleKpiColors` - List of 6 colors for KPI cards
- `AppColors.cattleAgeInsightColors` - List of 4 colors for age insights
- `AppColors.cattleGenderColors` - Map of gender-specific colors
- `AppColors.cattleAnimalTypeColors` - Map of animal type colors
- `AppColors.cattleAgeDistributionColors` - Map of age distribution colors
- `AppColors.cattleKpiSection` - KPI section header color
- `AppColors.cattleAgeDemographics` - Age demographics section color
- `AppColors.cattleFinancialOverview` - Financial overview section color
- `AppColors.cattleHerdComposition` - Herd composition section color

#### Other Module Colors (To be added when working on those modules)
```dart
// Breeding module (TODO: Add when working on breeding module)
AppBar(backgroundColor: AppColors.breedingHeader)

// Health module (TODO: Add when working on health module)
AppBar(backgroundColor: AppColors.healthHeader)

// Milk module (TODO: Add when working on milk module)
AppBar(backgroundColor: AppColors.milkHeader)

// Weight module (TODO: Add when working on weight module)
AppBar(backgroundColor: AppColors.weightHeader)

// Transaction module (TODO: Add when working on transaction module)
AppBar(backgroundColor: AppColors.transactionHeader)
```

## Universal Layout System

The `app_layout.dart` file provides a unified layout system for consistent screen structures throughout the app. This eliminates layout duplication and ensures responsive design across all screens.

### Layout Philosophy

One unified layout system for the entire app with:
- **Consistent AppBar integration** - Automatic AppBar selection based on layout type
- **Responsive design patterns** - Built-in responsive behavior for all screen sizes
- **Standardized spacing** - Consistent padding, margins, and spacing
- **Module-agnostic** - Works with any module's content
- **State management ready** - Built-in refresh and state handling

### Layout Types

#### 1. Dashboard Layout
```dart
import '../constants/app_layout.dart';

class MainDashboard extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return UniversalLayout.dashboard(
      title: 'Cattle Manager',
      body: ResponsiveGrid.dashboard(
        children: [
          DashboardMenuItem(title: 'Cattle', onTap: () {}),
          DashboardMenuItem(title: 'Health', onTap: () {}),
          DashboardMenuItem(title: 'Breeding', onTap: () {}),
          // ... more items
        ],
      ),
      actions: [
        AppBarConfig.notificationsButton(onPressed: () {}),
      ],
      drawer: MyDrawer(),
      onRefresh: () => _refreshData(),
    );
  }
}
```

#### 2. Tab Screen Layout (Module Screens)
```dart
import '../constants/app_layout.dart';

class CattleScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return UniversalLayout.tabScreen(
      title: 'Cattle Management',
      body: Column(
        children: [
          ReusableTabBar.controlled(
            controller: _tabController,
            tabs: _tabs,
          ),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                CattleAnalyticsTab(),
                CattleRecordsTab(),
                CattleInsightsTab(),
              ],
            ),
          ),
        ],
      ),
      actions: [
        IconButton(icon: Icon(Icons.refresh), onPressed: () {}),
      ],
      floatingActionButton: FloatingActionButton(
        onPressed: () => _addRecord(),
        child: Icon(Icons.add),
      ),
      onRefresh: () => _refreshData(),
    );
  }
}
```

#### 3. List Screen Layout
```dart
import '../constants/app_layout.dart';

class RecordsListScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return UniversalLayout.listScreen(
      title: 'Health Records',
      body: ResponsiveColumn(
        children: [
          SearchBar(),
          FilterChips(),
          Expanded(
            child: ListView.builder(
              itemCount: records.length,
              itemBuilder: (context, index) => RecordCard(records[index]),
            ),
          ),
        ],
      ),
      actions: [
        IconButton(icon: Icon(Icons.filter_list), onPressed: () {}),
      ],
      floatingActionButton: FloatingActionButton(
        onPressed: () => _addRecord(),
        child: Icon(Icons.add),
      ),
      onRefresh: () => _refreshData(),
    );
  }
}
```

#### 4. Detail Screen Layout
```dart
import '../constants/app_layout.dart';

class CattleDetailScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return UniversalLayout.detailScreen(
      title: 'Cattle Details',
      body: ResponsiveColumn(
        children: [
          CattleInfoCard(),
          LayoutSpacing.medium,
          HealthSummaryCard(),
          LayoutSpacing.medium,
          BreedingHistoryCard(),
        ],
      ),
      actions: [
        AppBarConfig.saveButton(onPressed: () => _save()),
      ],
      onBackPressed: () => Navigator.pop(context),
      onRefresh: () => _refreshData(),
    );
  }
}
```

#### 5. Universal Form Dialog Layout
```dart
import '../constants/app_layout.dart';

class AddCattleDialog extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return UniversalFormDialog.add(
      title: 'Add New Cattle',
      formContent: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            TextFormField(decoration: InputDecoration(labelText: 'Name')),
            SizedBox(height: 16),
            DropdownButtonFormField(items: animalTypes),
            SizedBox(height: 16),
            DatePickerField(),
          ],
        ),
      ),
      onCancel: () => Navigator.pop(context),
      onAdd: _handleSave,
      isAdding: _isSaving,
    );
  }
}

// For editing existing records
class EditCattleDialog extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return UniversalFormDialog.edit(
      title: 'Edit Cattle',
      formContent: _buildFormContent(),
      onCancel: () => Navigator.pop(context),
      onUpdate: _handleUpdate,
      isUpdating: _isUpdating,
    );
  }
}
```

### Responsive Grid Helpers

#### Dashboard Grid
```dart
ResponsiveGrid.dashboard(
  children: [
    DashboardCard(title: 'Total Cattle', value: '150'),
    DashboardCard(title: 'Active', value: '142'),
    DashboardCard(title: 'Pregnant', value: '25'),
    DashboardCard(title: 'Health Issues', value: '3'),
  ],
)
```

#### Card Grid
```dart
ResponsiveGrid.cards(
  children: [
    AnalyticsCard(title: 'KPI', data: kpiData),
    AnalyticsCard(title: 'Trends', data: trendData),
    AnalyticsCard(title: 'Reports', data: reportData),
  ],
)
```

### Layout Spacing Helpers

```dart
Column(
  children: [
    HeaderWidget(),
    LayoutSpacing.medium,        // 16px spacing
    ContentWidget(),
    LayoutSpacing.large,         // 24px spacing
    FooterWidget(),
    LayoutSpacing.responsive(context), // Responsive spacing
  ],
)
```

### Benefits

#### 1. **Consistency**
- All screens use the same layout patterns
- Automatic AppBar selection based on screen type
- Consistent spacing and responsive behavior

#### 2. **Maintainability**
- Single source of truth for layout patterns
- Easy to update layout behavior across the entire app
- Centralized responsive design logic

#### 3. **Developer Experience**
- Simple factory constructors for common patterns
- Built-in refresh and state management
- Automatic responsive grid calculations

#### 4. **Performance**
- Optimized responsive calculations
- Consistent widget reuse
- Reduced layout code duplication

## Universal Dialog Buttons

The `app_dialog_buttons.dart` file provides consistent action buttons for all dialogs throughout the app.

### Dialog Button Philosophy

One unified dialog button system with:
- **Primary actions** - Add, Save, Edit, Update with consistent green styling
- **Secondary actions** - Cancel, Clear with appropriate neutral styling
- **Destructive actions** - Delete with red warning styling
- **Loading states** - Built-in loading indicators for async operations
- **Pre-built combinations** - Common button layouts ready to use

### Usage Examples

#### Individual Dialog Buttons
```dart
import '../constants/app_dialog_buttons.dart';

// Primary actions (green styling)
UniversalDialogButtons.add(onPressed: () {}, isLoading: false)
UniversalDialogButtons.save(onPressed: () {}, isLoading: false)
UniversalDialogButtons.update(onPressed: () {}, isLoading: false)

// Secondary actions (neutral styling)
UniversalDialogButtons.cancel(onPressed: () {})
UniversalDialogButtons.clear(onPressed: () {})

// Destructive actions (red styling)
UniversalDialogButtons.delete(onPressed: () {}, isLoading: false)
```

#### Pre-built Button Combinations
```dart
// Cancel + Add combination
UniversalDialogButtons.cancelAddRow(
  onCancel: () => Navigator.pop(context),
  onAdd: () => _handleAdd(),
  isAdding: _isLoading,
)

// Cancel + Save combination
UniversalDialogButtons.cancelSaveRow(
  onCancel: () => Navigator.pop(context),
  onSave: () => _handleSave(),
  isSaving: _isLoading,
)

// Clear + Cancel + Save combination
UniversalDialogButtons.clearCancelSaveRow(
  onClear: () => _clearForm(),
  onCancel: () => Navigator.pop(context),
  onSave: () => _handleSave(),
  isSaving: _isLoading,
)
```

## Universal Tab and FAB System

The `app_tabs.dart` file provides a unified tab bar and floating action button system for consistent navigation and actions throughout the app.

### Tab and FAB Philosophy

One unified tab and FAB system for the entire app with:
- **Number-based configurations** - Support for 2, 3, 4, or 5 tabs
- **Automatic FAB management** - FABs show/hide based on selected tab
- **Flexible theming** - Any color scheme for any module
- **Universal behavior** - Same interaction patterns across all screens

### Universal Tab Bar

#### Number-Based Tab Bars
```dart
import '../constants/app_tabs.dart';

class CattleScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // 3-tab configuration with custom labels, icons, and colors
          UniversalTabBar.threeTabs(
            controller: _tabController,
            labels: ['Analytics', 'Records', 'Insights'],
            icons: [Icons.analytics, Icons.list, Icons.lightbulb],
            colors: [Colors.blue, Colors.green, Colors.purple],
            showFABs: [false, true, false], // FAB only on Records tab
            indicatorColor: Colors.blue,
          ),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                CattleAnalyticsTab(),
                CattleRecordsTab(),
                CattleInsightsTab(),
              ],
            ),
          ),
        ],
      ),
      // FAB automatically managed based on selected tab
      floatingActionButton: _getCurrentFAB(),
    );
  }
}
```

#### Available Tab Configurations
```dart
// 2-tab configuration
UniversalTabBar.twoTabs(
  controller: controller,
  labels: ['Analytics', 'Records'],
  icons: [Icons.analytics, Icons.list],
  colors: [Colors.blue, Colors.green],
  showFABs: [false, true], // Optional: defaults to [false, true]
)

// 3-tab configuration
UniversalTabBar.threeTabs(
  controller: controller,
  labels: ['Analytics', 'Records', 'Insights'],
  icons: [Icons.analytics, Icons.list, Icons.lightbulb],
  colors: [Colors.blue, Colors.green, Colors.purple],
  showFABs: [false, true, false], // Optional: defaults to [false, true, false]
)

// 4-tab configuration (automatically scrollable)
UniversalTabBar.fourTabs(
  controller: controller,
  labels: ['Analytics', 'Records', 'Custom', 'Insights'],
  icons: [Icons.analytics, Icons.list, Icons.star, Icons.lightbulb],
  colors: [Colors.blue, Colors.green, Colors.orange, Colors.purple],
  showFABs: [false, true, false, false], // Optional: defaults to [false, true, false, false]
)

// 5-tab configuration (automatically scrollable)
UniversalTabBar.fiveTabs(
  controller: controller,
  labels: ['Analytics', 'Records', 'Custom1', 'Custom2', 'Insights'],
  icons: [Icons.analytics, Icons.list, Icons.star, Icons.favorite, Icons.lightbulb],
  colors: [Colors.blue, Colors.green, Colors.orange, Colors.red, Colors.purple],
  showFABs: [false, true, false, false, false], // Optional: defaults to [false, true, false, false, false]
)
```

### Universal FAB System

#### Standard FAB Types
```dart
import '../constants/app_tabs.dart';

// Add FAB (most common)
UniversalFAB.add(
  onPressed: () => _addRecord(),
  tooltip: 'Add Record',
  backgroundColor: AppColors.cattleHeader,
)

// Save FAB
UniversalFAB.save(
  onPressed: () => _saveData(),
  tooltip: 'Save Changes',
)

// Edit FAB
UniversalFAB.edit(
  onPressed: () => _editRecord(),
  tooltip: 'Edit Record',
)

// Delete FAB
UniversalFAB.delete(
  onPressed: () => _deleteRecord(),
  tooltip: 'Delete Record',
)

// Extended FAB
UniversalFAB.extended(
  onPressed: () => _addRecord(),
  label: Text('Add Record'),
  icon: Icon(Icons.add),
)
```

### Universal Tab Screen System

#### Complete Tab Screen with Automatic FAB Management
```dart
import '../constants/app_tabs.dart';

class CattleScreen extends StatefulWidget {
  @override
  State<CattleScreen> createState() => _CattleScreenState();
}

class _CattleScreenState extends State<CattleScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  late UniversalTabScreen _tabScreen;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _tabController.addListener(() => setState(() {})); // Rebuild for FAB changes

    _tabScreen = UniversalTabScreen.cattle(
      controller: _tabController,
      tabViews: [
        CattleAnalyticsTab(),
        CattleRecordsTab(),
        CattleInsightsTab(),
      ],
      onAddCattle: () => _showAddDialog(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return UniversalLayout.tabScreen(
      title: 'Cattle Management',
      body: _tabScreen,
      floatingActionButton: _tabScreen.getCurrentFAB(), // Automatic FAB management
    );
  }
}
```

#### Available Module Tab Screens
```dart
// Cattle module
UniversalTabScreen.cattle(
  controller: controller,
  tabViews: [AnalyticsTab(), RecordsTab(), InsightsTab()],
  onAddCattle: () {},
)

// Breeding module
UniversalTabScreen.breeding(
  controller: controller,
  tabViews: [AnalyticsTab(), BreedingTab(), PregnancyTab(), DeliveryTab(), InsightsTab()],
  onAddBreeding: () {},
)

// Health module
UniversalTabScreen.health(
  controller: controller,
  tabViews: [AnalyticsTab(), RecordsTab(), TreatmentsTab(), VaccinesTab(), InsightsTab()],
  onAddHealth: () {},
)

// And so on for other modules...
```

### Tab Configuration System

#### Predefined Tab Configurations
Each module has predefined tab configurations with:
- **Consistent tab labels and icons**
- **Module-specific colors**
- **FAB behavior per tab** (which tabs show FABs)
- **Automatic scrolling** for modules with 4+ tabs

```dart
// Example: Cattle module configuration
ModuleTabConfigs.cattle(onAddCattle: () {})
// Returns:
// [
//   UniversalTabConfig(label: 'Analytics', icon: Icons.analytics, showFAB: false),
//   UniversalTabConfig(label: 'Records', icon: Icons.list, showFAB: true, fabWidget: AddFAB),
//   UniversalTabConfig(label: 'Insights', icon: Icons.lightbulb, showFAB: false),
// ]
```

### Benefits

#### 1. **Consistency**
- All modules use the same tab bar design and behavior
- Consistent FAB positioning and styling
- Standardized tab colors and icons

#### 2. **Maintainability**
- Single source of truth for tab configurations
- Easy to update tab behavior across all modules
- Centralized FAB styling and management

#### 3. **Developer Experience**
- Simple factory constructors for each module
- Automatic FAB show/hide based on tab selection
- Predefined configurations eliminate setup code

#### 4. **Performance**
- Optimized tab switching animations
- Consistent widget reuse across modules
- Reduced code duplication

## 🎨 Universal Form Dialog System

The Universal Form Dialog System provides consistent form dialog layout with:
- **Fixed Header**: Solid green background (#2E7D32) with white text and icons
- **Scrollable Content**: Form content area that adapts to content size
- **Fixed Footer**: Action buttons with consistent styling
- **Responsive Sizing**: Automatic sizing based on screen dimensions

### Key Features
- **Consistent Header Design**: Green background with white text/icons across all forms
- **Standardized Action Buttons**: Uses existing `UniversalDialogButtons` for consistency
- **Loading States**: Built-in loading indicators for async operations
- **Responsive Layout**: Adapts to different screen sizes automatically
- **Scrollable Content**: Handles long forms with proper scrolling
- **No Button Duplication**: Leverages existing button system from `app_dialog_buttons.dart`

### Factory Constructors

#### Add Form Dialog
```dart
UniversalFormDialog.add(
  title: 'Add New Record',
  formContent: _buildFormContent(),
  onCancel: () => Navigator.pop(context),
  onAdd: _handleSave,
  addText: 'Save', // Optional, defaults to 'Add'
  isAdding: _isSaving, // Loading state
)
```

#### Edit Form Dialog
```dart
UniversalFormDialog.edit(
  title: 'Edit Record',
  formContent: _buildFormContent(),
  onCancel: () => Navigator.pop(context),
  onUpdate: _handleUpdate,
  updateText: 'Update', // Optional, defaults to 'Update'
  isUpdating: _isUpdating, // Loading state
)
```

### Implementation Examples

#### Cattle Form Dialog
- ✅ **Updated**: Uses UniversalFormDialog.add/edit
- **Header**: Green background with add_circle/edit icons
- **Content**: Comprehensive cattle information form
- **Footer**: Cancel + Add/Update buttons

#### Weight Form Dialog
- ✅ **Updated**: Uses UniversalFormDialog.add/edit
- **Header**: Green background with scale icon
- **Content**: Weight measurement form
- **Footer**: Cancel + Save/Update buttons

#### Health Record Form Dialog
- ✅ **Updated**: Uses UniversalFormDialog.add/edit
- **Header**: Green background with add_circle/edit icons
- **Content**: Health record information form
- **Footer**: Cancel + Save buttons

#### Vaccination Form Dialog
- ✅ **Updated**: Uses UniversalFormDialog.add/edit
- **Header**: Green background with add_circle/edit icons
- **Content**: Vaccination record form
- **Footer**: Cancel + Save buttons

### Design Specifications
- **Header Color**: #2E7D32 (solid green)
- **Header Text**: White color, 18px, bold weight
- **Header Icon**: White color, consistent with action type
- **Action Buttons**: Uses `UniversalDialogButtons.cancelAddRow()` and `UniversalDialogButtons.cancelUpdateRow()`
- **Button Styling**: Inherits from existing `app_dialog_buttons.dart` system
- **Border Radius**: 16px for dialog, 12px for buttons (from existing button system)
- **Padding**: 16px for header/footer, configurable for content

### Integration with Existing Systems
- **Leverages `UniversalDialogButtons`**: No duplicate button definitions
- **Consistent with App-wide Button System**: All buttons follow the same styling rules
- **Maintains Single Source of Truth**: Button styling managed in `app_dialog_buttons.dart`

This form dialog system ensures consistency across all form dialogs while providing the flexibility needed for different types of content and interactions, without duplicating existing button functionality.