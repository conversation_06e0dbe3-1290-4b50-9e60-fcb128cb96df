import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../models/cattle_isar.dart';
import '../../Health/models/health_record_isar.dart';
import '../../Health/models/medication_isar.dart';
import '../../Health/models/vaccination_record_isar.dart';
import '../../Health/services/health_repository.dart';
import '../../Health/dialogs/vaccination_form_dialog.dart';

class HealthRecordsView extends StatefulWidget {
  final CattleIsar cattle;

  const HealthRecordsView({Key? key, required this.cattle}) : super(key: key);

  @override
  State<HealthRecordsView> createState() => _HealthRecordsViewState();
}

class _HealthRecordsViewState extends State<HealthRecordsView> {
  late final HealthRepository _healthRepository;
  List<HealthRecordIsar> healthRecords = [];
  List<MedicationIsar> medications = [];
  List<VaccinationIsar> vaccinations = [];

  @override
  void initState() {
    super.initState();
    _initializeHealthService();
  }

  Future<void> _initializeHealthService() async {
    _healthRepository = GetIt.instance<HealthRepository>();
    _loadHealthData();
  }

  Future<void> _showAddHealthRecordDialog() async {
    String condition = '';
    String treatment = '';
    String veterinarian = '';
    double cost = 0.0;
    String notes = '';

    if (!mounted) return;
    final BuildContext dialogContext = context;

    await showDialog(
      context: dialogContext,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('Add Health Record'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                decoration: const InputDecoration(labelText: 'Condition'),
                onChanged: (value) => condition = value,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Treatment'),
                onChanged: (value) => treatment = value,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Veterinarian'),
                onChanged: (value) => veterinarian = value,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Cost'),
                keyboardType: TextInputType.number,
                onChanged: (value) => cost = double.tryParse(value) ?? 0.0,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Notes'),
                onChanged: (value) => notes = value,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              if (condition.isNotEmpty && treatment.isNotEmpty) {
                final record = HealthRecordIsar.fromCattleModel(
                  cattleBusinessId: widget.cattle.businessId ?? '',
                  date: DateTime.now(),
                  condition: condition,
                  treatment: treatment,
                  veterinarian: veterinarian,
                  cost: cost,
                  notes: notes,
                );
                Navigator.pop(context);
                await _healthRepository.addHealthRecord(record);
                if (!mounted) return;
                await _loadHealthData();
              }
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  Future<void> _showAddMedicationDialog() async {
    String medName = '';
    String dosage = '';
    String frequency = '';
    double medCost = 0.0;
    String medNotes = '';

    if (!mounted) return;
    final BuildContext dialogContext = context;

    await showDialog(
      context: dialogContext,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('Add Medication'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                decoration: const InputDecoration(labelText: 'Medication Name'),
                onChanged: (value) => medName = value,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Dosage'),
                onChanged: (value) => dosage = value,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Frequency'),
                onChanged: (value) => frequency = value,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Cost'),
                keyboardType: TextInputType.number,
                onChanged: (value) => medCost = double.tryParse(value) ?? 0.0,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Notes'),
                onChanged: (value) => medNotes = value,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              if (medName.isNotEmpty && dosage.isNotEmpty) {
                final medication = MedicationIsar.create(
                  cattleBusinessId: widget.cattle.businessId ?? '',
                  name: medName,
                  dosage: dosage,
                  frequency: frequency,
                  startDate: DateTime.now(),
                  notes: medNotes,
                  cost: medCost,
                );
                Navigator.pop(context);
                await _healthRepository.addTreatment(medication);
                if (!mounted) return;
                _loadHealthData();
              }
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  Future<void> _showAddVaccinationDialog() async {
    if (!mounted) return;
    
    await showDialog(
      context: context,
      builder: (BuildContext context) => VaccinationFormDialog(
        cattleId: widget.cattle.businessId ?? '',
        cattle: null, // TODO: Fix case sensitivity issue with CattleIsar imports
        onSave: (vaccination) async {
          await _healthRepository.addOrUpdateVaccination(
            vaccination.cattleId ?? '',
            vaccination
          );
          if (!mounted) return true;
          await _loadHealthData();
          return true;
        },
      ),
    );
  }

  Future<void> _loadHealthData() async {
    if (!mounted) return;
    final records =
        await _healthRepository.getHealthRecordsForCattle(widget.cattle.businessId ?? '');
    final meds =
        await _healthRepository.getMedicationsForCattle(widget.cattle.businessId ?? '');
    final vacs =
        await _healthRepository.getVaccinationsForCattle(widget.cattle.businessId ?? '');

    if (!mounted) return;
    setState(() {
      healthRecords = records;
      medications = meds;
      vaccinations = vacs;
    });
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 3,
      child: Column(
        children: [
          const TabBar(
            tabs: [
              Tab(text: 'Health Records'),
              Tab(text: 'Medications'),
              Tab(text: 'Vaccinations'),
            ],
          ),
          Expanded(
            child: TabBarView(
              children: [
                _buildHealthRecordsTab(),
                _buildMedicationsTab(),
                _buildVaccinationsTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHealthRecordsTab() {
    return Column(
      children: [
        ElevatedButton(
          onPressed: _showAddHealthRecordDialog,
          child: const Text('Add Health Record'),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: healthRecords.length,
            itemBuilder: (context, index) {
              final record = healthRecords[index];
              return ListTile(
                title: Text(record.condition ?? 'No condition'),
                subtitle: Text(
                  'Treatment: ${record.treatment ?? 'None'}\nVeterinarian: ${record.veterinarian ?? 'None'}\nCost: \$${record.cost?.toStringAsFixed(2) ?? '0.00'}',
                ),
                trailing: Text(record.date.toString().split(' ')[0]),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildMedicationsTab() {
    return Column(
      children: [
        ElevatedButton(
          onPressed: _showAddMedicationDialog,
          child: const Text('Add Medication'),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: medications.length,
            itemBuilder: (context, index) {
              final medication = medications[index];
              return ListTile(
                title: Text(medication.name ?? 'No name'),
                subtitle: Text(
                  'Dosage: ${medication.dosage ?? 'None'}\nFrequency: ${medication.frequency ?? 'None'}\nCost: \$${medication.cost?.toStringAsFixed(2) ?? '0.00'}',
                ),
                trailing: Text(medication.startDate.toString().split(' ')[0]),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildVaccinationsTab() {
    return Column(
      children: [
        ElevatedButton(
          onPressed: _showAddVaccinationDialog,
          child: const Text('Add Vaccination'),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: vaccinations.length,
            itemBuilder: (context, index) {
              final vaccination = vaccinations[index];
              return ListTile(
                title: Text(vaccination.vaccineName ?? 'No name'),
                subtitle: Text(
                  'Batch: ${vaccination.batchNumber ?? 'None'}\nManufacturer: ${vaccination.manufacturer ?? 'None'}\nCost: \$${vaccination.cost?.toStringAsFixed(2) ?? '0.00'}',
                ),
                trailing: Text(vaccination.date.toString().split(' ')[0]),
              );
            },
          ),
        ),
      ],
    );
  }
}
