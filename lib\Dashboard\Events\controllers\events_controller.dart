import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:isar/isar.dart';
import 'package:async/async.dart';
import '../models/event_isar.dart';
import '../models/event_type_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../services/events_repository.dart';
import '../services/event_analytics_service.dart';
import '../../Cattle/services/cattle_repository.dart';
import '../../Cattle/controllers/cattle_controller.dart'; // Import for ControllerState enum

/// Filter state object for decoupled filter management
/// Following the established FilterController pattern for consistency
class EventFilterState {
  final String? searchQuery;
  final DateTime? startDate;
  final DateTime? endDate;
  final String? eventType;
  final String? status;
  final String? priority;
  final String? cattleId;

  const EventFilterState({
    this.searchQuery,
    this.startDate,
    this.endDate,
    this.eventType,
    this.status,
    this.priority,
    this.cattleId,
  });

  /// Check if any filters are active
  bool get hasActiveFilters =>
      (searchQuery?.isNotEmpty ?? false) ||
      startDate != null ||
      endDate != null ||
      (eventType?.isNotEmpty ?? false) ||
      (status?.isNotEmpty ?? false) ||
      (priority?.isNotEmpty ?? false) ||
      (cattleId?.isNotEmpty ?? false);

  /// Create a copy with updated values
  EventFilterState copyWith({
    String? searchQuery,
    DateTime? startDate,
    DateTime? endDate,
    String? eventType,
    String? status,
    String? priority,
    String? cattleId,
  }) {
    return EventFilterState(
      searchQuery: searchQuery ?? this.searchQuery,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      eventType: eventType ?? this.eventType,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      cattleId: cattleId ?? this.cattleId,
    );
  }

  /// Clear all filters
  static const EventFilterState empty = EventFilterState();
}

/// Reactive controller for the main events screen using Dual-Stream Pattern
/// Following the cattle module template: separate unfiltered and filtered streams
/// Unfiltered stream feeds analytics, filtered stream feeds UI
class EventsController extends ChangeNotifier {
  // Repositories
  final EventsRepository _eventsRepository = GetIt.instance<EventsRepository>();
  final CattleRepository _cattleRepository = GetIt.instance<CattleRepository>();
  final Isar _isar = GetIt.instance<Isar>();

  // State management
  ControllerState _state = ControllerState.loading;
  String? _errorMessage;

  // Stream subscriptions for real-time updates - Separated for filtered/unfiltered data
  StreamSubscription? _unfilteredStreamSubscription;
  StreamSubscription? _filteredStreamSubscription;

  // Data - Critical separation: filtered data for UI, unfiltered for analytics
  List<EventIsar> _unfilteredEvents = []; // Complete dataset for analytics calculations
  List<EventTypeIsar> _unfilteredEventTypes = []; // Complete event types dataset
  List<CattleIsar> _unfilteredCattle = []; // Complete cattle dataset for analytics
  
  List<EventIsar> _filteredEvents = []; // Filtered dataset for UI display
  bool _hasActiveFilters = false; // Track if filters are currently applied

  // Analytics result from the dedicated service - ALWAYS calculated on unfiltered data
  EventAnalyticsResult _analyticsResult = EventAnalyticsResult.empty;

  // Lookup maps for O(1) access
  Map<String, EventTypeIsar> _eventTypeMap = {};

  // Filter state management - decoupled from UI
  EventFilterState _currentFilters = EventFilterState.empty;

  // Getters
  ControllerState get state => _state;
  String? get errorMessage => _errorMessage;

  /// Returns the filtered events for UI display
  /// This is what the EventsTab should show
  List<EventIsar> get events => List.unmodifiable(_filteredEvents);

  /// Returns the complete unfiltered events for analytics
  /// This ensures analytics are always calculated on the full dataset
  List<EventIsar> get unfilteredEvents => List.unmodifiable(_unfilteredEvents);

  /// Returns the complete unfiltered event types for analytics
  List<EventTypeIsar> get unfilteredEventTypes => List.unmodifiable(_unfilteredEventTypes);

  /// Returns the complete unfiltered cattle list for analytics
  List<CattleIsar> get unfilteredCattle => List.unmodifiable(_unfilteredCattle);

  // Main analytics object - single source of truth
  EventAnalyticsResult get analytics => _analyticsResult;

  // Lookup maps for O(1) access
  Map<String, EventTypeIsar> get eventTypeMap => Map.unmodifiable(_eventTypeMap);

  // Filter state access
  EventFilterState get currentFilters => _currentFilters;

  // Convenience getters for backward compatibility with UI
  int get totalEvents => _analyticsResult.totalEvents;
  int get upcomingEvents => _analyticsResult.upcomingEvents;
  int get overdueEvents => _analyticsResult.overdueEvents;
  int get completedEvents => _analyticsResult.completedEvents;
  int get pendingEvents => _analyticsResult.pendingEvents;
  int get cancelledEvents => _analyticsResult.cancelledEvents;
  double get completionRate => _analyticsResult.completionRate;
  double get averageEventDuration => _analyticsResult.averageEventDuration;
  Map<String, int> get eventTypeDistribution => _analyticsResult.eventTypeDistribution;
  Map<String, int> get eventStatusDistribution => _analyticsResult.eventStatusDistribution;
  Map<String, int> get eventPriorityDistribution => _analyticsResult.eventPriorityDistribution;
  Map<String, int> get cattleEventDistribution => _analyticsResult.cattleEventDistribution;
  String get mostCommonEventType => _analyticsResult.mostCommonEventType;
  String get busiestMonth => _analyticsResult.busiestMonth;
  int get eventsThisWeek => _analyticsResult.eventsThisWeek;
  int get eventsThisMonth => _analyticsResult.eventsThisMonth;
  int get eventsNextWeek => _analyticsResult.eventsNextWeek;
  double get averageEventsPerCattle => _analyticsResult.averageEventsPerCattle;
  int get recurringEvents => _analyticsResult.recurringEvents;
  double get eventEfficiencyScore => _analyticsResult.eventEfficiencyScore;
  bool get hasData => _unfilteredEvents.isNotEmpty;

  // Constructor
  EventsController() {
    _initializeStreamListeners();
  }

  /// Initialize stream listeners for real-time updates using Dual-Stream Pattern
  /// Critical: Separate streams for unfiltered (analytics) and filtered (UI) data
  void _initializeStreamListeners() {
    // Primary stream: Unfiltered data for analytics calculations
    // This stream NEVER changes and always provides the complete dataset
    final combinedStream = StreamZip([
      _eventsRepository.watchAllEvents(),
      _eventsRepository.watchAllEventTypes(),
      _cattleRepository.watchAllCattle(),
    ]);

    _unfilteredStreamSubscription = combinedStream.listen((data) {
      _handleUnfilteredDataUpdate(
        data[0] as List<EventIsar>,
        data[1] as List<EventTypeIsar>,
        data[2] as List<CattleIsar>,
      );
    });

    // Initially, filtered data equals unfiltered data (no filters applied)
    _filteredEvents = _unfilteredEvents;
    _hasActiveFilters = false;
  }

  /// Handle unfiltered data updates - Used for analytics calculations
  /// Critical: This method ONLY updates analytics and unfiltered data
  void _handleUnfilteredDataUpdate(
    List<EventIsar> unfilteredEvents,
    List<EventTypeIsar> unfilteredEventTypes,
    List<CattleIsar> unfilteredCattle,
  ) async {
    try {
      // Update the complete unfiltered datasets
      _unfilteredEvents = unfilteredEvents;
      _unfilteredEventTypes = unfilteredEventTypes;
      _unfilteredCattle = unfilteredCattle;

      // Create lookup map for O(1) access
      _eventTypeMap = {
        for (var eventType in _unfilteredEventTypes)
          if (eventType.businessId != null) eventType.businessId!: eventType
      };

      // ALWAYS calculate analytics on the complete unfiltered dataset
      // This ensures analytics remain accurate regardless of applied filters
      if (_unfilteredEvents.isEmpty) {
        _analyticsResult = EventAnalyticsResult.empty;
      } else {
        _calculateAnalytics();
      }

      // If no filters are active, update filtered data to match unfiltered
      if (!_hasActiveFilters) {
        _filteredEvents = List.from(_unfilteredEvents);
      }

      _setState(ControllerState.loaded);
      notifyListeners();
    } catch (e) {
      debugPrint('Error handling unfiltered data update: $e');
      _setState(ControllerState.error);
      _errorMessage = 'Failed to update unfiltered data: $e';
      notifyListeners();
    }
  }

  /// Calculate analytics using the dedicated service
  /// Critical: ALWAYS uses unfiltered data to ensure accurate analytics
  void _calculateAnalytics() {
    _analyticsResult = EventAnalyticsService.calculate(
      _unfilteredEvents, // Use unfiltered data for accurate analytics
      _unfilteredEventTypes,
      _unfilteredCattle,
    );
  }

  /// Apply filters using the FilterController pattern for decoupled filter management
  /// Critical Fix: This method now creates a separate filtered stream without affecting analytics
  void applyFilters(EventFilterState filterState) async {
    // Update current filter state
    _currentFilters = filterState;

    // Cancel existing filtered subscription (but keep unfiltered stream for analytics)
    _filteredStreamSubscription?.cancel();

    // Check if filters are active
    _hasActiveFilters = filterState.hasActiveFilters;

    if (_hasActiveFilters) {
      // Build filtered query for UI display
      final filteredQuery = _buildFilteredQuery(filterState);

      // Create separate stream for filtered data
      _filteredStreamSubscription = filteredQuery.watch(fireImmediately: true)
          .listen((filteredList) {
        _handleFilteredDataUpdate(filteredList);
      });
    } else {
      // No filters: filtered data equals unfiltered data
      _filteredEvents = List.from(_unfilteredEvents);
      notifyListeners();
    }
  }

  /// Convenience method for backward compatibility and simple filter updates
  void updateFilters({
    String? searchQuery,
    DateTime? startDate,
    DateTime? endDate,
    String? eventType,
    String? status,
    String? priority,
    String? cattleId,
  }) {
    final newFilters = _currentFilters.copyWith(
      searchQuery: searchQuery,
      startDate: startDate,
      endDate: endDate,
      eventType: eventType,
      status: status,
      priority: priority,
      cattleId: cattleId,
    );
    applyFilters(newFilters);
  }

  /// Clear all filters
  void clearFilters() {
    applyFilters(EventFilterState.empty);
  }

  /// Handle filtered data updates - Used for UI display only
  /// Critical: This method NEVER affects analytics calculations
  void _handleFilteredDataUpdate(List<EventIsar> filteredList) async {
    try {
      // Update only the filtered dataset for UI display
      _filteredEvents = filteredList;

      // Do NOT recalculate analytics here - they're handled by unfiltered stream
      // This ensures analytics always reflect the complete dataset

      notifyListeners();
    } catch (e) {
      debugPrint('Error handling filtered data update: $e');
      // Don't change state to error for filtered data issues
      notifyListeners();
    }
  }

  /// Build filtered Isar query based on FilterState object
  /// This method dynamically constructs database queries for optimal performance
  dynamic _buildFilteredQuery(EventFilterState filterState) {
    // Start with base query and build the chain using dynamic typing
    dynamic currentQuery = _isar.eventIsars.where();

    // Apply search filter at database level
    if (filterState.searchQuery?.isNotEmpty ?? false) {
      final searchTerm = filterState.searchQuery!.toLowerCase();
      currentQuery = currentQuery.filter().group((q) => q
          .titleContains(searchTerm, caseSensitive: false)
          .or()
          .descriptionContains(searchTerm, caseSensitive: false));
    }

    // Apply date range filters at database level
    if (filterState.startDate != null) {
      currentQuery = currentQuery.filter().scheduledDateGreaterThan(filterState.startDate!);
    }
    if (filterState.endDate != null) {
      // Add one day to make end date inclusive
      final inclusiveEndDate = filterState.endDate!.add(const Duration(days: 1));
      currentQuery = currentQuery.filter().scheduledDateLessThan(inclusiveEndDate);
    }

    // Apply event type filter
    if (filterState.eventType?.isNotEmpty ?? false) {
      currentQuery = currentQuery.filter().eventTypeEqualTo(filterState.eventType);
    }

    // Apply status filter
    if (filterState.status?.isNotEmpty ?? false) {
      currentQuery = currentQuery.filter().statusEqualTo(filterState.status);
    }

    // Apply priority filter
    if (filterState.priority?.isNotEmpty ?? false) {
      currentQuery = currentQuery.filter().priorityEqualTo(filterState.priority);
    }

    // Apply cattle filter
    if (filterState.cattleId?.isNotEmpty ?? false) {
      currentQuery = currentQuery.filter().cattleIdEqualTo(filterState.cattleId);
    }

    // Apply sorting at database level for optimal performance
    currentQuery = currentQuery.sortByScheduledDateDesc(); // Default sort by scheduled date (newest first)

    return currentQuery;
  }

  // CRUD Methods - Single Source of Truth pattern
  // These methods only update the database, stream handles UI updates
  // Exceptions bubble up naturally to be handled by higher-level error handlers

  /// Add new event - only updates database, stream handles UI update
  Future<void> addEvent(EventIsar event) async {
    await _eventsRepository.saveEvent(event);
    // Stream will handle the UI update automatically
  }

  /// Update event - only updates database, stream handles UI update
  Future<void> updateEvent(EventIsar updatedEvent) async {
    await _eventsRepository.saveEvent(updatedEvent);
    // Stream will handle the UI update automatically
  }

  /// Delete event - only updates database, stream handles UI update
  Future<void> deleteEvent(int eventId) async {
    await _eventsRepository.deleteEvent(eventId);
    // Stream will handle the UI update automatically
  }

  /// Add new event type - only updates database, stream handles UI update
  Future<void> addEventType(EventTypeIsar eventType) async {
    await _eventsRepository.saveEventType(eventType);
    // Stream will handle the UI update automatically
  }

  // Helper methods
  EventTypeIsar? getEventType(String? eventTypeId) {
    if (eventTypeId == null) return null;
    return _eventTypeMap[eventTypeId];
  }

  String getEventTypeName(String? eventTypeId) {
    final eventType = getEventType(eventTypeId);
    return eventType?.name ?? 'Unknown Event Type';
  }

  CattleIsar? getCattle(String? cattleId) {
    if (cattleId == null) return null;
    try {
      return _unfilteredCattle.firstWhere(
        (cattle) => cattle.businessId == cattleId,
      );
    } catch (e) {
      return null;
    }
  }

  String getCattleName(String? cattleId) {
    final cattle = getCattle(cattleId);
    return cattle?.name ?? 'Unknown Cattle';
  }

  // State management helpers
  void _setState(ControllerState newState) {
    _state = newState;
    _errorMessage = null;
    notifyListeners();
  }

  void _setError(String error) {
    _state = ControllerState.error;
    _errorMessage = error;
    notifyListeners();
  }

  @override
  void dispose() {
    _unfilteredStreamSubscription?.cancel();
    _filteredStreamSubscription?.cancel();
    super.dispose();
  }
}
