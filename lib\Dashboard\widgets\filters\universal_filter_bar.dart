import 'package:flutter/material.dart';

/// Universal Filter Bar Widget
/// 
/// A reusable filter bar widget that provides consistent filtering UI across modules.
class UniversalFilterBar extends StatelessWidget {
  final String searchHint;
  final String? searchValue;
  final ValueChanged<String>? onSearchChanged;
  final VoidCallback? onFilterPressed;
  final VoidCallback? onSortPressed;
  final VoidCallback? onClearFilters;
  final bool hasActiveFilters;
  final Color? primaryColor;

  const UniversalFilterBar({
    Key? key,
    this.searchHint = 'Search...',
    this.searchValue,
    this.onSearchChanged,
    this.onFilterPressed,
    this.onSortPressed,
    this.onClearFilters,
    this.hasActiveFilters = false,
    this.primaryColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Search bar
          TextField(
            onChanged: onSearchChanged,
            decoration: InputDecoration(
              hintText: searchHint,
              prefixIcon: const Icon(Icons.search),
              suffixIcon: searchValue?.isNotEmpty == true
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () => onSearchChanged?.call(''),
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: Theme.of(context).colorScheme.surface,
            ),
          ),
          
          const SizedBox(height: 12),
          
          // Filter buttons
          Row(
            children: [
              // Filter button
              if (onFilterPressed != null)
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: onFilterPressed,
                    icon: Icon(
                      Icons.filter_list,
                      color: hasActiveFilters ? primaryColor : null,
                    ),
                    label: Text(
                      hasActiveFilters ? 'Filters (Active)' : 'Filters',
                      style: TextStyle(
                        color: hasActiveFilters ? primaryColor : null,
                        fontWeight: hasActiveFilters ? FontWeight.bold : null,
                      ),
                    ),
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(
                        color: hasActiveFilters 
                            ? (primaryColor ?? Theme.of(context).primaryColor)
                            : Theme.of(context).dividerColor,
                      ),
                    ),
                  ),
                ),
              
              if (onFilterPressed != null && onSortPressed != null)
                const SizedBox(width: 12),
              
              // Sort button
              if (onSortPressed != null)
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: onSortPressed,
                    icon: const Icon(Icons.sort),
                    label: const Text('Sort'),
                  ),
                ),
              
              // Clear filters button
              if (hasActiveFilters && onClearFilters != null) ...[
                const SizedBox(width: 12),
                IconButton(
                  onPressed: onClearFilters,
                  icon: const Icon(Icons.clear_all),
                  tooltip: 'Clear all filters',
                  color: Colors.red,
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }
}
