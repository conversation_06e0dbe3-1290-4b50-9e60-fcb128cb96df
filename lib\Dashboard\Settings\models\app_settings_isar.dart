import 'package:isar/isar.dart';

part 'app_settings_isar.g.dart';

/// App settings storage for the application
@collection
class AppSettingsIsar {
  /// Auto-incremented ID
  Id id = Isar.autoIncrement;
  
  /// Language code (e.g., 'en', 'es')
  String? language;
  
  /// Theme setting ('light', 'dark', 'system')
  String? theme;
  
  /// Date format preference
  String? dateFormat;
  
  /// Time format preference (12h or 24h)
  String? timeFormat;
  
  /// Unit for weight measurements
  String? weightUnit;
  
  /// Unit for temperature measurements
  String? temperatureUnit;
  
  /// Currency symbol for financial values
  String? currencySymbol;
  
  /// Whether notifications are enabled
  bool? notificationsEnabled = true;
  
  /// Whether auto-backup is enabled
  bool? autoBackupEnabled = true;
  
  /// Frequency of automatic backups
  String? backupFrequency;
  
  /// Time of creation
  DateTime? createdAt;
  
  /// Time of last update
  DateTime? updatedAt;

  /// Database version for migration tracking
  int databaseVersion = 0;

  /// Constructor
  AppSettingsIsar();
  
  /// Convert to Map/JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'language': language,
      'theme': theme,
      'dateFormat': dateFormat,
      'timeFormat': timeFormat,
      'weightUnit': weightUnit,
      'temperatureUnit': temperatureUnit,
      'currencySymbol': currencySymbol,
      'notificationsEnabled': notificationsEnabled,
      'autoBackupEnabled': autoBackupEnabled,
      'backupFrequency': backupFrequency,
      'databaseVersion': databaseVersion,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }
  
  /// Create from Map/JSON
  factory AppSettingsIsar.fromJson(Map<String, dynamic> json) {
    return AppSettingsIsar()
      ..language = json['language'] as String?
      ..theme = json['theme'] as String?
      ..dateFormat = json['dateFormat'] as String?
      ..timeFormat = json['timeFormat'] as String?
      ..weightUnit = json['weightUnit'] as String?
      ..temperatureUnit = json['temperatureUnit'] as String?
      ..currencySymbol = json['currencySymbol'] as String?
      ..notificationsEnabled = json['notificationsEnabled'] as bool?
      ..autoBackupEnabled = json['autoBackupEnabled'] as bool?
      ..backupFrequency = json['backupFrequency'] as String?
      ..databaseVersion = json['databaseVersion'] as int? ?? 0
      ..createdAt = json['createdAt'] != null ? DateTime.parse(json['createdAt'] as String) : null
      ..updatedAt = json['updatedAt'] != null ? DateTime.parse(json['updatedAt'] as String) : null;
  }
} 