import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class DashboardMenuItem extends StatelessWidget {
  final String title;
  final String? imagePath;
  final IconData? icon;
  final VoidCallback onTap;
  final Color color;
  final double size;

  const DashboardMenuItem({
    Key? key,
    required this.title,
    this.imagePath,
    this.icon,
    required this.onTap,
    required this.color,
    this.size = 48,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(15),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(15),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (icon != null)
                Icon(
                  icon,
                  size: size,
                  color: color,
                )
              else if (imagePath != null)
                SizedBox(
                  height: size,
                  width: size,
                  child: imagePath!.endsWith('.svg')
                      ? SvgPicture.asset(
                          imagePath!,
                          colorFilter: ColorFilter.mode(color, BlendMode.srcIn),
                        )
                      : Image.asset(imagePath!),
                ),
              const SizedBox(height: 12),
              Text(
                title,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
