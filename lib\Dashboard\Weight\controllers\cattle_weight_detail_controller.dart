
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:uuid/uuid.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../models/weight_record_isar.dart';
import '../models/weight_insights_models.dart';
import '../services/weight_repository.dart';
import '../services/weight_analytics_service.dart';

/// Pure reactive controller for individual cattle weight detail screens
/// Following the cattle module pattern: Stream-driven, no manual loading
class CattleWeightDetailController with ChangeNotifier {
  final CattleIsar cattle;
  final WeightRepository _weightRepository;
  final Uuid _uuid = const Uuid();
  late final StreamSubscription<List<WeightRecordIsar>> _subscription;

  CattleWeightDetailController({required this.cattle})
      : _weightRepository = GetIt.I<WeightRepository>() {
    _initializeStream();
  }

  // Public State for the UI - updated by reactive streams
  List<WeightRecordIsar> records = []; // Filtered records for display
  IndividualCattleAnalytics analytics = IndividualCattleAnalytics.empty;
  bool isLoading = true;
  String? error;

  // Private state for filtering
  List<WeightRecordIsar> _allRecords = []; // Unfiltered source data

  // Filter state
  DateTime? _startDate;
  DateTime? _endDate;
  String? _sortBy;
  bool _sortAscending = true;

  // Getters
  bool get hasData => records.isNotEmpty;
  DateTime? get startDate => _startDate;
  DateTime? get endDate => _endDate;
  String? get sortBy => _sortBy;
  bool get sortAscending => _sortAscending;

  /// Initialize reactive stream listener
  void _initializeStream() {
    _subscription = _weightRepository
        .watchWeightsForCattle(cattle.id)
        .listen((data) {
      _handleDataUpdate(data);
    });
  }

  /// Handle data updates from the reactive stream
  void _handleDataUpdate(List<WeightRecordIsar> data) {
    // Store unfiltered data
    _allRecords = data;

    // Apply filters and sorting
    records = _applyFiltersAndSort(_allRecords);

    // Calculate analytics using the service
    analytics = WeightAnalyticsService.calculateIndividualAnalytics(
      records,
      _startDate,
      _endDate,
    );

    isLoading = false;
    error = null;
    notifyListeners(); // Inform UI of updates
  }

  /// Set date range filter
  void setDateRange(DateTime? startDate, DateTime? endDate) {
    if (_startDate == startDate && _endDate == endDate) return;

    _startDate = startDate;
    _endDate = endDate;
    _reapplyFilters();
  }

  /// Set sort criteria
  void setSortBy(String? sortBy, {bool? ascending}) {
    bool changed = false;

    if (_sortBy != sortBy) {
      _sortBy = sortBy;
      changed = true;
    }

    if (ascending != null && _sortAscending != ascending) {
      _sortAscending = ascending;
      changed = true;
    }

    if (changed) {
      _reapplyFilters();
    }
  }

  /// Clear all filters
  void clearFilters() {
    _startDate = null;
    _endDate = null;
    _sortBy = null;
    _sortAscending = true;
    _reapplyFilters();
  }

  /// Reapply filters to current data
  void _reapplyFilters() {
    // Apply filters to the unfiltered source data
    records = _applyFiltersAndSort(_allRecords);

    // Calculate analytics using the service
    analytics = WeightAnalyticsService.calculateIndividualAnalytics(
      records,
      _startDate,
      _endDate,
    );

    notifyListeners();
  }

  /// Add a new weight record
  Future<void> addWeightRecord(WeightRecordIsar record) async {
    try {
      // 1. Prepare the record using the analytics service
      final previousRecord = _allRecords.isEmpty ? null : _allRecords.first;
      final preparedRecord = WeightAnalyticsService.populateGrowthData(record, previousRecord);

      // 2. Set up the record with required fields
      if (preparedRecord.businessId?.isEmpty ?? true) {
        preparedRecord.businessId = _uuid.v4();
      }
      preparedRecord.cattle.value = cattle; // Link to parent cattle
      preparedRecord.createdAt = DateTime.now();
      preparedRecord.updatedAt = DateTime.now();

      // 3. Pass to the repository
      await _weightRepository.addRecord(preparedRecord);
      // NO refresh() or notifyListeners() needed - stream handles updates!
    } catch (e) {
      error = 'Error adding weight record: $e';
      notifyListeners();
      rethrow;
    }
  }

  /// Update an existing weight record
  Future<void> updateWeightRecord(WeightRecordIsar record) async {
    try {
      record.updatedAt = DateTime.now();
      await _weightRepository.updateRecord(record);
      // NO refresh() needed - stream handles updates!
    } catch (e) {
      error = 'Error updating weight record: $e';
      notifyListeners();
      rethrow;
    }
  }

  /// Delete a weight record
  Future<void> deleteWeightRecord(int recordId) async {
    try {
      await _weightRepository.deleteRecord(recordId);
      // NO refresh() needed - stream handles updates!
    } catch (e) {
      error = 'Error deleting weight record: $e';
      notifyListeners();
      rethrow;
    }
  }

  /// Apply filters and sorting to the data
  List<WeightRecordIsar> _applyFiltersAndSort(List<WeightRecordIsar> sourceRecords) {
    List<WeightRecordIsar> tempRecords = List.from(sourceRecords);

    // Apply date filter
    if (_startDate != null && _endDate != null) {
      final inclusiveEndDate = _endDate!.add(const Duration(days: 1));
      tempRecords = tempRecords.where((record) {
        final date = record.measurementDate;
        if (date == null) return false;
        return !date.isBefore(_startDate!) && date.isBefore(inclusiveEndDate);
      }).toList();
    }

    // Apply sorting
    if (_sortBy != null) {
      tempRecords.sort((a, b) {
        int comparison;
        switch (_sortBy) {
          case 'Date':
            comparison = (a.measurementDate ?? DateTime(0))
                .compareTo(b.measurementDate ?? DateTime(0));
            if (comparison == 0) {
              comparison = (a.createdAt ?? DateTime(0))
                  .compareTo(b.createdAt ?? DateTime(0));
            }
            break;
          case 'Weight':
            comparison = a.weight.compareTo(b.weight);
            break;
          default:
            comparison = 0;
        }
        return _sortAscending ? comparison : -comparison;
      });
    } else {
      // Default sorting: Newest first
      tempRecords.sort((a, b) {
        final dateComparison = (b.measurementDate ?? DateTime(0))
            .compareTo(a.measurementDate ?? DateTime(0));
        if (dateComparison == 0) {
          return (b.createdAt ?? DateTime(0))
              .compareTo(a.createdAt ?? DateTime(0));
        }
        return dateComparison;
      });
    }

    return tempRecords;
  }

  @override
  void dispose() {
    _subscription.cancel();
    super.dispose();
  }
}


