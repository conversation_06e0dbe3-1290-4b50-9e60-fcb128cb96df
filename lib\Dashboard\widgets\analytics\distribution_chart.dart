import 'package:flutter/material.dart';

/// Distribution Chart Widget
/// 
/// A reusable widget for displaying distribution data as a simple bar chart.
/// Provides consistent styling for distribution visualizations.
class DistributionChart extends StatelessWidget {
  final Map<String, int> data;
  final Color? color;
  final double height;
  final bool showValues;
  final bool showPercentages;

  const DistributionChart({
    Key? key,
    required this.data,
    this.color,
    this.height = 200,
    this.showValues = true,
    this.showPercentages = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (data.isEmpty) {
      return SizedBox(
        height: height,
        child: const Center(
          child: Text(
            'No data available',
            style: TextStyle(color: Colors.grey),
          ),
        ),
      );
    }

    final theme = Theme.of(context);
    final primaryColor = color ?? theme.primaryColor;
    final maxValue = data.values.reduce((a, b) => a > b ? a : b);
    final totalValue = data.values.reduce((a, b) => a + b);

    return SizedBox(
      height: height,
      child: Column(
        children: [
          Expanded(
            child: ListView.builder(
              itemCount: data.length,
              itemBuilder: (context, index) {
                final entry = data.entries.elementAt(index);
                final key = entry.key;
                final value = entry.value;
                final percentage = totalValue > 0 ? (value / totalValue * 100) : 0.0;
                final barWidth = maxValue > 0 ? (value / maxValue) : 0.0;

                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: Row(
                    children: [
                      // Label
                      SizedBox(
                        width: 80,
                        child: Text(
                          key,
                          style: theme.textTheme.bodySmall,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      
                      const SizedBox(width: 8),
                      
                      // Bar
                      Expanded(
                        child: Stack(
                          children: [
                            // Background bar
                            Container(
                              height: 20,
                              decoration: BoxDecoration(
                                color: Colors.grey[200],
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                            // Value bar
                            FractionallySizedBox(
                              widthFactor: barWidth,
                              child: Container(
                                height: 20,
                                decoration: BoxDecoration(
                                  color: primaryColor.withValues(alpha: 0.8),
                                  borderRadius: BorderRadius.circular(10),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      const SizedBox(width: 8),
                      
                      // Value/Percentage
                      SizedBox(
                        width: 50,
                        child: Text(
                          showPercentages 
                              ? '${percentage.toStringAsFixed(1)}%'
                              : value.toString(),
                          style: theme.textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.end,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
