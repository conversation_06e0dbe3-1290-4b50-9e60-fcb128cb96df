import 'package:flutter/material.dart';
import '../models/milk_record_isar.dart';
import '../../Cattle/models/cattle_isar.dart';

import 'package:fl_chart/fl_chart.dart';
import '../../../constants/app_tabs.dart';


class MilkDetailsAnalyticsTab extends StatefulWidget {
  final MilkRecordIsar milkRecord;
  final CattleIsar? cattle;
  final List<MilkRecordIsar> relatedRecords;

  const MilkDetailsAnalyticsTab({
    Key? key,
    required this.milkRecord,
    this.cattle,
    required this.relatedRecords,
  }) : super(key: key);

  @override
  State<MilkDetailsAnalyticsTab> createState() => _MilkDetailsAnalyticsTabState();
}

class _MilkDetailsAnalyticsTabState extends State<MilkDetailsAnalyticsTab>
    with AutomaticKeepAliveClientMixin {

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    if (widget.relatedRecords.isEmpty) {
      return UniversalEmptyState.milk(
        title: 'No Analytics Data',
        message: 'Add more milk production records for ${widget.cattle?.name ?? 'this cattle'} to see detailed analytics.',
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'Milk Production Analytics for ${widget.cattle?.name ?? 'Cattle'}',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),

          // Summary Cards
          _buildSummaryCards(),
          const SizedBox(height: 24),

          // Production Trend Chart
          _buildProductionTrendChart(),
          const SizedBox(height: 24),

          // Session Distribution
          _buildSessionDistribution(),
          const SizedBox(height: 24),

          // Production Insights
          _buildProductionInsights(),
        ],
      ),
    );
  }

  Widget _buildSummaryCards() {
    final totalProduction = widget.relatedRecords.fold(0.0, (sum, record) => sum + (record.quantity ?? 0.0));
    final averageProduction = widget.relatedRecords.isNotEmpty ? totalProduction / widget.relatedRecords.length : 0.0;
    final highestProduction = widget.relatedRecords.isNotEmpty 
        ? widget.relatedRecords.map((r) => r.quantity ?? 0.0).reduce((a, b) => a > b ? a : b)
        : 0.0;
    final totalRecords = widget.relatedRecords.length;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Production Summary',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildSummaryCard(
                'Total Production',
                '${totalProduction.toStringAsFixed(1)}L',
                Icons.water_drop,
                UniversalEmptyStateTheme.milk,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildSummaryCard(
                'Average/Record',
                '${averageProduction.toStringAsFixed(1)}L',
                Icons.analytics,
                Colors.blue,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildSummaryCard(
                'Highest Production',
                '${highestProduction.toStringAsFixed(1)}L',
                Icons.trending_up,
                Colors.green,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildSummaryCard(
                'Total Records',
                totalRecords.toString(),
                Icons.list_alt,
                Colors.orange,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductionTrendChart() {
    final sortedRecords = List<MilkRecordIsar>.from(widget.relatedRecords)
      ..sort((a, b) => (a.date ?? DateTime.now()).compareTo(b.date ?? DateTime.now()));

    if (sortedRecords.length < 2) {
      return Card(
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                'Production Trend',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              const Text('Need at least 2 records to show production trend'),
            ],
          ),
        ),
      );
    }

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Production Trend',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: LineChart(
                LineChartData(
                  gridData: const FlGridData(show: true),
                  titlesData: const FlTitlesData(
                    leftTitles: AxisTitles(sideTitles: SideTitles(showTitles: true)),
                    bottomTitles: AxisTitles(sideTitles: SideTitles(showTitles: true)),
                    rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                    topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  ),
                  borderData: FlBorderData(show: true),
                  lineBarsData: [
                    LineChartBarData(
                      spots: _buildProductionSpots(sortedRecords),
                      isCurved: true,
                      color: UniversalEmptyStateTheme.milk,
                      barWidth: 3,
                      dotData: const FlDotData(show: true),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSessionDistribution() {
    final sessionDistribution = <String, double>{};
    final sessionCounts = <String, int>{};
    
    for (final record in widget.relatedRecords) {
      final session = record.session ?? 'Unknown';
      sessionDistribution[session] = (sessionDistribution[session] ?? 0.0) + (record.quantity ?? 0.0);
      sessionCounts[session] = (sessionCounts[session] ?? 0) + 1;
    }

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Production by Session',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            if (sessionDistribution.isNotEmpty)
              SizedBox(
                height: 200,
                child: PieChart(
                  PieChartData(
                    sections: _buildSessionPieChartSections(sessionDistribution),
                    centerSpaceRadius: 40,
                    sectionsSpace: 2,
                  ),
                ),
              ),
            const SizedBox(height: 16),
            _buildSessionLegend(sessionDistribution, sessionCounts),
          ],
        ),
      ),
    );
  }

  Widget _buildProductionInsights() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Production Insights',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ..._generateInsights(),
          ],
        ),
      ),
    );
  }

  List<Widget> _generateInsights() {
    final insights = <Widget>[];
    
    // Production consistency
    final quantities = widget.relatedRecords.map((r) => r.quantity ?? 0.0).toList();
    if (quantities.isNotEmpty) {
      final average = quantities.reduce((a, b) => a + b) / quantities.length;
      final variance = quantities.map((q) => (q - average) * (q - average)).reduce((a, b) => a + b) / quantities.length;
      final consistency = variance < (average * 0.2); // Low variance indicates consistency

      insights.add(_buildInsightItem(
        'Production Consistency',
        consistency ? 'Consistent milk production levels' : 'Variable milk production levels',
        Icons.timeline,
        consistency ? Colors.green : Colors.orange,
      ));
    }

    // Recent performance
    final recentRecords = widget.relatedRecords.where((r) {
      if (r.date == null) return false;
      final sevenDaysAgo = DateTime.now().subtract(const Duration(days: 7));
      return r.date!.isAfter(sevenDaysAgo);
    }).toList();

    if (recentRecords.isNotEmpty) {
      final recentAverage = recentRecords.fold(0.0, (sum, r) => sum + (r.quantity ?? 0.0)) / recentRecords.length;
      final overallAverage = widget.relatedRecords.fold(0.0, (sum, r) => sum + (r.quantity ?? 0.0)) / widget.relatedRecords.length;
      final improving = recentAverage > overallAverage;

      insights.add(_buildInsightItem(
        'Recent Performance',
        improving ? 'Production improving in recent days' : 'Production stable or declining recently',
        improving ? Icons.trending_up : Icons.trending_down,
        improving ? Colors.green : Colors.orange,
      ));
    }

    // Session analysis
    final morningRecords = widget.relatedRecords.where((r) => r.session?.toLowerCase() == 'morning').toList();
    final eveningRecords = widget.relatedRecords.where((r) => r.session?.toLowerCase() == 'evening').toList();

    if (morningRecords.isNotEmpty && eveningRecords.isNotEmpty) {
      final morningAvg = morningRecords.fold(0.0, (sum, r) => sum + (r.quantity ?? 0.0)) / morningRecords.length;
      final eveningAvg = eveningRecords.fold(0.0, (sum, r) => sum + (r.quantity ?? 0.0)) / eveningRecords.length;
      final betterSession = morningAvg > eveningAvg ? 'Morning' : 'Evening';

      insights.add(_buildInsightItem(
        'Best Session',
        '$betterSession sessions produce more milk on average',
        Icons.schedule,
        Colors.blue,
      ));
    }

    return insights;
  }

  Widget _buildInsightItem(String title, String description, IconData icon, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<FlSpot> _buildProductionSpots(List<MilkRecordIsar> sortedRecords) {
    return sortedRecords.asMap().entries.map((entry) {
      return FlSpot(entry.key.toDouble(), entry.value.quantity ?? 0.0);
    }).toList();
  }

  List<PieChartSectionData> _buildSessionPieChartSections(Map<String, double> data) {
    final colors = [Colors.blue, Colors.green, Colors.orange, Colors.red];
    final total = data.values.fold(0.0, (sum, value) => sum + value);
    
    return data.entries.toList().asMap().entries.map((entry) {
      final index = entry.key;
      final mapEntry = entry.value;
      final percentage = total > 0 ? (mapEntry.value / total) * 100 : 0.0;
      
      return PieChartSectionData(
        color: colors[index % colors.length],
        value: mapEntry.value,
        title: '${percentage.toStringAsFixed(1)}%',
        radius: 60,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );
    }).toList();
  }

  Widget _buildSessionLegend(Map<String, double> distribution, Map<String, int> counts) {
    final colors = [Colors.blue, Colors.green, Colors.orange, Colors.red];
    
    return Column(
      children: distribution.entries.toList().asMap().entries.map((entry) {
        final index = entry.key;
        final mapEntry = entry.value;
        final count = counts[mapEntry.key] ?? 0;
        
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: Row(
            children: [
              Container(
                width: 16,
                height: 16,
                decoration: BoxDecoration(
                  color: colors[index % colors.length],
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text('${mapEntry.key} ($count records)'),
              ),
              Text(
                '${mapEntry.value.toStringAsFixed(1)}L',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }
}
