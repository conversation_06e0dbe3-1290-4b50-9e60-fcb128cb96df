import '../models/cattle_isar.dart';
import '../../Farm Setup/models/breed_category_isar.dart';
import '../../Farm Setup/models/animal_type_isar.dart';
import '../utils/cattle_age_calculator.dart';


enum AgeGroup { young, adult, mature, senior }

/// Data class to hold all analytics results
class CattleAnalyticsResult {
  // KPI Metrics
  final int totalCattle;
  final int activeCattle;
  final int soldCattle;
  final int maleCount;
  final int femaleCount;
  final double averageAge;
  final double totalValue;

  // Distributions
  final Map<String, int> genderDistribution;
  final Map<String, int> statusDistribution;
  final Map<String, int> ageDistribution;
  final Map<String, int> breedDistribution;

  // Financial metrics
  final double totalPurchaseValue;
  final double averagePurchasePrice;
  final double estimatedCurrentValue;

  const CattleAnalyticsResult({
    required this.totalCattle,
    required this.activeCattle,
    required this.soldCattle,
    required this.maleCount,
    required this.femaleCount,
    required this.averageAge,
    required this.totalValue,
    required this.genderDistribution,
    required this.statusDistribution,
    required this.ageDistribution,
    required this.breedDistribution,
    required this.totalPurchaseValue,
    required this.averagePurchasePrice,
    required this.estimatedCurrentValue,
  });

  /// Empty result for when there's no data
  static const empty = CattleAnalyticsResult(
    totalCattle: 0,
    activeCattle: 0,
    soldCattle: 0,
    maleCount: 0,
    femaleCount: 0,
    averageAge: 0.0,
    totalValue: 0.0,
    genderDistribution: {},
    statusDistribution: {},
    ageDistribution: {},
    breedDistribution: {},
    totalPurchaseValue: 0.0,
    averagePurchasePrice: 0.0,
    estimatedCurrentValue: 0.0,
  );
}



/// Pure analytics service - no state, just calculations
class CattleAnalyticsService {

  /// Main calculation method - single entry point with O(n) efficiency
  static CattleAnalyticsResult calculate(
    List<CattleIsar> cattle,
    Map<String, BreedCategoryIsar> breedMap,
    Map<String, AnimalTypeIsar> animalTypeMap,
  ) {
    if (cattle.isEmpty) {
      return CattleAnalyticsResult.empty;
    }

    // Single pass through the data for maximum efficiency - O(n) instead of O(3n)
    final accumulator = _AnalyticsAccumulator();

    for (final animal in cattle) {
      accumulator.process(animal, breedMap);
    }

    return accumulator.toResult();
  }





}

/// Efficient single-pass accumulator for all analytics calculations
/// Self-contained component with all necessary constants and helper methods
class _AnalyticsAccumulator {
  // Constants for calculations
  static const double _valueAppreciationPerMonth = 0.01; // 1% per month



  // Basic counts - including direct cattle count
  int totalCattle = 0;
  int activeCattle = 0;
  int soldCattle = 0;
  int maleCount = 0;
  int femaleCount = 0;

  // Age metrics - fixed to only count cattle with valid ages
  double totalAgeMonths = 0.0;
  int cattleWithValidAge = 0;

  // Financial metrics
  double totalPurchaseValue = 0.0;
  double totalCurrentValue = 0.0;
  int cattleWithPurchasePrice = 0;

  // Distribution maps
  final Map<String, int> genderDistribution = {};
  final Map<String, int> statusDistribution = {};
  final Map<String, int> ageDistribution = {};
  final Map<String, int> breedDistribution = {};

  /// Process a single cattle record - updates all metrics in one pass
  /// Eliminates redundant calculations by computing age once and reusing
  void process(CattleIsar animal, Map<String, BreedCategoryIsar> breedMap) {
    // Direct cattle count
    totalCattle++;

    // Calculate age once and reuse - eliminates redundant calculations
    // Delegate to the specialist utility for consistency across the app
    final ageInMonths = CattleAgeCalculator.calculateAgeInMonths(animal).toDouble();

    // Status processing using enum field directly
    final status = animal.status;
    if (status == CattleStatus.active) {
      activeCattle++;
    } else if (status == CattleStatus.sold) {
      soldCattle++;
    }
    final statusKey = status.name;
    statusDistribution[statusKey] = (statusDistribution[statusKey] ?? 0) + 1;

    // Gender processing using enum field directly
    final gender = animal.gender;
    switch (gender) {
      case CattleGender.male:
        maleCount++;
        break;
      case CattleGender.female:
        femaleCount++;
        break;
      case CattleGender.unknown:
        break;
    }
    final genderKey = gender.name;
    genderDistribution[genderKey] = (genderDistribution[genderKey] ?? 0) + 1;

    // Age processing - fixed to only count valid ages
    if (ageInMonths > 0) {
      totalAgeMonths += ageInMonths;
      cattleWithValidAge++;
    }
    final ageGroup = CattleAgeCalculator.getAgeCategory(animal);
    ageDistribution[ageGroup] = (ageDistribution[ageGroup] ?? 0) + 1;

    // Breed processing
    final breedName = breedMap[animal.breedId]?.name ?? 'Unknown';
    breedDistribution[breedName] = (breedDistribution[breedName] ?? 0) + 1;

    // Financial processing - reuse age calculation
    final purchasePrice = animal.purchasePrice ?? 0.0;
    if (purchasePrice > 0) {
      totalPurchaseValue += purchasePrice;
      cattleWithPurchasePrice++;
    }
    final estimatedValue = _estimateCurrentValue(animal, ageInMonths);
    totalCurrentValue += estimatedValue;
  }

  /// Convert accumulated data to immutable result
  CattleAnalyticsResult toResult() {
    final averageAge = cattleWithValidAge > 0
        ? totalAgeMonths / cattleWithValidAge
        : 0.0;

    final averagePurchasePrice = cattleWithPurchasePrice > 0
        ? totalPurchaseValue / cattleWithPurchasePrice
        : 0.0;

    return CattleAnalyticsResult(
      totalCattle: totalCattle, // Direct count instead of calculating from distribution
      activeCattle: activeCattle,
      soldCattle: soldCattle,
      maleCount: maleCount,
      femaleCount: femaleCount,
      averageAge: averageAge,
      totalValue: totalCurrentValue,
      genderDistribution: Map.unmodifiable(genderDistribution),
      statusDistribution: Map.unmodifiable(statusDistribution),
      ageDistribution: Map.unmodifiable(ageDistribution),
      breedDistribution: Map.unmodifiable(breedDistribution),
      totalPurchaseValue: totalPurchaseValue,
      averagePurchasePrice: averagePurchasePrice,
      estimatedCurrentValue: totalCurrentValue,
    );
  }

  /// Self-contained helper methods - no external dependencies

  /// Estimate current value with pre-calculated age to avoid redundant calculations
  double _estimateCurrentValue(CattleIsar cattle, double ageInMonths) {
    final purchasePrice = cattle.purchasePrice ?? 0.0;
    if (purchasePrice <= 0) return 0.0;

    // Simple appreciation model using named constant
    final appreciationFactor = 1.0 + (ageInMonths * _valueAppreciationPerMonth);

    return purchasePrice * appreciationFactor;
  }
}
