// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'weight_report_data_isar.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetWeightReportDataIsarCollection on Isar {
  IsarCollection<WeightReportDataIsar> get weightReportDataIsars =>
      this.collection();
}

const WeightReportDataIsarSchema = CollectionSchema(
  name: r'WeightReportDataIsar',
  id: 8304390126596392945,
  properties: {
    r'averageDailyGain': PropertySchema(
      id: 0,
      name: r'averageDailyGain',
      type: IsarType.double,
    ),
    r'averageWeight': PropertySchema(
      id: 1,
      name: r'averageWeight',
      type: IsarType.double,
    ),
    r'averageWeightGain': PropertySchema(
      id: 2,
      name: r'averageWeightGain',
      type: IsarType.double,
    ),
    r'businessId': PropertySchema(
      id: 3,
      name: r'businessId',
      type: IsarType.string,
    ),
    r'createdAt': PropertySchema(
      id: 4,
      name: r'createdAt',
      type: IsarType.dateTime,
    ),
    r'endDate': PropertySchema(
      id: 5,
      name: r'endDate',
      type: IsarType.dateTime,
    ),
    r'filterCriteria': PropertySchema(
      id: 6,
      name: r'filterCriteria',
      type: IsarType.string,
    ),
    r'generatedAt': PropertySchema(
      id: 7,
      name: r'generatedAt',
      type: IsarType.dateTime,
    ),
    r'maximumWeight': PropertySchema(
      id: 8,
      name: r'maximumWeight',
      type: IsarType.double,
    ),
    r'minimumWeight': PropertySchema(
      id: 9,
      name: r'minimumWeight',
      type: IsarType.double,
    ),
    r'reportType': PropertySchema(
      id: 10,
      name: r'reportType',
      type: IsarType.string,
    ),
    r'startDate': PropertySchema(
      id: 11,
      name: r'startDate',
      type: IsarType.dateTime,
    ),
    r'title': PropertySchema(
      id: 12,
      name: r'title',
      type: IsarType.string,
    ),
    r'totalWeighings': PropertySchema(
      id: 13,
      name: r'totalWeighings',
      type: IsarType.long,
    ),
    r'totalWeightGain': PropertySchema(
      id: 14,
      name: r'totalWeightGain',
      type: IsarType.double,
    ),
    r'updatedAt': PropertySchema(
      id: 15,
      name: r'updatedAt',
      type: IsarType.dateTime,
    ),
    r'weightDates': PropertySchema(
      id: 16,
      name: r'weightDates',
      type: IsarType.dateTimeList,
    ),
    r'weightRangeColors': PropertySchema(
      id: 17,
      name: r'weightRangeColors',
      type: IsarType.longList,
    ),
    r'weightRangeCounts': PropertySchema(
      id: 18,
      name: r'weightRangeCounts',
      type: IsarType.longList,
    ),
    r'weightRanges': PropertySchema(
      id: 19,
      name: r'weightRanges',
      type: IsarType.stringList,
    ),
    r'weightValues': PropertySchema(
      id: 20,
      name: r'weightValues',
      type: IsarType.doubleList,
    )
  },
  estimateSize: _weightReportDataIsarEstimateSize,
  serialize: _weightReportDataIsarSerialize,
  deserialize: _weightReportDataIsarDeserialize,
  deserializeProp: _weightReportDataIsarDeserializeProp,
  idName: r'id',
  indexes: {},
  links: {},
  embeddedSchemas: {},
  getId: _weightReportDataIsarGetId,
  getLinks: _weightReportDataIsarGetLinks,
  attach: _weightReportDataIsarAttach,
  version: '3.1.0+1',
);

int _weightReportDataIsarEstimateSize(
  WeightReportDataIsar object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.businessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.filterCriteria;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.reportType;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.title;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.weightDates;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  {
    final value = object.weightRangeColors;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  {
    final value = object.weightRangeCounts;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  {
    final list = object.weightRanges;
    if (list != null) {
      bytesCount += 3 + list.length * 3;
      {
        for (var i = 0; i < list.length; i++) {
          final value = list[i];
          bytesCount += value.length * 3;
        }
      }
    }
  }
  {
    final value = object.weightValues;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  return bytesCount;
}

void _weightReportDataIsarSerialize(
  WeightReportDataIsar object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeDouble(offsets[0], object.averageDailyGain);
  writer.writeDouble(offsets[1], object.averageWeight);
  writer.writeDouble(offsets[2], object.averageWeightGain);
  writer.writeString(offsets[3], object.businessId);
  writer.writeDateTime(offsets[4], object.createdAt);
  writer.writeDateTime(offsets[5], object.endDate);
  writer.writeString(offsets[6], object.filterCriteria);
  writer.writeDateTime(offsets[7], object.generatedAt);
  writer.writeDouble(offsets[8], object.maximumWeight);
  writer.writeDouble(offsets[9], object.minimumWeight);
  writer.writeString(offsets[10], object.reportType);
  writer.writeDateTime(offsets[11], object.startDate);
  writer.writeString(offsets[12], object.title);
  writer.writeLong(offsets[13], object.totalWeighings);
  writer.writeDouble(offsets[14], object.totalWeightGain);
  writer.writeDateTime(offsets[15], object.updatedAt);
  writer.writeDateTimeList(offsets[16], object.weightDates);
  writer.writeLongList(offsets[17], object.weightRangeColors);
  writer.writeLongList(offsets[18], object.weightRangeCounts);
  writer.writeStringList(offsets[19], object.weightRanges);
  writer.writeDoubleList(offsets[20], object.weightValues);
}

WeightReportDataIsar _weightReportDataIsarDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = WeightReportDataIsar();
  object.averageDailyGain = reader.readDoubleOrNull(offsets[0]);
  object.averageWeight = reader.readDoubleOrNull(offsets[1]);
  object.averageWeightGain = reader.readDoubleOrNull(offsets[2]);
  object.businessId = reader.readStringOrNull(offsets[3]);
  object.createdAt = reader.readDateTimeOrNull(offsets[4]);
  object.endDate = reader.readDateTimeOrNull(offsets[5]);
  object.filterCriteria = reader.readStringOrNull(offsets[6]);
  object.generatedAt = reader.readDateTimeOrNull(offsets[7]);
  object.id = id;
  object.maximumWeight = reader.readDoubleOrNull(offsets[8]);
  object.minimumWeight = reader.readDoubleOrNull(offsets[9]);
  object.reportType = reader.readStringOrNull(offsets[10]);
  object.startDate = reader.readDateTimeOrNull(offsets[11]);
  object.title = reader.readStringOrNull(offsets[12]);
  object.totalWeighings = reader.readLongOrNull(offsets[13]);
  object.totalWeightGain = reader.readDoubleOrNull(offsets[14]);
  object.updatedAt = reader.readDateTimeOrNull(offsets[15]);
  object.weightDates = reader.readDateTimeList(offsets[16]);
  object.weightRangeColors = reader.readLongList(offsets[17]);
  object.weightRangeCounts = reader.readLongList(offsets[18]);
  object.weightRanges = reader.readStringList(offsets[19]);
  object.weightValues = reader.readDoubleList(offsets[20]);
  return object;
}

P _weightReportDataIsarDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readDoubleOrNull(offset)) as P;
    case 1:
      return (reader.readDoubleOrNull(offset)) as P;
    case 2:
      return (reader.readDoubleOrNull(offset)) as P;
    case 3:
      return (reader.readStringOrNull(offset)) as P;
    case 4:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 5:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 6:
      return (reader.readStringOrNull(offset)) as P;
    case 7:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 8:
      return (reader.readDoubleOrNull(offset)) as P;
    case 9:
      return (reader.readDoubleOrNull(offset)) as P;
    case 10:
      return (reader.readStringOrNull(offset)) as P;
    case 11:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 12:
      return (reader.readStringOrNull(offset)) as P;
    case 13:
      return (reader.readLongOrNull(offset)) as P;
    case 14:
      return (reader.readDoubleOrNull(offset)) as P;
    case 15:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 16:
      return (reader.readDateTimeList(offset)) as P;
    case 17:
      return (reader.readLongList(offset)) as P;
    case 18:
      return (reader.readLongList(offset)) as P;
    case 19:
      return (reader.readStringList(offset)) as P;
    case 20:
      return (reader.readDoubleList(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _weightReportDataIsarGetId(WeightReportDataIsar object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _weightReportDataIsarGetLinks(
    WeightReportDataIsar object) {
  return [];
}

void _weightReportDataIsarAttach(
    IsarCollection<dynamic> col, Id id, WeightReportDataIsar object) {
  object.id = id;
}

extension WeightReportDataIsarQueryWhereSort
    on QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QWhere> {
  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterWhere>
      anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension WeightReportDataIsarQueryWhere
    on QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QWhereClause> {
  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterWhereClause>
      idEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterWhereClause>
      idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterWhereClause>
      idGreaterThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterWhereClause>
      idLessThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterWhereClause>
      idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension WeightReportDataIsarQueryFilter on QueryBuilder<WeightReportDataIsar,
    WeightReportDataIsar, QFilterCondition> {
  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> averageDailyGainIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'averageDailyGain',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> averageDailyGainIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'averageDailyGain',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> averageDailyGainEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'averageDailyGain',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> averageDailyGainGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'averageDailyGain',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> averageDailyGainLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'averageDailyGain',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> averageDailyGainBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'averageDailyGain',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> averageWeightIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'averageWeight',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> averageWeightIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'averageWeight',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> averageWeightEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'averageWeight',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> averageWeightGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'averageWeight',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> averageWeightLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'averageWeight',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> averageWeightBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'averageWeight',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> averageWeightGainIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'averageWeightGain',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> averageWeightGainIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'averageWeightGain',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> averageWeightGainEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'averageWeightGain',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> averageWeightGainGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'averageWeightGain',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> averageWeightGainLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'averageWeightGain',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> averageWeightGainBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'averageWeightGain',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> businessIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> businessIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> businessIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> businessIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'businessId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> businessIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> businessIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
          QAfterFilterCondition>
      businessIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
          QAfterFilterCondition>
      businessIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'businessId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> businessIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> businessIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> createdAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> createdAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> createdAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> createdAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> createdAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> createdAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'createdAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> endDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'endDate',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> endDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'endDate',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> endDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'endDate',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> endDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'endDate',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> endDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'endDate',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> endDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'endDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> filterCriteriaIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'filterCriteria',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> filterCriteriaIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'filterCriteria',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> filterCriteriaEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> filterCriteriaGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> filterCriteriaLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> filterCriteriaBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'filterCriteria',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> filterCriteriaStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> filterCriteriaEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
          QAfterFilterCondition>
      filterCriteriaContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
          QAfterFilterCondition>
      filterCriteriaMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'filterCriteria',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> filterCriteriaIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'filterCriteria',
        value: '',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> filterCriteriaIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'filterCriteria',
        value: '',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> generatedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'generatedAt',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> generatedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'generatedAt',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> generatedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'generatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> generatedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'generatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> generatedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'generatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> generatedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'generatedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> idEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> maximumWeightIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'maximumWeight',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> maximumWeightIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'maximumWeight',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> maximumWeightEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'maximumWeight',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> maximumWeightGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'maximumWeight',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> maximumWeightLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'maximumWeight',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> maximumWeightBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'maximumWeight',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> minimumWeightIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'minimumWeight',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> minimumWeightIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'minimumWeight',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> minimumWeightEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'minimumWeight',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> minimumWeightGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'minimumWeight',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> minimumWeightLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'minimumWeight',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> minimumWeightBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'minimumWeight',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> reportTypeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'reportType',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> reportTypeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'reportType',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> reportTypeEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> reportTypeGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> reportTypeLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> reportTypeBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'reportType',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> reportTypeStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> reportTypeEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
          QAfterFilterCondition>
      reportTypeContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
          QAfterFilterCondition>
      reportTypeMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'reportType',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> reportTypeIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'reportType',
        value: '',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> reportTypeIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'reportType',
        value: '',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> startDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'startDate',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> startDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'startDate',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> startDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'startDate',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> startDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'startDate',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> startDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'startDate',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> startDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'startDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> titleIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'title',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> titleIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'title',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> titleEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> titleGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> titleLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> titleBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'title',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> titleStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> titleEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
          QAfterFilterCondition>
      titleContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
          QAfterFilterCondition>
      titleMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'title',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> titleIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'title',
        value: '',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> titleIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'title',
        value: '',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> totalWeighingsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'totalWeighings',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> totalWeighingsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'totalWeighings',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> totalWeighingsEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'totalWeighings',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> totalWeighingsGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'totalWeighings',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> totalWeighingsLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'totalWeighings',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> totalWeighingsBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'totalWeighings',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> totalWeightGainIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'totalWeightGain',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> totalWeightGainIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'totalWeightGain',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> totalWeightGainEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'totalWeightGain',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> totalWeightGainGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'totalWeightGain',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> totalWeightGainLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'totalWeightGain',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> totalWeightGainBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'totalWeightGain',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> updatedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> updatedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> updatedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> updatedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> updatedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> updatedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'updatedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightDatesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'weightDates',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightDatesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'weightDates',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightDatesElementEqualTo(DateTime value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'weightDates',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightDatesElementGreaterThan(
    DateTime value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'weightDates',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightDatesElementLessThan(
    DateTime value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'weightDates',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightDatesElementBetween(
    DateTime lower,
    DateTime upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'weightDates',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightDatesLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'weightDates',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightDatesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'weightDates',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightDatesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'weightDates',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightDatesLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'weightDates',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightDatesLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'weightDates',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightDatesLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'weightDates',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightRangeColorsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'weightRangeColors',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightRangeColorsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'weightRangeColors',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightRangeColorsElementEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'weightRangeColors',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightRangeColorsElementGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'weightRangeColors',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightRangeColorsElementLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'weightRangeColors',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightRangeColorsElementBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'weightRangeColors',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightRangeColorsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'weightRangeColors',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightRangeColorsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'weightRangeColors',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightRangeColorsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'weightRangeColors',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightRangeColorsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'weightRangeColors',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightRangeColorsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'weightRangeColors',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightRangeColorsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'weightRangeColors',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightRangeCountsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'weightRangeCounts',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightRangeCountsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'weightRangeCounts',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightRangeCountsElementEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'weightRangeCounts',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightRangeCountsElementGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'weightRangeCounts',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightRangeCountsElementLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'weightRangeCounts',
        value: value,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightRangeCountsElementBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'weightRangeCounts',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightRangeCountsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'weightRangeCounts',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightRangeCountsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'weightRangeCounts',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightRangeCountsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'weightRangeCounts',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightRangeCountsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'weightRangeCounts',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightRangeCountsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'weightRangeCounts',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightRangeCountsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'weightRangeCounts',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightRangesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'weightRanges',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightRangesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'weightRanges',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightRangesElementEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'weightRanges',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightRangesElementGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'weightRanges',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightRangesElementLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'weightRanges',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightRangesElementBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'weightRanges',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightRangesElementStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'weightRanges',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightRangesElementEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'weightRanges',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
          QAfterFilterCondition>
      weightRangesElementContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'weightRanges',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
          QAfterFilterCondition>
      weightRangesElementMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'weightRanges',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightRangesElementIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'weightRanges',
        value: '',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightRangesElementIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'weightRanges',
        value: '',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightRangesLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'weightRanges',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightRangesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'weightRanges',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightRangesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'weightRanges',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightRangesLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'weightRanges',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightRangesLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'weightRanges',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightRangesLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'weightRanges',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightValuesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'weightValues',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightValuesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'weightValues',
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightValuesElementEqualTo(
    double value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'weightValues',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightValuesElementGreaterThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'weightValues',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightValuesElementLessThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'weightValues',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightValuesElementBetween(
    double lower,
    double upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'weightValues',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightValuesLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'weightValues',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightValuesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'weightValues',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightValuesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'weightValues',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightValuesLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'weightValues',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightValuesLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'weightValues',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar,
      QAfterFilterCondition> weightValuesLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'weightValues',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }
}

extension WeightReportDataIsarQueryObject on QueryBuilder<WeightReportDataIsar,
    WeightReportDataIsar, QFilterCondition> {}

extension WeightReportDataIsarQueryLinks on QueryBuilder<WeightReportDataIsar,
    WeightReportDataIsar, QFilterCondition> {}

extension WeightReportDataIsarQuerySortBy
    on QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QSortBy> {
  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      sortByAverageDailyGain() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'averageDailyGain', Sort.asc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      sortByAverageDailyGainDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'averageDailyGain', Sort.desc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      sortByAverageWeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'averageWeight', Sort.asc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      sortByAverageWeightDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'averageWeight', Sort.desc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      sortByAverageWeightGain() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'averageWeightGain', Sort.asc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      sortByAverageWeightGainDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'averageWeightGain', Sort.desc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      sortByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      sortByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      sortByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      sortByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      sortByEndDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'endDate', Sort.asc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      sortByEndDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'endDate', Sort.desc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      sortByFilterCriteria() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'filterCriteria', Sort.asc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      sortByFilterCriteriaDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'filterCriteria', Sort.desc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      sortByGeneratedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'generatedAt', Sort.asc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      sortByGeneratedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'generatedAt', Sort.desc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      sortByMaximumWeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'maximumWeight', Sort.asc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      sortByMaximumWeightDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'maximumWeight', Sort.desc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      sortByMinimumWeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'minimumWeight', Sort.asc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      sortByMinimumWeightDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'minimumWeight', Sort.desc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      sortByReportType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reportType', Sort.asc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      sortByReportTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reportType', Sort.desc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      sortByStartDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startDate', Sort.asc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      sortByStartDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startDate', Sort.desc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      sortByTitle() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'title', Sort.asc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      sortByTitleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'title', Sort.desc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      sortByTotalWeighings() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalWeighings', Sort.asc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      sortByTotalWeighingsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalWeighings', Sort.desc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      sortByTotalWeightGain() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalWeightGain', Sort.asc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      sortByTotalWeightGainDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalWeightGain', Sort.desc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      sortByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      sortByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }
}

extension WeightReportDataIsarQuerySortThenBy
    on QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QSortThenBy> {
  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      thenByAverageDailyGain() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'averageDailyGain', Sort.asc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      thenByAverageDailyGainDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'averageDailyGain', Sort.desc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      thenByAverageWeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'averageWeight', Sort.asc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      thenByAverageWeightDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'averageWeight', Sort.desc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      thenByAverageWeightGain() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'averageWeightGain', Sort.asc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      thenByAverageWeightGainDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'averageWeightGain', Sort.desc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      thenByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      thenByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      thenByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      thenByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      thenByEndDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'endDate', Sort.asc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      thenByEndDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'endDate', Sort.desc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      thenByFilterCriteria() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'filterCriteria', Sort.asc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      thenByFilterCriteriaDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'filterCriteria', Sort.desc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      thenByGeneratedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'generatedAt', Sort.asc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      thenByGeneratedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'generatedAt', Sort.desc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      thenByMaximumWeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'maximumWeight', Sort.asc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      thenByMaximumWeightDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'maximumWeight', Sort.desc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      thenByMinimumWeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'minimumWeight', Sort.asc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      thenByMinimumWeightDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'minimumWeight', Sort.desc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      thenByReportType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reportType', Sort.asc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      thenByReportTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reportType', Sort.desc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      thenByStartDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startDate', Sort.asc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      thenByStartDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startDate', Sort.desc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      thenByTitle() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'title', Sort.asc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      thenByTitleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'title', Sort.desc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      thenByTotalWeighings() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalWeighings', Sort.asc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      thenByTotalWeighingsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalWeighings', Sort.desc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      thenByTotalWeightGain() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalWeightGain', Sort.asc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      thenByTotalWeightGainDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalWeightGain', Sort.desc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      thenByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QAfterSortBy>
      thenByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }
}

extension WeightReportDataIsarQueryWhereDistinct
    on QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QDistinct> {
  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QDistinct>
      distinctByAverageDailyGain() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'averageDailyGain');
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QDistinct>
      distinctByAverageWeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'averageWeight');
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QDistinct>
      distinctByAverageWeightGain() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'averageWeightGain');
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QDistinct>
      distinctByBusinessId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'businessId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QDistinct>
      distinctByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'createdAt');
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QDistinct>
      distinctByEndDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'endDate');
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QDistinct>
      distinctByFilterCriteria({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'filterCriteria',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QDistinct>
      distinctByGeneratedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'generatedAt');
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QDistinct>
      distinctByMaximumWeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'maximumWeight');
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QDistinct>
      distinctByMinimumWeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'minimumWeight');
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QDistinct>
      distinctByReportType({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'reportType', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QDistinct>
      distinctByStartDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'startDate');
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QDistinct>
      distinctByTitle({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'title', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QDistinct>
      distinctByTotalWeighings() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'totalWeighings');
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QDistinct>
      distinctByTotalWeightGain() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'totalWeightGain');
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QDistinct>
      distinctByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'updatedAt');
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QDistinct>
      distinctByWeightDates() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'weightDates');
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QDistinct>
      distinctByWeightRangeColors() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'weightRangeColors');
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QDistinct>
      distinctByWeightRangeCounts() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'weightRangeCounts');
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QDistinct>
      distinctByWeightRanges() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'weightRanges');
    });
  }

  QueryBuilder<WeightReportDataIsar, WeightReportDataIsar, QDistinct>
      distinctByWeightValues() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'weightValues');
    });
  }
}

extension WeightReportDataIsarQueryProperty on QueryBuilder<
    WeightReportDataIsar, WeightReportDataIsar, QQueryProperty> {
  QueryBuilder<WeightReportDataIsar, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<WeightReportDataIsar, double?, QQueryOperations>
      averageDailyGainProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'averageDailyGain');
    });
  }

  QueryBuilder<WeightReportDataIsar, double?, QQueryOperations>
      averageWeightProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'averageWeight');
    });
  }

  QueryBuilder<WeightReportDataIsar, double?, QQueryOperations>
      averageWeightGainProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'averageWeightGain');
    });
  }

  QueryBuilder<WeightReportDataIsar, String?, QQueryOperations>
      businessIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'businessId');
    });
  }

  QueryBuilder<WeightReportDataIsar, DateTime?, QQueryOperations>
      createdAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'createdAt');
    });
  }

  QueryBuilder<WeightReportDataIsar, DateTime?, QQueryOperations>
      endDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'endDate');
    });
  }

  QueryBuilder<WeightReportDataIsar, String?, QQueryOperations>
      filterCriteriaProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'filterCriteria');
    });
  }

  QueryBuilder<WeightReportDataIsar, DateTime?, QQueryOperations>
      generatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'generatedAt');
    });
  }

  QueryBuilder<WeightReportDataIsar, double?, QQueryOperations>
      maximumWeightProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'maximumWeight');
    });
  }

  QueryBuilder<WeightReportDataIsar, double?, QQueryOperations>
      minimumWeightProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'minimumWeight');
    });
  }

  QueryBuilder<WeightReportDataIsar, String?, QQueryOperations>
      reportTypeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'reportType');
    });
  }

  QueryBuilder<WeightReportDataIsar, DateTime?, QQueryOperations>
      startDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'startDate');
    });
  }

  QueryBuilder<WeightReportDataIsar, String?, QQueryOperations>
      titleProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'title');
    });
  }

  QueryBuilder<WeightReportDataIsar, int?, QQueryOperations>
      totalWeighingsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'totalWeighings');
    });
  }

  QueryBuilder<WeightReportDataIsar, double?, QQueryOperations>
      totalWeightGainProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'totalWeightGain');
    });
  }

  QueryBuilder<WeightReportDataIsar, DateTime?, QQueryOperations>
      updatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'updatedAt');
    });
  }

  QueryBuilder<WeightReportDataIsar, List<DateTime>?, QQueryOperations>
      weightDatesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'weightDates');
    });
  }

  QueryBuilder<WeightReportDataIsar, List<int>?, QQueryOperations>
      weightRangeColorsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'weightRangeColors');
    });
  }

  QueryBuilder<WeightReportDataIsar, List<int>?, QQueryOperations>
      weightRangeCountsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'weightRangeCounts');
    });
  }

  QueryBuilder<WeightReportDataIsar, List<String>?, QQueryOperations>
      weightRangesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'weightRanges');
    });
  }

  QueryBuilder<WeightReportDataIsar, List<double>?, QQueryOperations>
      weightValuesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'weightValues');
    });
  }
}
