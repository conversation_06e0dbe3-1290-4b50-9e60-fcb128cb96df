import 'package:isar/isar.dart';
import '../models/event_isar.dart';
import '../models/event_type_isar.dart';
import '../../../services/database/isar_service.dart';

/// Pure reactive repository for Events module database operations
/// Following the cattle/weight module pattern: Stream-only, logic-free, simple CRUD
/// Maximum purity: no error handling, no logging - exceptions bubble up naturally
class EventsRepository {
  final IsarService _isarService;

  // Public constructor with explicit dependency injection
  EventsRepository(this._isarService);

  // Getter for Isar instance
  Isar get _isar => _isarService.isar;

  //=== REACTIVE EVENTS STREAMS ===//

  /// Watches all events with reactive updates
  /// The controller is responsible for all filtering and sorting
  Stream<List<EventIsar>> watchAllEvents() {
    return _isar.eventIsars.where().watch(fireImmediately: true);
  }

  /// Watches all event types with reactive updates
  /// The controller is responsible for all filtering and sorting
  Stream<List<EventTypeIsar>> watchAllEventTypes() {
    return _isar.eventTypeIsars.where().watch(fireImmediately: true);
  }

  //=== EVENTS CRUD ===//

  /// Save (add or update) an event using Isar's native upsert
  Future<void> saveEvent(EventIsar event) async {
    await _isar.writeTxn(() async {
      await _isar.eventIsars.put(event);
    });
  }

  /// Delete an event by its Isar ID
  Future<void> deleteEvent(int id) async {
    await _isar.writeTxn(() async {
      await _isar.eventIsars.delete(id);
    });
  }

  //=== EVENT TYPES CRUD ===//

  /// Save (add or update) an event type using Isar's native upsert
  Future<void> saveEventType(EventTypeIsar eventType) async {
    await _isar.writeTxn(() async {
      await _isar.eventTypeIsars.put(eventType);
    });
  }

  /// Delete an event type by its Isar ID
  Future<void> deleteEventType(int id) async {
    await _isar.writeTxn(() async {
      await _isar.eventTypeIsars.delete(id);
    });
  }
}
